# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at
  # https://dart-lang.github.io/linter/lints/index.html.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    # Error rules
    - avoid_print
    - avoid_unnecessary_containers
    - no_logic_in_create_state
    - prefer_const_constructors
    - prefer_const_constructors_in_immutables
    - prefer_const_declarations
    - prefer_const_literals_to_create_immutables
    - always_use_package_imports
    - avoid_type_to_string
    - literal_only_boolean_expressions
    - unnecessary_statements
    - use_key_in_widget_constructors
    # Style rules
    - always_declare_return_types
    - always_put_required_named_parameters_first
    - avoid_bool_literals_in_conditional_expressions
    - avoid_catches_without_on_clauses
    - avoid_classes_with_only_static_members
    - avoid_final_parameters
    - avoid_multiple_declarations_per_line
    - avoid_return_types_on_setters
    - camel_case_extensions
    - camel_case_types
    - directives_ordering
    - eol_at_end_of_file
    - flutter_style_todos
    - missing_whitespace_between_adjacent_strings
    - prefer_constructors_over_static_methods
    - prefer_single_quotes
    - prefer_typing_uninitialized_variables
    - sort_child_properties_last
    - sized_box_for_whitespace
    - recursive_getters
    - require_trailing_commas
    - unnecessary_brace_in_string_interps
    - unnecessary_final
    - unnecessary_getters_setters
    - unnecessary_late
    - unnecessary_new
    - unnecessary_null_aware_assignments
    - unnecessary_null_in_if_null_operators
    - unnecessary_overrides
    - unnecessary_this
    - unnecessary_string_escapes
    - unnecessary_string_interpolations
    - use_function_type_syntax_for_parameters
    - use_named_constants
    - use_rethrow_when_possible

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
