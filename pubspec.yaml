name: ams
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

  shimmer: ^3.0.0 # Shimmer Effect
  lottie: ^3.1.2 # Lottie Animation
  cached_network_image: ^3.3.1 # Network Images
  flutter_svg: ^2.0.10+1 # View SVG Images
  get: ^4.6.6 # State, Navigation & Dependencies Manager
  get_it: ^7.7.0 # DI - Service Locator - Cache
  intl: ^0.19.0 # Localization - Date-time manager
  go_router: ^13.2.1 # Manage App Routes
  logger: ^2.2.0  # Print Logs
  permission_handler: ^11.3.1 # Handle Permissions
  device_info_plus: ^10.1.0 # Get Device information
  dropdown_button2: ^2.3.9 # Dropdown Button
  bot_toast: ^4.1.3 # Toast Messages
  file_picker: ^8.0.5 #Pick Files
  image_picker: ^1.1.2 #Pick Images
  internet_connection_checker: ^1.0.0+1 # Check Internet
  url_launcher: ^6.3.0 # Launch URLs
  http: ^1.2.1 # API Calling
  shared_preferences: ^2.2.3 # Manage catch storage
  json_annotation: ^4.9.0 # Generate JSON
  external_path: ^1.0.3 # Manage External Storage
  path_provider: ^2.1.3 # Get Path

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  build_runner: ^2.4.11
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/icons/
    - assets/images/
    - assets/animation/
    - assets/data/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Arial
      fonts:
        - asset: assets/fonts/arial/Arial-Light.ttf
          weight: 200
        - asset: assets/fonts/arial/Arial-Regular.ttf
          weight: 400
        - asset: assets/fonts/arial/Arial-Medium.ttf
          weight: 600
        - asset: assets/fonts/arial/Arial-Bold.ttf
          weight: 800
    - family: DinRound
      fonts:
        - asset: assets/fonts/din_round/DINRoundPro-Light.ttf
          weight: 200
        - asset: assets/fonts/din_round/DINRoundPro.ttf
          weight: 400
        - asset: assets/fonts/din_round/DINRoundPro-Medium.ttf
          weight: 600
        - asset: assets/fonts/din_round/DINRoundPro-Bold.ttf
          weight: 800
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
