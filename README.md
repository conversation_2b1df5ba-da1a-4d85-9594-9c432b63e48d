# AMS

## Getting Started

- ## Flutter version required : `3.22.0`

- ## GetIt [API Service] - https://www.sandromaglione.com/articles/how_to_implement_dependecy_injection_in_flutter

- ## Controller - State using GetX [update()-Page Load or rX Object-Soft Load]

- ## Get.put or GetBuilder & Get.find if already put|builder

- ## Flavour - [https://dwirandyh.medium.com/create-build-flavor-in-flutter-application-ios-android-fb35a81a9fac]
- ## Download appropriate google service.json | Set Base Url in Main file

- ### Run command

    - debug prod `flutter run -t lib/main.dart --flavor prod`
    - debug dev `flutter run -t lib/main_dev.dart --flavor dev`

    - release prod `flutter run --release -t lib/main.dart --flavor prod`
    - release dev `flutter run --release -t lib/main_dev.dart --flavor dev`


- ### Run Test

    - `flutter test ./test/`


- ### Build command

    - Android

        - prod `flutter build apk -t lib/main.dart --flavor prod`
        - dev `flutter build apk -t lib/main_dev.dart --flavor dev`

    - appbundle

        - prod `flutter build appbundle --target-platform android-arm,android-arm64,android-x64 -t lib/main.dart --flavor prod`
        - dev `flutter build appbundle --target-platform android-arm,android-arm64,android-x64 -t lib/main_dev.dart --flavor dev`

    - iOS

        - prod `flutter build ios -t lib/main.dart --flavor prod`
        - dev `flutter build ios -t lib/main_dev.dart --flavor dev`


# Notes [For Separation Of Logic Must have controller for each page and also re usable on other pages if required and should be defined on page view model]
