import 'package:new_project_template/app_imports.dart';

class PermissionHelper {
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  static Future<bool> requestStoragePermission() async {
    final status = await Permission.storage.request();
    return status.isGranted;
  }

  static Future<bool> requestPhotosPermission() async {
    final status = await Permission.photos.request();
    return status.isGranted;
  }

  static Future<bool> requestLocationPermission() async {
    final status = await Permission.location.request();
    return status.isGranted;
  }

  static Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status.isGranted;
  }

  static Future<bool> requestNotificationPermission() async {
    final status = await Permission.notification.request();
    return status.isGranted;
  }

  static Future<bool> checkCameraPermission() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }

  static Future<bool> checkStoragePermission() async {
    final status = await Permission.storage.status;
    return status.isGranted;
  }

  static Future<bool> checkPhotosPermission() async {
    final status = await Permission.photos.status;
    return status.isGranted;
  }

  static Future<bool> checkLocationPermission() async {
    final status = await Permission.location.status;
    return status.isGranted;
  }

  static Future<bool> checkMicrophonePermission() async {
    final status = await Permission.microphone.status;
    return status.isGranted;
  }

  static Future<bool> checkNotificationPermission() async {
    final status = await Permission.notification.status;
    return status.isGranted;
  }

  static Future<void> openAppSettings() async {
    await openAppSettings();
  }

  static Future<bool> requestPermissionWithDialog(
    Permission permission,
    String title,
    String message,
  ) async {
    final status = await permission.status;
    
    if (status.isGranted) {
      return true;
    }
    
    if (status.isDenied) {
      final result = await Get.dialog<bool>(
        AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text(AppStringConstants.cancel),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text(AppStringConstants.ok),
            ),
          ],
        ),
      );
      
      if (result == true) {
        final newStatus = await permission.request();
        return newStatus.isGranted;
      }
    }
    
    if (status.isPermanentlyDenied) {
      final result = await Get.dialog<bool>(
        AlertDialog(
          title: Text(title),
          content: Text('$message\n\nPlease enable this permission in app settings.'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text(AppStringConstants.cancel),
            ),
            TextButton(
              onPressed: () {
                Get.back(result: true);
                openAppSettings();
              },
              child: const Text('Settings'),
            ),
          ],
        ),
      );
      
      return result == true;
    }
    
    return false;
  }
}
