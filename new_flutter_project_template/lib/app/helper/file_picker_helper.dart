import 'package:new_project_template/app_imports.dart';

class FilePickerHelper {
  static Future<File?> pickImage({ImageSource source = ImageSource.gallery}) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: source);
      
      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      AppHelper.logError('Error picking image', e);
      AppHelper.showToast('Error picking image', isError: true);
      return null;
    }
  }

  static Future<List<File>?> pickMultipleImages() async {
    try {
      final picker = ImagePicker();
      final pickedFiles = await picker.pickMultiImage();
      
      if (pickedFiles.isNotEmpty) {
        return pickedFiles.map((file) => File(file.path)).toList();
      }
      return null;
    } catch (e) {
      AppHelper.logError('Error picking multiple images', e);
      AppHelper.showToast('Error picking images', isError: true);
      return null;
    }
  }

  static Future<File?> pickVideo({ImageSource source = ImageSource.gallery}) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickVideo(source: source);
      
      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      AppHelper.logError('Error picking video', e);
      AppHelper.showToast('Error picking video', isError: true);
      return null;
    }
  }

  static Future<File?> pickFile({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
      );
      
      if (result != null && result.files.single.path != null) {
        return File(result.files.single.path!);
      }
      return null;
    } catch (e) {
      AppHelper.logError('Error picking file', e);
      AppHelper.showToast('Error picking file', isError: true);
      return null;
    }
  }

  static Future<List<File>?> pickMultipleFiles({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
        allowMultiple: true,
      );
      
      if (result != null) {
        return result.files
            .where((file) => file.path != null)
            .map((file) => File(file.path!))
            .toList();
      }
      return null;
    } catch (e) {
      AppHelper.logError('Error picking multiple files', e);
      AppHelper.showToast('Error picking files', isError: true);
      return null;
    }
  }

  static Future<File?> pickDocument() async {
    return await pickFile(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'doc', 'docx', 'txt'],
    );
  }

  static Future<File?> pickPdf() async {
    return await pickFile(
      type: FileType.custom,
      allowedExtensions: ['pdf'],
    );
  }

  static Future<void> showImagePickerDialog({
    required Function(File) onImageSelected,
  }) async {
    await Get.dialog(
      AlertDialog(
        title: const Text('Select Image'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () async {
                Get.back();
                final file = await pickImage(source: ImageSource.camera);
                if (file != null) {
                  onImageSelected(file);
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Gallery'),
              onTap: () async {
                Get.back();
                final file = await pickImage(source: ImageSource.gallery);
                if (file != null) {
                  onImageSelected(file);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  static String getFileExtension(String filePath) {
    return filePath.split('.').last.toLowerCase();
  }

  static String getFileName(String filePath) {
    return filePath.split('/').last;
  }

  static String getFileSize(File file) {
    final bytes = file.lengthSync();
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
