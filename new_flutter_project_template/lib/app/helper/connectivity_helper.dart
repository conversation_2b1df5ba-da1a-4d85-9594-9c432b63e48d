import 'package:new_project_template/app_imports.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

class ConnectivityHelper {
  static final InternetConnectionChecker _connectionChecker = InternetConnectionChecker();

  static Future<bool> hasInternetConnection() async {
    try {
      return await _connectionChecker.hasConnection;
    } catch (e) {
      AppHelper.logError('Error checking internet connection', e);
      return false;
    }
  }

  static Stream<InternetConnectionStatus> get connectionStream {
    return _connectionChecker.onStatusChange;
  }

  static void listenToConnectionChanges({
    required Function() onConnected,
    required Function() onDisconnected,
  }) {
    connectionStream.listen((status) {
      switch (status) {
        case InternetConnectionStatus.connected:
          onConnected();
          break;
        case InternetConnectionStatus.disconnected:
          onDisconnected();
          break;
      }
    });
  }

  static Future<bool> checkConnectionAndShowMessage() async {
    final hasConnection = await hasInternetConnection();
    if (!hasConnection) {
      AppHelper.showToast(AppStringConstants.noInternetConnection, isError: true);
    }
    return hasConnection;
  }
}
