import 'package:new_project_template/app_imports.dart';

class AppText extends StatelessWidget {
  final String text;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final String? fontFamily;
  final double? letterSpacing;
  final double? lineHeight;
  final TextDecoration? decoration;

  const AppText(
    this.text, {
    super.key,
    this.fontSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.fontFamily,
    this.letterSpacing,
    this.lineHeight,
    this.decoration,
  });

  const AppText.heading(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.fontFamily,
    this.letterSpacing,
    this.lineHeight,
    this.decoration,
  })  : fontSize = AppSizeConstants.fontSizeHeading,
        fontWeight = FontWeight.bold;

  const AppText.title(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.fontFamily,
    this.letterSpacing,
    this.lineHeight,
    this.decoration,
  })  : fontSize = AppSizeConstants.fontSizeTitle,
        fontWeight = FontWeight.w600;

  const AppText.subtitle(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.fontFamily,
    this.letterSpacing,
    this.lineHeight,
    this.decoration,
  })  : fontSize = AppSizeConstants.fontSizeLarge,
        fontWeight = FontWeight.w500;

  const AppText.body(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.fontFamily,
    this.letterSpacing,
    this.lineHeight,
    this.decoration,
  })  : fontSize = AppSizeConstants.fontSizeMedium,
        fontWeight = FontWeight.normal;

  const AppText.caption(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.fontFamily,
    this.letterSpacing,
    this.lineHeight,
    this.decoration,
  })  : fontSize = AppSizeConstants.fontSizeSmall,
        fontWeight = FontWeight.normal;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize ?? AppSizeConstants.fontSizeMedium,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color ?? AppColorConstants.colorBlack,
        fontFamily: fontFamily ?? AppAssetsConstants.defaultFont,
        letterSpacing: letterSpacing,
        height: lineHeight,
        decoration: decoration,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}
