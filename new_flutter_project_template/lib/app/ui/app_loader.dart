import 'package:new_project_template/app_imports.dart';

class AppLoader extends StatelessWidget {
  final Color? color;
  final double? size;
  final double? strokeWidth;

  const AppLoader({
    super.key,
    this.color,
    this.size,
    this.strokeWidth,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        height: size ?? 24,
        width: size ?? 24,
        child: CircularProgressIndicator(
          color: color ?? AppColorConstants.colorPrimary,
          strokeWidth: strokeWidth ?? 2,
        ),
      ),
    );
  }
}

class AppFullScreenLoader extends StatelessWidget {
  final String? message;
  final Color? backgroundColor;

  const AppFullScreenLoader({
    super.key,
    this.message,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor ?? AppColorConstants.colorBlack.withOpacity(0.5),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(AppSizeConstants.paddingLarge),
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const AppLoader(),
              if (message != null) ...[
                const SizedBox(height: AppSizeConstants.marginMedium),
                AppText.body(
                  message!,
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
