import 'package:new_project_template/app_imports.dart';

class HeightSliverDelegate extends SliverPersistentHeaderDelegate {
  final double height;
  final Widget child;

  HeightSliverDelegate({
    required this.height,
    required this.child,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox(
      height: height,
      child: child,
    );
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate != this;
  }
}
