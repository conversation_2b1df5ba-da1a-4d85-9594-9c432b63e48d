import 'package:new_project_template/app_imports.dart';

final GoRouter router = GoRouter(
  initialLocation: AppRoutes.splash,
  routes: [
    GoRoute(
      path: AppRoutes.splash,
      builder: (context, state) => const SplashPage(),
    ),
    GoRoute(
      path: AppRoutes.login,
      builder: (context, state) => const LoginPage(),
    ),
    GoRoute(
      path: AppRoutes.noInternet,
      builder: (context, state) => const NoInternetConnectionPage(),
    ),
    // Add more routes as needed
  ],
  errorBuilder: (context, state) => Scaffold(
    body: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColorConstants.colorRed,
          ),
          const SizedBox(height: AppSizeConstants.marginMedium),
          Text(
            'Page not found',
            style: const TextStyle(
              fontSize: AppSizeConstants.fontSizeTitle,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSizeConstants.marginSmall),
          Text(
            'The page you are looking for does not exist.',
            style: const TextStyle(
              fontSize: AppSizeConstants.fontSizeMedium,
              color: AppColorConstants.colorGrey,
            ),
          ),
          const SizedBox(height: AppSizeConstants.marginLarge),
          ElevatedButton(
            onPressed: () => context.go(AppRoutes.splash),
            child: const Text('Go Home'),
          ),
        ],
      ),
    ),
  ),
);
