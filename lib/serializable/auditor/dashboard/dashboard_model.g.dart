// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuditorDashboardModel _$AuditorDashboardModelFromJson(
        Map<String, dynamic> json) =>
    AuditorDashboardModel(
      inspectionConducted: (json['inspectionconducted'] as num?)?.toInt() ?? 0,
      inspectionPending: (json['inspectionpending'] as num?)?.toInt() ?? 0,
      inspectionRejected: (json['inspectionrejected'] as num?)?.toInt() ?? 0,
      inspectionApproved: (json['inspectionapproved'] as num?)?.toInt() ?? 0,
      approvalPendingWithAgency:
          (json['approvalpendingwithagency'] as num?)?.toInt() ?? 0,
      inspectionReportNonCompliance:
          (json['inspectionreportnoncompliance'] as num?)?.toInt() ?? 0,
      auditRequestCancelled:
          (json['auditrequestcancelled'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$AuditorDashboardModelToJson(
        AuditorDashboardModel instance) =>
    <String, dynamic>{
      'inspectionconducted': instance.inspectionConducted,
      'inspectionpending': instance.inspectionPending,
      'inspectionrejected': instance.inspectionRejected,
      'inspectionapproved': instance.inspectionApproved,
      'approvalpendingwithagency': instance.approvalPendingWithAgency,
      'inspectionreportnoncompliance': instance.inspectionReportNonCompliance,
      'auditrequestcancelled': instance.auditRequestCancelled,
    };
