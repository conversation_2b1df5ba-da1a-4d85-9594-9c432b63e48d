// To parse this JSON data, do
//
//     final auditorDashboardModel = auditorDashboardModelFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'dashboard_model.g.dart';

AuditorDashboardModel auditorDashboardModelFromJson(String str) => AuditorDashboardModel.fromJson(json.decode(str));

String auditorDashboardModelToJson(AuditorDashboardModel data) => json.encode(data.toJson());

@JsonSerializable()
class AuditorDashboardModel {
  @JsonKey(name: 'inspectionconducted')
  int inspectionConducted;
  @JsonKey(name: 'inspectionpending')
  int inspectionPending;
  @JsonKey(name: 'inspectionrejected')
  int inspectionRejected;
  @JsonKey(name: 'inspectionapproved')
  int inspectionApproved;
  @JsonKey(name: 'approvalpendingwithagency')
  int approvalPendingWithAgency;
  @JsonKey(name: 'inspectionreportnoncompliance')
  int inspectionReportNonCompliance;
  @JsonKey(name: 'auditrequestcancelled')
  int auditRequestCancelled;

  AuditorDashboardModel({
    this.inspectionConducted = 0,
    this.inspectionPending = 0,
    this.inspectionRejected = 0,
    this.inspectionApproved = 0,
    this.approvalPendingWithAgency = 0,
    this.inspectionReportNonCompliance = 0,
    this.auditRequestCancelled = 0,
  });

  factory AuditorDashboardModel.fromJson(Map<String, dynamic> json) => _$AuditorDashboardModelFromJson(json);

  Map<String, dynamic> toJson() => _$AuditorDashboardModelToJson(this);
}
