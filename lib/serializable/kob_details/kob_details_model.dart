// To parse this JSON data, do
//
//     final kobDetailsModel = kobDetailsModelFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'kob_details_model.g.dart';

List<KobDetailsModel> kobDetailsModelFromJson(String str) => List<KobDetailsModel>.from(json.decode(str).map((x) => KobDetailsModel.fromJson(x)));

String kobDetailsModelToJson(List<KobDetailsModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class KobDetailsModel {
  @JsonKey(name: 'kobmasterid')
  int kobMasterId;
  @JsonKey(name: 'kobmastername')
  String kobMasterName;
  @JsonKey(name: 'kobdetails')
  List<KobDetail> kobDetails;


  KobDetailsModel({
    this.kobMasterId = 0,
    this.kobMasterName = '',
    this.kobDetails = const <KobDetail>[],
  });

  factory KobDetailsModel.fromJson(Map<String, dynamic> json) => _$KobDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$KobDetailsModelToJson(this);
}

@JsonSerializable()
class KobDetail {
  @JsonKey(name: 'kobdetailid')
  int kobDetailId;
  @JsonKey(name: 'kobdetailname')
  String kobDetailName;
  @JsonKey(name: 'isSelected',includeToJson: false,includeFromJson: false)
  bool isSelected;

  KobDetail({
    this.kobDetailId = 0,
    this.kobDetailName = '',
    this.isSelected = false,
  });

  factory KobDetail.fromJson(Map<String, dynamic> json) => _$KobDetailFromJson(json);

  Map<String, dynamic> toJson() => _$KobDetailToJson(this);
}
