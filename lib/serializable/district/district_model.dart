

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'district_model.g.dart';

DistrictModel districtModelFromJson(String str) => DistrictModel.fromJson(json.decode(str));

String districtModelToJson(DistrictModel data) => json.encode(data.toJson());

@JsonSerializable()
class DistrictModel {
  @JsonKey(name: 'districtid')
  int? districtId;
  @JsonKey(name: 'districtname')
  String? districtName;
  @JsonKey(name: 'stateid')
  int? stateId;

  DistrictModel({
    this.districtId,
    this.districtName,
    this.stateId,
  });

  factory DistrictModel.fromJson(Map<String, dynamic> json) => _$DistrictModelFromJson(json);

  Map<String, dynamic> toJson() => _$DistrictModelToJson(this);
}
