import 'package:ams/app_imports.dart';
import 'package:json_annotation/json_annotation.dart';
part 'dashboard_model.g.dart';

SuperAdminDashboardModel superAdminDashboardModelFromJson(String str) => SuperAdminDashboardModel.fromJson(json.decode(str));

String superAdminDashboardModelToJson(SuperAdminDashboardModel data) => json.encode(data.toJson());

@JsonSerializable()
class SuperAdminDashboardModel {
  @JsonKey(name: 'applicationreceived')
  int applicationReceived;
  @JsonKey(name: 'agency')
  int agency;
  @Json<PERSON>ey(name: 'verify')
  int verify;
  @<PERSON>son<PERSON><PERSON>(name: 'rejected')
  int rejected;
  @Json<PERSON>ey(name: 'auditor')
  int auditor;
  @Json<PERSON><PERSON>(name: 'auditscompleted')
  int auditsCompleted;


  SuperAdminDashboardModel({
    this.applicationReceived = 0,
    this.agency = 0,
    this.verify = 0,
    this.rejected = 0,
    this.auditor = 0,
    this.auditsCompleted = 0,
  });

  factory SuperAdminDashboardModel.fromJson(Map<String, dynamic> json) => _$SuperAdminDashboardModelFromJson(json);

  Map<String, dynamic> toJson() => _$SuperAdminDashboardModelToJson(this);
}
