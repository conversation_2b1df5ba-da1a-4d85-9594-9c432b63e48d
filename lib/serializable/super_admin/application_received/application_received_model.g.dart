// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'application_received_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SuperAdminApplicationReceivedModel _$SuperAdminApplicationReceivedModelFromJson(
        Map<String, dynamic> json) =>
    SuperAdminApplicationReceivedModel(
      agencyId: (json['agencyid'] as num?)?.toInt(),
      agencyName: json['agencyname'] as String? ?? '',
      contactPersonName: json['contactpersonname'],
      stateName: json['statename'] as String? ?? '',
      districtName: json['districtname'] as String? ?? '',
      address: json['address'] as String? ?? '',
      pincode: json['pincode'],
      phone: json['phone'],
      mobile: json['mobile'] as String? ?? '',
      email: json['email'],
      certificateNumber: json['certificatenumber'],
      validUpTo: json['validupto'],
      recognitionNumber: json['recognitionnumber'],
      legalEntryStatus: json['legalentrystatus'],
      status: json['status'] as String? ?? '',
      nooFAuditors: (json['noofauditors'] as num?)?.toInt() ?? 0,
      nooFAudits: json['noofaudits'],
      remarks: json['remarks'] as String? ?? '',
      auditingScope: json['auditingscope'],
      geographicalArea: json['geographicalarea'],
      kob: json['kob'],
      docDetails: json['docDetails'],
    );

Map<String, dynamic> _$SuperAdminApplicationReceivedModelToJson(
        SuperAdminApplicationReceivedModel instance) =>
    <String, dynamic>{
      'agencyid': instance.agencyId,
      'agencyname': instance.agencyName,
      'contactpersonname': instance.contactPersonName,
      'statename': instance.stateName,
      'districtname': instance.districtName,
      'address': instance.address,
      'pincode': instance.pincode,
      'phone': instance.phone,
      'mobile': instance.mobile,
      'email': instance.email,
      'certificatenumber': instance.certificateNumber,
      'validupto': instance.validUpTo,
      'recognitionnumber': instance.recognitionNumber,
      'legalentrystatus': instance.legalEntryStatus,
      'status': instance.status,
      'noofauditors': instance.nooFAuditors,
      'noofaudits': instance.nooFAudits,
      'remarks': instance.remarks,
      'auditingscope': instance.auditingScope,
      'geographicalarea': instance.geographicalArea,
      'kob': instance.kob,
      'docDetails': instance.docDetails,
    };
