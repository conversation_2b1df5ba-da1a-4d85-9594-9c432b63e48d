// To parse this JSON data, do
//
//     final superAdminApplicationReceivedModel = superAdminApplicationReceivedModelFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'application_received_model.g.dart';

List<SuperAdminApplicationReceivedModel> superAdminApplicationReceivedModelFromJson(String str) =>
    List<SuperAdminApplicationReceivedModel>.from(json.decode(str).map((x) => SuperAdminApplicationReceivedModel.fromJson(x)));

String superAdminApplicationReceivedModelToJson(List<SuperAdminApplicationReceivedModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class SuperAdminApplicationReceivedModel {
  @J<PERSON><PERSON>ey(name: 'agencyid')
  int? agencyId;
  @JsonKey(name: 'agencyname')
  String agencyName;
  @JsonKey(name: 'contactpersonname')
  dynamic contactPersonName;
  @JsonKey(name: 'statename')
  String stateName;
  @JsonKey(name: 'districtname')
  String districtName;
  @JsonKey(name: 'address')
  String address;
  @JsonKey(name: 'pincode')
  dynamic pincode;
  @JsonKey(name: 'phone')
  dynamic phone;
  @JsonKey(name: 'mobile')
  String mobile;
  @JsonKey(name: 'email')
  dynamic email;
  @JsonKey(name: 'certificatenumber')
  dynamic certificateNumber;
  @JsonKey(name: 'validupto')
  dynamic validUpTo;
  @JsonKey(name: 'recognitionnumber')
  dynamic recognitionNumber;
  @JsonKey(name: 'legalentrystatus')
  dynamic legalEntryStatus;
  @JsonKey(name: 'status')
  String status;
  @JsonKey(name: 'noofauditors')
  int nooFAuditors;
  @JsonKey(name: 'noofaudits')
  dynamic nooFAudits;
  @JsonKey(name: 'remarks')
  String remarks;
  @JsonKey(name: 'auditingscope')
  dynamic auditingScope;
  @JsonKey(name: 'geographicalarea')
  dynamic geographicalArea;
  @JsonKey(name: 'kob')
  dynamic kob;
  @JsonKey(name: 'docDetails')
  dynamic docDetails;

  SuperAdminApplicationReceivedModel({
    this.agencyId,
    this.agencyName = '',
    this.contactPersonName,
    this.stateName = '',
    this.districtName = '',
    this.address = '',
    this.pincode,
    this.phone,
    this.mobile = '',
    this.email,
    this.certificateNumber,
    this.validUpTo,
    this.recognitionNumber,
    this.legalEntryStatus,
    this.status = '',
    this.nooFAuditors = 0,
    this.nooFAudits,
    this.remarks = '',
    this.auditingScope,
    this.geographicalArea,
    this.kob,
    this.docDetails,
  });

  factory SuperAdminApplicationReceivedModel.fromJson(Map<String, dynamic> json) =>
      _$SuperAdminApplicationReceivedModelFromJson(json);

  Map<String, dynamic> toJson() => _$SuperAdminApplicationReceivedModelToJson(this);
}
