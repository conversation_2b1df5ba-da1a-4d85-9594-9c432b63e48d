// To parse this JSON data, do
//
//     final superAdminAgencyDetailsModel = superAdminAgencyDetailsModelFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'agency_details_model.g.dart';

SuperAdminAgencyDetailsModel superAdminAgencyDetailsModelFromJson(String str) =>
    SuperAdminAgencyDetailsModel.fromJson(json.decode(str));

String superAdminAgencyDetailsModelToJson(SuperAdminAgencyDetailsModel data) => json.encode(data.toJson());

@JsonSerializable()
class SuperAdminAgencyDetailsModel {
  @JsonKey(name: 'agencyid')
  int? agencyId;
  @JsonKey(name: 'agencyname')
  String agencyName;
  @JsonKey(name: 'contactpersonname')
  String contactPersonName;
  @JsonKey(name: 'statename')
  String stateName;
  @JsonKey(name: 'districtname')
  String districtName;
  @Json<PERSON>ey(name: 'address')
  String address;
  @Json<PERSON>ey(name: 'pincode')
  String pincode;
  @JsonKey(name: 'phone')
  String phone;
  @JsonKey(name: 'mobile')
  String mobile;
  @JsonKey(name: 'email')
  String email;
  @JsonKey(name: 'certificatenumber')
  String certificateNumber;
  @JsonKey(name: 'validupto')
  DateTime? validUpTo;
  @JsonKey(name: 'recognitionnumber')
  String recognitionNumber;
  @JsonKey(name: 'legalentrystatus')
  String legalEntryStatus;
  @JsonKey(name: 'status')
  String status;
  @JsonKey(name: 'noofauditors')
  int nooFAuditors;
  @JsonKey(name: 'noofaudits')
  int nooFAudits;
  @JsonKey(name: 'remarks')
  String remarks;
  @JsonKey(name: 'auditingscope')
  dynamic auditingScope;
  @JsonKey(name: 'geographicalarea')
  String geographicalArea;
  @JsonKey(name: 'kob')
  List<Kob> kob;
  @JsonKey(name: 'docDetails')
  List<DocDetail> docDetails;

  SuperAdminAgencyDetailsModel({
    this.agencyId,
    this.agencyName = '',
    this.contactPersonName = '',
    this.stateName = '',
    this.districtName = '',
    this.address = '',
    this.pincode = '',
    this.phone = '',
    this.mobile = '',
    this.email = '',
    this.certificateNumber = '',
    this.validUpTo,
    this.recognitionNumber = '',
    this.legalEntryStatus = '',
    this.status = '',
    this.nooFAuditors = 0,
    this.nooFAudits = 0,
    this.remarks = '',
    this.auditingScope,
    this.geographicalArea = '',
    this.kob = const <Kob>[],
    this.docDetails = const <DocDetail>[],
  });

  factory SuperAdminAgencyDetailsModel.fromJson(Map<String, dynamic> json) => _$SuperAdminAgencyDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$SuperAdminAgencyDetailsModelToJson(this);
}

@JsonSerializable()
class DocDetail {
  @JsonKey(name: 'id')
  int id;
  @JsonKey(name: 'filename')
  String filename;

  DocDetail({
    this.id = 0,
    this.filename = '',
  });

  factory DocDetail.fromJson(Map<String, dynamic> json) => _$DocDetailFromJson(json);

  Map<String, dynamic> toJson() => _$DocDetailToJson(this);
}

@JsonSerializable()
class Kob {
  @JsonKey(name: 'kobid')
  int kobId;
  @JsonKey(name: 'kobmasterid')
  int kobMasterId;
  @JsonKey(name: 'kobmastername')
  String kobMasterName;
  @JsonKey(name: 'kobname')
  String kobName;

  Kob({
    this.kobId = 0,
    this.kobMasterId = 0,
    this.kobMasterName = '',
    this.kobName = '',
  });

  factory Kob.fromJson(Map<String, dynamic> json) => _$KobFromJson(json);

  Map<String, dynamic> toJson() => _$KobToJson(this);
}
