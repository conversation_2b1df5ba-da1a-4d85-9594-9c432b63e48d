// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agency_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SuperAdminAgencyDetailsModel _$SuperAdminAgencyDetailsModelFromJson(
        Map<String, dynamic> json) =>
    SuperAdminAgencyDetailsModel(
      agencyId: (json['agencyid'] as num?)?.toInt(),
      agencyName: json['agencyname'] as String? ?? '',
      contactPersonName: json['contactpersonname'] as String? ?? '',
      stateName: json['statename'] as String? ?? '',
      districtName: json['districtname'] as String? ?? '',
      address: json['address'] as String? ?? '',
      pincode: json['pincode'] as String? ?? '',
      phone: json['phone'] as String? ?? '',
      mobile: json['mobile'] as String? ?? '',
      email: json['email'] as String? ?? '',
      certificateNumber: json['certificatenumber'] as String? ?? '',
      validUpTo: json['validupto'] == null
          ? null
          : DateTime.parse(json['validupto'] as String),
      recognitionNumber: json['recognitionnumber'] as String? ?? '',
      legalEntryStatus: json['legalentrystatus'] as String? ?? '',
      status: json['status'] as String? ?? '',
      nooFAuditors: (json['noofauditors'] as num?)?.toInt() ?? 0,
      nooFAudits: (json['noofaudits'] as num?)?.toInt() ?? 0,
      remarks: json['remarks'] as String? ?? '',
      auditingScope: json['auditingscope'],
      geographicalArea: json['geographicalarea'] as String? ?? '',
      kob: (json['kob'] as List<dynamic>?)
              ?.map((e) => Kob.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const <Kob>[],
      docDetails: (json['docDetails'] as List<dynamic>?)
              ?.map((e) => DocDetail.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const <DocDetail>[],
    );

Map<String, dynamic> _$SuperAdminAgencyDetailsModelToJson(
        SuperAdminAgencyDetailsModel instance) =>
    <String, dynamic>{
      'agencyid': instance.agencyId,
      'agencyname': instance.agencyName,
      'contactpersonname': instance.contactPersonName,
      'statename': instance.stateName,
      'districtname': instance.districtName,
      'address': instance.address,
      'pincode': instance.pincode,
      'phone': instance.phone,
      'mobile': instance.mobile,
      'email': instance.email,
      'certificatenumber': instance.certificateNumber,
      'validupto': instance.validUpTo?.toIso8601String(),
      'recognitionnumber': instance.recognitionNumber,
      'legalentrystatus': instance.legalEntryStatus,
      'status': instance.status,
      'noofauditors': instance.nooFAuditors,
      'noofaudits': instance.nooFAudits,
      'remarks': instance.remarks,
      'auditingscope': instance.auditingScope,
      'geographicalarea': instance.geographicalArea,
      'kob': instance.kob,
      'docDetails': instance.docDetails,
    };

DocDetail _$DocDetailFromJson(Map<String, dynamic> json) => DocDetail(
      id: (json['id'] as num?)?.toInt() ?? 0,
      filename: json['filename'] as String? ?? '',
    );

Map<String, dynamic> _$DocDetailToJson(DocDetail instance) => <String, dynamic>{
      'id': instance.id,
      'filename': instance.filename,
    };

Kob _$KobFromJson(Map<String, dynamic> json) => Kob(
      kobId: (json['kobid'] as num?)?.toInt() ?? 0,
      kobMasterId: (json['kobmasterid'] as num?)?.toInt() ?? 0,
      kobMasterName: json['kobmastername'] as String? ?? '',
      kobName: json['kobname'] as String? ?? '',
    );

Map<String, dynamic> _$KobToJson(Kob instance) => <String, dynamic>{
      'kobid': instance.kobId,
      'kobmasterid': instance.kobMasterId,
      'kobmastername': instance.kobMasterName,
      'kobname': instance.kobName,
    };
