// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'empanelled_agencies_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SuperAdminEmpanelledAgenciesModel _$SuperAdminEmpanelledAgenciesModelFromJson(
  Map<String, dynamic> json,
) =>
    SuperAdminEmpanelledAgenciesModel(
      agencyId: (json['agencyid'] as num?)?.toInt(),
      agencyName: json['agencyname'] as String? ?? '',
      contactPersonName: json['contactpersonname'] as String? ?? '',
      stateName: json['statename'] as String? ?? '',
      districtName: json['districtname'] as String? ?? '',
      address: json['address'] as String? ?? '',
      pincode: json['pincode'] as String? ?? '',
      phone: json['phone'] as String? ?? '',
      mobile: json['mobile'] as String? ?? '',
      email: json['email'] as String? ?? '',
      certificateNumber: json['certificatenumber'] as String? ?? '',
      validUpTo: json['validupto'],
      recognitionNumber: json['recognitionnumber'] as String? ?? '',
      legalEntryStatus: json['legalentrystatus'] as String? ?? '',
      status: json['status'] as String? ?? '',
      nooFAuditors: (json['noofauditors'] as num?)?.toInt() ?? 0,
      nooFAudits: (json['noofaudits'] as num?)?.toInt() ?? 0,
      remarks: json['remarks'] as String? ?? '',
      auditingScope: json['auditingscope'] as String? ?? '',
      geographicalArea: json['geographicalarea'] as String? ?? '',
      kob: json['kob'] as String? ?? '',
      docDetails: json['docDetails'] as String? ?? '',
      activeFlag: json['activeflag'] as String? ?? '',
    );

Map<String, dynamic> _$SuperAdminEmpanelledAgenciesModelToJson(
  SuperAdminEmpanelledAgenciesModel instance,
) =>
    <String, dynamic>{
      'agencyid': instance.agencyId,
      'agencyname': instance.agencyName,
      'contactpersonname': instance.contactPersonName,
      'statename': instance.stateName,
      'districtname': instance.districtName,
      'address': instance.address,
      'pincode': instance.pincode,
      'phone': instance.phone,
      'mobile': instance.mobile,
      'email': instance.email,
      'certificatenumber': instance.certificateNumber,
      'validupto': instance.validUpTo,
      'recognitionnumber': instance.recognitionNumber,
      'legalentrystatus': instance.legalEntryStatus,
      'status': instance.status,
      'noofauditors': instance.nooFAuditors,
      'noofaudits': instance.nooFAudits,
      'remarks': instance.remarks,
      'auditingscope': instance.auditingScope,
      'geographicalarea': instance.geographicalArea,
      'kob': instance.kob,
      'docDetails': instance.docDetails,
      'activeflag': instance.activeFlag,
    };
