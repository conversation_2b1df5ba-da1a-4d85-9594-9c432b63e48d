import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'empanelled_agencies_model.g.dart';

List<SuperAdminEmpanelledAgenciesModel> superAdminEmpanelledAgenciesModelFromJson(String str) =>
    List<SuperAdminEmpanelledAgenciesModel>.from(json.decode(str).map((x) => SuperAdminEmpanelledAgenciesModel.fromJson(x)));

String superAdminEmpanelledAgenciesModelToJson(List<SuperAdminEmpanelledAgenciesModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class SuperAdminEmpanelledAgenciesModel {
  @JsonKey(name: 'agencyid')
  int? agencyId;
  @JsonKey(name: 'agencyname')
  String agencyName;
  @JsonKey(name: 'contactpersonname')
  String contactPersonName;
  @JsonKey(name: 'statename')
  String stateName;
  @Json<PERSON>ey(name: 'districtname')
  String districtName;
  @JsonKey(name: 'address')
  String address;
  @JsonKey(name: 'pincode')
  String pincode;
  @JsonKey(name: 'phone')
  String phone;
  @JsonKey(name: 'mobile')
  String mobile;
  @JsonKey(name: 'email')
  String email;
  @JsonKey(name: 'certificatenumber')
  String certificateNumber;
  @JsonKey(name: 'validupto')
  dynamic validUpTo;
  @JsonKey(name: 'recognitionnumber')
  String recognitionNumber;
  @JsonKey(name: 'legalentrystatus')
  String legalEntryStatus;
  @JsonKey(name: 'status')
  String status;
  @JsonKey(name: 'noofauditors')
  int nooFAuditors;
  @JsonKey(name: 'noofaudits')
  int nooFAudits;
  @JsonKey(name: 'remarks')
  String remarks;
  @JsonKey(name: 'auditingscope')
  String auditingScope;
  @JsonKey(name: 'geographicalarea')
  String geographicalArea;
  @JsonKey(name: 'kob')
  String kob;
  @JsonKey(name: 'docDetails')
  String docDetails;
  @JsonKey(name: 'activeflag')
  String activeFlag;

  SuperAdminEmpanelledAgenciesModel({
    this.agencyId,
    this.agencyName = '',
    this.contactPersonName = '',
    this.stateName = '',
    this.districtName = '',
    this.address = '',
    this.pincode = '',
    this.phone = '',
    this.mobile = '',
    this.email = '',
    this.certificateNumber = '',
    this.validUpTo,
    this.recognitionNumber = '',
    this.legalEntryStatus = '',
    this.status = '',
    this.nooFAuditors = 0,
    this.nooFAudits = 0,
    this.remarks = '',
    this.auditingScope = '',
    this.geographicalArea = '',
    this.kob = '',
    this.docDetails = '',
    this.activeFlag = '',
  });

  factory SuperAdminEmpanelledAgenciesModel.fromJson(Map<String, dynamic> json) =>
      _$SuperAdminEmpanelledAgenciesModelFromJson(json);

  Map<String, dynamic> toJson() => _$SuperAdminEmpanelledAgenciesModelToJson(this);
}
