
import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'enlisted_auditors_model.g.dart';

List<SuperAdminEnlistedAuditorsModel> superAdminEnlistedAuditorsModelFromJson(String str) => List<SuperAdminEnlistedAuditorsModel>.from(json.decode(str).map((x) => SuperAdminEnlistedAuditorsModel.fromJson(x)));

String superAdminEnlistedAuditorsModelToJson(List<SuperAdminEnlistedAuditorsModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class SuperAdminEnlistedAuditorsModel {
  @Json<PERSON>ey(name: 'auditorregistrationid')
  int? auditorRegistrationId;
  @JsonKey(name: 'address')
  String address;
  @JsonKey(name: 'auditorname')
  String auditorName;
  @Json<PERSON>ey(name: 'agencyname')
  String agencyName;
  @JsonKey(name: 'district')
  int district;
  @Json<PERSON>ey(name: 'email')
  String email;
  @Json<PERSON>ey(name: 'expertise')
  String expertise;
  @JsonKey(name: 'fathername')
  String fatherName;
  @JsonKey(name: 'mobile')
  String mobile;
  @JsonKey(name: 'pincode')
  String pincode;
  @JsonKey(name: 'state')
  int state;
  @JsonKey(name: 'noofaudits')
  int nooFAudits;
  @JsonKey(name: 'status')
  String status;
  @JsonKey(name: 'districtname')
  String districtName;
  @JsonKey(name: 'statename')
  String stateName;
  @JsonKey(name: 'leadauditorfile')
  String leadAuditorFile;
  @JsonKey(name: 'qualificationfile')
  String qualificationFile;
  @JsonKey(name: 'auditorlogfile')
  String auditorLogFile;
  @JsonKey(name: 'sectorspecificfile')
  String sectorsPecificFile;

  SuperAdminEnlistedAuditorsModel({
    this.auditorRegistrationId,
    this.address = '',
    this.auditorName = '',
    this.agencyName = '',
    this.district = 0,
    this.email = '',
    this.expertise = '',
    this.fatherName = '',
    this.mobile = '',
    this.pincode = '',
    this.state = 0,
    this.nooFAudits = 0,
    this.status = '',
    this.districtName = '',
    this.stateName = '',
    this.leadAuditorFile = '',
    this.qualificationFile = '',
    this.auditorLogFile = '',
    this.sectorsPecificFile = '',
  });

  factory SuperAdminEnlistedAuditorsModel.fromJson(Map<String, dynamic> json) => _$SuperAdminEnlistedAuditorsModelFromJson(json);

  Map<String, dynamic> toJson() => _$SuperAdminEnlistedAuditorsModelToJson(this);
}
