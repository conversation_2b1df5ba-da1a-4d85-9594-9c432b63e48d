// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enlisted_auditors_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SuperAdminEnlistedAuditorsModel _$SuperAdminEnlistedAuditorsModelFromJson(
        Map<String, dynamic> json) =>
    SuperAdminEnlistedAuditorsModel(
      auditorRegistrationId: (json['auditorregistrationid'] as num?)?.toInt(),
      address: json['address'] as String? ?? '',
      auditorName: json['auditorname'] as String? ?? '',
      agencyName: json['agencyname'] as String? ?? '',
      district: (json['district'] as num?)?.toInt() ?? 0,
      email: json['email'] as String? ?? '',
      expertise: json['expertise'] as String? ?? '',
      fatherName: json['fathername'] as String? ?? '',
      mobile: json['mobile'] as String? ?? '',
      pincode: json['pincode'] as String? ?? '',
      state: (json['state'] as num?)?.toInt() ?? 0,
      nooFAudits: (json['noofaudits'] as num?)?.toInt() ?? 0,
      status: json['status'] as String? ?? '',
      districtName: json['districtname'] as String? ?? '',
      stateName: json['statename'] as String? ?? '',
      leadAuditorFile: json['leadauditorfile'] as String? ?? '',
      qualificationFile: json['qualificationfile'] as String? ?? '',
      auditorLogFile: json['auditorlogfile'] as String? ?? '',
      sectorsPecificFile: json['sectorspecificfile'] as String? ?? '',
    );

Map<String, dynamic> _$SuperAdminEnlistedAuditorsModelToJson(
        SuperAdminEnlistedAuditorsModel instance) =>
    <String, dynamic>{
      'auditorregistrationid': instance.auditorRegistrationId,
      'address': instance.address,
      'auditorname': instance.auditorName,
      'agencyname': instance.agencyName,
      'district': instance.district,
      'email': instance.email,
      'expertise': instance.expertise,
      'fathername': instance.fatherName,
      'mobile': instance.mobile,
      'pincode': instance.pincode,
      'state': instance.state,
      'noofaudits': instance.nooFAudits,
      'status': instance.status,
      'districtname': instance.districtName,
      'statename': instance.stateName,
      'leadauditorfile': instance.leadAuditorFile,
      'qualificationfile': instance.qualificationFile,
      'auditorlogfile': instance.auditorLogFile,
      'sectorspecificfile': instance.sectorsPecificFile,
    };
