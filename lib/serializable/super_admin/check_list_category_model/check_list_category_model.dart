// To parse this JSON data, do
//
//     final checkListCategoryModel = checkListCategoryModelFromJson(jsonString);
import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'check_list_category_model.g.dart';

List<CheckListCategoryModel> checkListCategoryModelFromJson(String str) =>
    List<CheckListCategoryModel>.from(json.decode(str).map((x) => CheckListCategoryModel.fromJson(x)));

String checkListCategoryModelToJson(List<CheckListCategoryModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class CheckListCategoryModel {
  int? id;
  @JsonKey(name: 'categoryname')
  String? categoryName;

  CheckListCategoryModel({
    this.id,
    this.categoryName,
  });

  factory CheckListCategoryModel.fromJson(Map<String, dynamic> json) => _$CheckListCategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CheckListCategoryModelToJson(this);
}
