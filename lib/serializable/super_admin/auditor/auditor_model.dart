// To parse this JSON data, do
//
//     final superAdminAuditorModel = superAdminAuditorModelFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'auditor_model.g.dart';

List<SuperAdminAuditorModel> superAdminAuditorModelFromJson(String str) => List<SuperAdminAuditorModel>.from(json.decode(str).map((x) => SuperAdminAuditorModel.fromJson(x)));

String superAdminAuditorModelToJson(List<SuperAdminAuditorModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class SuperAdminAuditorModel {
  @JsonKey(name: 'auditorregistrationid')
  int? auditorRegistrationId;
  @JsonKey(name: 'address')
  String address;
  @JsonKey(name: 'auditorname')
  String auditorName;
  @JsonKey(name: 'agencyname')
  String agencyName;
  @Json<PERSON>ey(name: 'district')
  int district;
  @JsonKey(name: 'email')
  String email;
  @JsonKey(name: 'expertise')
  String expertise;
  @JsonKey(name: 'fathername')
  String fatherName;
  @JsonKey(name: 'mobile')
  String mobile;
  @JsonKey(name: 'pincode')
  String pincode;
  @JsonKey(name: 'state')
  int state;
  @JsonKey(name: 'noofaudits')
  int nooFAudits;
  @JsonKey(name: 'status')
  String status;
  @JsonKey(name: 'districtname')
  String districtName;
  @JsonKey(name: 'statename')
  String stateName;
  @JsonKey(name: 'leadauditorfile')
  String leadAuditorFile;
  @JsonKey(name: 'qualificationfile')
  String qualificationFile;
  @JsonKey(name: 'auditorlogfile')
  String auditorLogFile;
  @JsonKey(name: 'sectorspecificfile')
  String sectorSpecificFile;

  SuperAdminAuditorModel({
    this.auditorRegistrationId = 0,
    this.address = '',
    this.auditorName = '',
    this.agencyName = '',
    this.district = 0,
    this.email = '',
    this.expertise = '',
    this.fatherName = '',
    this.mobile = '',
    this.pincode = '',
    this.state = 0,
    this.nooFAudits = 0,
    this.status = '',
    this.districtName = '',
    this.stateName = '',
    this.leadAuditorFile = '',
    this.qualificationFile = '',
    this.auditorLogFile = '',
    this.sectorSpecificFile = '',
  });

  factory SuperAdminAuditorModel.fromJson(Map<String, dynamic> json) => _$SuperAdminAuditorModelFromJson(json);

  Map<String, dynamic> toJson() => _$SuperAdminAuditorModelToJson(this);
}
