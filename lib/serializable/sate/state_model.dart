// To parse this JSON data, do
//
//     final stateListModel = stateListModelFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'state_model.g.dart';

StateModel stateModelFromJson(String str) => StateModel.fromJson(json.decode(str));

String stateModelToJson(StateModel data) => json.encode(data.toJson());

@JsonSerializable()
class StateModel {
  @JsonKey(name: 'stateid')
  int? stateId;
  @JsonKey(name: 'statename')
  String? stateName;

  StateModel({
    this.stateId,
    this.stateName,
  });

  factory StateModel.fromJson(Map<String, dynamic> json) => _$StateModelFromJson(json);

  Map<String, dynamic> toJson() => _$StateModelToJson(this);
}
