import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'agency_approved_audits_model.g.dart';

List<AgencyApprovedAuditsModel> agencyApprovedAuditsModelFromJson(String str) =>
    List<AgencyApprovedAuditsModel>.from(json.decode(str).map((x) => AgencyApprovedAuditsModel.fromJson(x)));

String agencyApprovedAuditsModelToJson(List<AgencyApprovedAuditsModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class AgencyApprovedAuditsModel {
  @JsonKey(name: 'inspectionreportid')
  int inspectionreportid;
  @JsonKey(name: 'inspectionrequestid')
  int inspectionrequestid;
  @JsonKey(name: 'reportno')
  String reportno;
  @JsonKey(name: 'licenseno')
  String licenseno;
  @JsonKey(name: 'fboname')
  String fboname;
  @JsonKey(name: 'address')
  dynamic address;
  @JsonKey(name: 'state')
  String state;
  @Json<PERSON>ey(name: 'district')
  String district;
  @JsonKey(name: 'agencyname')
  String agencyname;
  @JsonKey(name: 'auditorname')
  String auditorname;
  @JsonKey(name: 'contactperson')
  dynamic contactperson;
  @JsonKey(name: 'auditstartdate')
  String auditstartdate;
  @JsonKey(name: 'auditenddate')
  String auditenddate;
  @JsonKey(name: 'auditsubmissiondate')
  String auditsubmissiondate;
  @JsonKey(name: 'dateofrequest')
  DateTime? dateofrequest;
  @JsonKey(name: 'rating')
  dynamic rating;
  @JsonKey(name: 'score')
  int score;
  @JsonKey(name: 'maxscore')
  int maxscore;
  @JsonKey(name: 'kob')
  String kob;

  AgencyApprovedAuditsModel({
    this.inspectionreportid = 0,
    this.inspectionrequestid = 0,
    this.reportno = '',
    this.licenseno = '',
    this.fboname = '',
    this.address = '',
    this.state = '',
    this.district = '',
    this.agencyname = '',
    this.auditorname = '',
    this.contactperson = '',
    this.auditstartdate = '',
    this.auditenddate = '',
    this.auditsubmissiondate = '',
    this.dateofrequest,
    this.rating = '',
    this.score = 0,
    this.maxscore = 0,
    this.kob = '',
  });

  factory AgencyApprovedAuditsModel.fromJson(Map<String, dynamic> json) => _$AgencyApprovedAuditsModelFromJson(json);

  Map<String, dynamic> toJson() => _$AgencyApprovedAuditsModelToJson(this);
}
