// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agency_approved_audits_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AgencyApprovedAuditsModel _$AgencyApprovedAuditsModelFromJson(
  Map<String, dynamic> json,
) =>
    AgencyApprovedAuditsModel(
      inspectionreportid: (json['inspectionreportid'] as num?)?.toInt() ?? 0,
      inspectionrequestid: (json['inspectionrequestid'] as num?)?.toInt() ?? 0,
      reportno: json['reportno'] as String? ?? '',
      licenseno: json['licenseno'] as String? ?? '',
      fboname: json['fboname'] as String? ?? '',
      address: json['address'] ?? '',
      state: json['state'] as String? ?? '',
      district: json['district'] as String? ?? '',
      agencyname: json['agencyname'] as String? ?? '',
      auditorname: json['auditorname'] as String? ?? '',
      contactperson: json['contactperson'] ?? '',
      auditstartdate: json['auditstartdate'] as String? ?? '',
      auditenddate: json['auditenddate'] as String? ?? '',
      auditsubmissiondate: json['auditsubmissiondate'] as String? ?? '',
      dateofrequest: json['dateofrequest'] == null ? null : DateTime.parse(json['dateofrequest'] as String),
      rating: json['rating'] ?? '',
      score: (json['score'] as num?)?.toInt() ?? 0,
      maxscore: (json['maxscore'] as num?)?.toInt() ?? 0,
      kob: json['kob'] as String? ?? '',
    );

Map<String, dynamic> _$AgencyApprovedAuditsModelToJson(
  AgencyApprovedAuditsModel instance,
) =>
    <String, dynamic>{
      'inspectionreportid': instance.inspectionreportid,
      'inspectionrequestid': instance.inspectionrequestid,
      'reportno': instance.reportno,
      'licenseno': instance.licenseno,
      'fboname': instance.fboname,
      'address': instance.address,
      'state': instance.state,
      'district': instance.district,
      'agencyname': instance.agencyname,
      'auditorname': instance.auditorname,
      'contactperson': instance.contactperson,
      'auditstartdate': instance.auditstartdate,
      'auditenddate': instance.auditenddate,
      'auditsubmissiondate': instance.auditsubmissiondate,
      'dateofrequest': instance.dateofrequest?.toIso8601String(),
      'rating': instance.rating,
      'score': instance.score,
      'maxscore': instance.maxscore,
      'kob': instance.kob,
    };
