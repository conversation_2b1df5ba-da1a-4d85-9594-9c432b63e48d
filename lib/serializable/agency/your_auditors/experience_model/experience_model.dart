// To parse this JSON data, do
//
//     final experienceModel = experienceModelFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'experience_model.g.dart';

List<ExperienceModel> experienceModelFromJson(String str) => List<ExperienceModel>.from(json.decode(str).map((x) => ExperienceModel.fromJson(x)));

String experienceModelToJson(List<ExperienceModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class ExperienceModel {
  @JsonKey(name: 'auditorexperienceid')
  int? auditorExperienceId;
  @Json<PERSON>ey(name: 'auditorregistrationid')
  int? auditorRegistrationId;
  @JsonKey(name: 'expmonth')
  dynamic expMonth;
  @JsonKey(name: 'fromexp')
  DateTime? fromExp;
  @J<PERSON><PERSON><PERSON>(name: 'orgname')
  String? orgName;
  @Json<PERSON>ey(name: 'toexp')
  DateTime? toExp;
  @Json<PERSON>ey(name: 'designation')
  String? designation;

  ExperienceModel({
    this.auditorExperienceId,
    this.auditorRegistrationId,
    this.expMonth,
    this.fromExp,
    this.orgName,
    this.toExp,
    this.designation,
  });

  factory ExperienceModel.fromJson(Map<String, dynamic> json) => _$ExperienceModelFromJson(json);

  Map<String, dynamic> toJson() => _$ExperienceModelToJson(this);
}
