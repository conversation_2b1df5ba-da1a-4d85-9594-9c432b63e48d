// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'experience_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ExperienceModel _$ExperienceModelFromJson(Map<String, dynamic> json) =>
    ExperienceModel(
      auditorExperienceId: (json['auditorexperienceid'] as num?)?.toInt(),
      auditorRegistrationId: (json['auditorregistrationid'] as num?)?.toInt(),
      expMonth: json['expmonth'],
      fromExp: json['fromexp'] == null
          ? null
          : DateTime.parse(json['fromexp'] as String),
      orgName: json['orgname'] as String?,
      toExp: json['toexp'] == null
          ? null
          : DateTime.parse(json['toexp'] as String),
      designation: json['designation'] as String?,
    );

Map<String, dynamic> _$ExperienceModelToJson(ExperienceModel instance) =>
    <String, dynamic>{
      'auditorexperienceid': instance.auditorExperienceId,
      'auditorregistrationid': instance.auditorRegistrationId,
      'expmonth': instance.expMonth,
      'fromexp': instance.fromExp?.toIso8601String(),
      'orgname': instance.orgName,
      'toexp': instance.toExp?.toIso8601String(),
      'designation': instance.designation,
    };
