// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'your_auditors_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AgencyYourAuditorsModel _$AgencyYourAuditorsModelFromJson(
        Map<String, dynamic> json) =>
    AgencyYourAuditorsModel(
      auditorregistrationid:
          (json['auditorregistrationid'] as num?)?.toInt() ?? 0,
      address: json['address'] as String? ?? '',
      auditorname: json['auditorname'] as String? ?? '',
      agencyname: json['agencyname'] as String? ?? '',
      district: (json['district'] as num?)?.toInt() ?? 0,
      email: json['email'] as String? ?? '',
      expertise: json['expertise'] as String? ?? '',
      fathername: json['fathername'] as String? ?? '',
      mobile: json['mobile'] as String? ?? '',
      pincode: json['pincode'] as String? ?? '',
      state: (json['state'] as num?)?.toInt() ?? 0,
      noofaudits: (json['noofaudits'] as num?)?.toInt() ?? 0,
      status: json['status'] as String? ?? '',
      districtname: json['districtname'] as String? ?? '',
      statename: json['statename'] as String? ?? '',
      leadauditorfile: json['leadauditorfile'] as String? ?? '',
      qualificationfile: json['qualificationfile'] as String? ?? '',
      auditorlogfile: json['auditorlogfile'] as String? ?? '',
      sectorspecificfile: json['sectorspecificfile'] as String? ?? '',
    );

Map<String, dynamic> _$AgencyYourAuditorsModelToJson(
        AgencyYourAuditorsModel instance) =>
    <String, dynamic>{
      'auditorregistrationid': instance.auditorregistrationid,
      'address': instance.address,
      'auditorname': instance.auditorname,
      'agencyname': instance.agencyname,
      'district': instance.district,
      'email': instance.email,
      'expertise': instance.expertise,
      'fathername': instance.fathername,
      'mobile': instance.mobile,
      'pincode': instance.pincode,
      'state': instance.state,
      'noofaudits': instance.noofaudits,
      'status': instance.status,
      'districtname': instance.districtname,
      'statename': instance.statename,
      'leadauditorfile': instance.leadauditorfile,
      'qualificationfile': instance.qualificationfile,
      'auditorlogfile': instance.auditorlogfile,
      'sectorspecificfile': instance.sectorspecificfile,
    };

YourAuditorsQualificationModel _$YourAuditorsQualificationModelFromJson(
        Map<String, dynamic> json) =>
    YourAuditorsQualificationModel(
      auditorqualificationid:
          (json['auditorqualificationid'] as num?)?.toInt() ?? 0,
      auditorregistrationid:
          (json['auditorregistrationid'] as num?)?.toInt() ?? 0,
      passingyear: json['passingyear'] == null
          ? null
          : DateTime.parse(json['passingyear'] as String),
      percentage: json['percentage'] as String? ?? '',
      qulname: json['qulname'] as String? ?? '',
      universityboard: json['universityboard'] as String? ?? '',
      subject: json['subject'] as String? ?? '',
      steps: json['steps'] as String? ?? '',
      auditorRegistration: json['auditorRegistration'] as String? ?? '',
    );

Map<String, dynamic> _$YourAuditorsQualificationModelToJson(
        YourAuditorsQualificationModel instance) =>
    <String, dynamic>{
      'auditorqualificationid': instance.auditorqualificationid,
      'auditorregistrationid': instance.auditorregistrationid,
      'passingyear': instance.passingyear?.toIso8601String(),
      'percentage': instance.percentage,
      'qulname': instance.qulname,
      'universityboard': instance.universityboard,
      'subject': instance.subject,
      'steps': instance.steps,
      'auditorRegistration': instance.auditorRegistration,
    };

YourAuditorsExperienceModel _$YourAuditorsExperienceModelFromJson(
        Map<String, dynamic> json) =>
    YourAuditorsExperienceModel(
      auditorexperienceid: (json['auditorexperienceid'] as num).toInt(),
      auditorregistrationid: (json['auditorregistrationid'] as num).toInt(),
      expmonth: json['expmonth'],
      fromexp: DateTime.parse(json['fromexp'] as String),
      orgname: json['orgname'] as String,
      toexp: DateTime.parse(json['toexp'] as String),
      designation: json['designation'] as String,
    );

Map<String, dynamic> _$YourAuditorsExperienceModelToJson(
        YourAuditorsExperienceModel instance) =>
    <String, dynamic>{
      'auditorexperienceid': instance.auditorexperienceid,
      'auditorregistrationid': instance.auditorregistrationid,
      'expmonth': instance.expmonth,
      'fromexp': instance.fromexp.toIso8601String(),
      'orgname': instance.orgname,
      'toexp': instance.toexp.toIso8601String(),
      'designation': instance.designation,
    };
