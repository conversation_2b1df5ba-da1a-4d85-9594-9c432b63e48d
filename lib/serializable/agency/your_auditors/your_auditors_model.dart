
import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'your_auditors_model.g.dart';

List<AgencyYourAuditorsModel> agencyYourAuditorsModelFromJson(String str) => List<AgencyYourAuditorsModel>.from(json.decode(str).map((x) => AgencyYourAuditorsModel.fromJson(x)));

String agencyYourAuditorsModelToJson(List<AgencyYourAuditorsModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class AgencyYourAuditorsModel {
  @Json<PERSON>ey(name: 'auditorregistrationid')
  int auditorregistrationid;
  @Json<PERSON>ey(name: 'address')
  String address;
  @JsonKey(name: 'auditorname')
  String auditorname;
  @JsonKey(name: 'agencyname')
  String agencyname;
  @JsonKey(name: 'district')
  int district;
  @<PERSON>son<PERSON>ey(name: 'email')
  String email;
  @<PERSON>son<PERSON>ey(name: 'expertise')
  String expertise;
  @JsonKey(name: 'fathername')
  String fathername;
  @JsonKey(name: 'mobile')
  String mobile;
  @Json<PERSON>ey(name: 'pincode')
  String pincode;
  @JsonKey(name: 'state')
  int state;
  @JsonKey(name: 'noofaudits')
  int noofaudits;
  @JsonKey(name: 'status')
  String status;
  @JsonKey(name: 'districtname')
  String districtname;
  @JsonKey(name: 'statename')
  String statename;
  @JsonKey(name: 'leadauditorfile')
  String leadauditorfile;
  @JsonKey(name: 'qualificationfile')
  String qualificationfile;
  @JsonKey(name: 'auditorlogfile')
  String auditorlogfile;
  @JsonKey(name: 'sectorspecificfile')
  String sectorspecificfile;

  AgencyYourAuditorsModel({
    this.auditorregistrationid = 0,
    this.address = '',
    this.auditorname = '',
    this.agencyname = '',
    this.district = 0,
    this.email = '',
    this.expertise = '',
    this.fathername = '',
    this.mobile = '',
    this.pincode = '',
    this.state = 0,
    this.noofaudits = 0,
    this.status = '',
    this.districtname = '',
    this.statename = '',
    this.leadauditorfile = '',
    this.qualificationfile = '',
    this.auditorlogfile = '',
    this.sectorspecificfile = '',
  });

  factory AgencyYourAuditorsModel.fromJson(Map<String, dynamic> json) => _$AgencyYourAuditorsModelFromJson(json);

  Map<String, dynamic> toJson() => _$AgencyYourAuditorsModelToJson(this);
}


List<YourAuditorsQualificationModel> yourAuditorsQualificationModelFromJson(String str) => List<YourAuditorsQualificationModel>.from(json.decode(str).map((x) => YourAuditorsQualificationModel.fromJson(x)));

String yourAuditorsQualificationModelToJson(List<YourAuditorsQualificationModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class YourAuditorsQualificationModel {
  @JsonKey(name: 'auditorqualificationid')
  int auditorqualificationid;
  @JsonKey(name: 'auditorregistrationid')
  int auditorregistrationid;
  @JsonKey(name: 'passingyear')
  DateTime? passingyear;
  @JsonKey(name: 'percentage')
  String percentage;
  @JsonKey(name: 'qulname')
  String qulname;
  @JsonKey(name: 'universityboard')
  String universityboard;
  @JsonKey(name: 'subject')
  String subject;
  @JsonKey(name: 'steps')
  String steps;
  @JsonKey(name: 'auditorRegistration')
  String auditorRegistration;

  YourAuditorsQualificationModel({
    this.auditorqualificationid = 0,
    this.auditorregistrationid = 0,
    this.passingyear,
    this.percentage = '',
    this.qulname = '',
    this.universityboard = '',
    this.subject = '',
    this.steps = '',
    this.auditorRegistration = '',
  });

  factory YourAuditorsQualificationModel.fromJson(Map<String, dynamic> json) => _$YourAuditorsQualificationModelFromJson(json);

  Map<String, dynamic> toJson() => _$YourAuditorsQualificationModelToJson(this);
}

List<YourAuditorsExperienceModel> yourAuditorsExperienceModelFromJson(String str) => List<YourAuditorsExperienceModel>.from(json.decode(str).map((x) => YourAuditorsExperienceModel.fromJson(x)));

String yourAuditorsExperienceModelToJson(List<YourAuditorsExperienceModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class YourAuditorsExperienceModel {
  @JsonKey(name: 'auditorexperienceid')
  int auditorexperienceid;
  @JsonKey(name: 'auditorregistrationid')
  int auditorregistrationid;
  @JsonKey(name: 'expmonth')
  dynamic expmonth;
  @JsonKey(name: 'fromexp')
  DateTime fromexp;
  @JsonKey(name: 'orgname')
  String orgname;
  @JsonKey(name: 'toexp')
  DateTime toexp;
  @JsonKey(name: 'designation')
  String designation;

  YourAuditorsExperienceModel({
    required this.auditorexperienceid,
    required this.auditorregistrationid,
    required this.expmonth,
    required this.fromexp,
    required this.orgname,
    required this.toexp,
    required this.designation,
  });

  factory YourAuditorsExperienceModel.fromJson(Map<String, dynamic> json) => _$YourAuditorsExperienceModelFromJson(json);

  Map<String, dynamic> toJson() => _$YourAuditorsExperienceModelToJson(this);
}
