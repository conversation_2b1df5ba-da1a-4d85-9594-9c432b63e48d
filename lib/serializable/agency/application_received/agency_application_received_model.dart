import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'agency_application_received_model.g.dart';

List<AgencyApplicationReceivedModel> agencyApplicationReceivedModelFromJson(String str) => List<AgencyApplicationReceivedModel>.from(json.decode(str).map((x) => AgencyApplicationReceivedModel.fromJson(x)));

String agencyApplicationReceivedModelToJson(List<AgencyApplicationReceivedModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class AgencyApplicationReceivedModel {
  @JsonKey(name: 'inspectionreportid')
  int inspectionreportid;
  @JsonKey(name: 'inspectionrequestid')
  int inspectionrequestid;
  @Json<PERSON>ey(name: 'reportno')
  String reportno;
  @JsonKey(name: 'licenseno')
  String licenseno;
  @<PERSON>son<PERSON>ey(name: 'fboname')
  String fboname;
  @Json<PERSON>ey(name: 'address')
  String address;
  @JsonKey(name: 'state')
  dynamic state;
  @JsonKey(name: 'district')
  dynamic district;
  @JsonKey(name: 'agencyname')
  dynamic agencyname;
  @JsonKey(name: 'auditorname')
  String auditorname;
  @JsonKey(name: 'contactperson')
  String contactperson;
  @JsonKey(name: 'auditstartdate')
  dynamic auditstartdate;
  @JsonKey(name: 'auditenddate')
  dynamic auditenddate;
  @JsonKey(name: 'auditsubmissiondate')
  dynamic auditsubmissiondate;
  @JsonKey(name: 'dateofrequest')
  String dateofrequest;
  @JsonKey(name: 'rating')
  dynamic rating;
  @JsonKey(name: 'score')
  int score;
  @JsonKey(name: 'maxscore')
  int maxscore;
  @JsonKey(name: 'kob')
  String kob;

  AgencyApplicationReceivedModel({
  this.inspectionreportid = 0,
  this.inspectionrequestid = 0,
  this.reportno = '',
  this.licenseno = '',
  this.fboname = '',
  this.address = '',
  this.state = '',
  this.district = '',
  this.agencyname = '',
  this.auditorname = '',
  this.contactperson = '',
  this.auditstartdate = '',
  this.auditenddate = '',
  this.auditsubmissiondate = '',
  this.dateofrequest = '',
  this.rating = '',
  this.score = 0,
  this.maxscore = 0,
  this.kob = '',
  });

  factory AgencyApplicationReceivedModel.fromJson(Map<String, dynamic> json) => _$AgencyApplicationReceivedModelFromJson(json);

  Map<String, dynamic> toJson() => _$AgencyApplicationReceivedModelToJson(this);
}
