// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agency_application_received_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AgencyApplicationReceivedModel _$AgencyApplicationReceivedModelFromJson(
  Map<String, dynamic> json,
) =>
    AgencyApplicationReceivedModel(
      inspectionreportid: (json['inspectionreportid'] as num?)?.toInt() ?? 0,
      inspectionrequestid: (json['inspectionrequestid'] as num?)?.toInt() ?? 0,
      reportno: json['reportno'] as String? ?? '',
      licenseno: json['licenseno'] as String? ?? '',
      fboname: json['fboname'] as String? ?? '',
      address: json['address'] as String? ?? '',
      state: json['state'] ?? '',
      district: json['district'] ?? '',
      agencyname: json['agencyname'] ?? '',
      auditorname: json['auditorname'] as String? ?? '',
      contactperson: json['contactperson'] as String? ?? '',
      auditstartdate: json['auditstartdate'] ?? '',
      auditenddate: json['auditenddate'] ?? '',
      auditsubmissiondate: json['auditsubmissiondate'] ?? '',
      dateofrequest: json['dateofrequest'] as String? ?? '',
      rating: json['rating'] ?? '',
      score: (json['score'] as num?)?.toInt() ?? 0,
      maxscore: (json['maxscore'] as num?)?.toInt() ?? 0,
      kob: json['kob'] as String? ?? '',
    );

Map<String, dynamic> _$AgencyApplicationReceivedModelToJson(AgencyApplicationReceivedModel instance) => <String, dynamic>{
      'inspectionreportid': instance.inspectionreportid,
      'inspectionrequestid': instance.inspectionrequestid,
      'reportno': instance.reportno,
      'licenseno': instance.licenseno,
      'fboname': instance.fboname,
      'address': instance.address,
      'state': instance.state,
      'district': instance.district,
      'agencyname': instance.agencyname,
      'auditorname': instance.auditorname,
      'contactperson': instance.contactperson,
      'auditstartdate': instance.auditstartdate,
      'auditenddate': instance.auditenddate,
      'auditsubmissiondate': instance.auditsubmissiondate,
      'dateofrequest': instance.dateofrequest,
      'rating': instance.rating,
      'score': instance.score,
      'maxscore': instance.maxscore,
      'kob': instance.kob,
    };
