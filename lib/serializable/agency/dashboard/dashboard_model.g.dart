// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AgencyDashboardModel _$AgencyDashboardModelFromJson(
  Map<String, dynamic> json,
) =>
    AgencyDashboardModel(
      auditors: (json['auditors'] as num?)?.toInt() ?? 0,
      pendingWithAuditor: (json['pendingwithauditor'] as num?)?.toInt() ?? 0,
      approved: (json['approved'] as num?)?.toInt() ?? 0,
      cancelled: (json['cancelled'] as num?)?.toInt() ?? 0,
      noncompliance: (json['noncompliance'] as num?)?.toInt() ?? 0,
      applicationReceived: (json['applicationreceived'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$AgencyDashboardModelToJson(
  AgencyDashboardModel instance,
) =>
    <String, dynamic>{
      'auditors': instance.auditors,
      'pendingwithauditor': instance.pendingWithAuditor,
      'approved': instance.approved,
      'cancelled': instance.cancelled,
      'noncompliance': instance.noncompliance,
      'applicationreceived': instance.applicationReceived,
    };
