// To parse this JSON data, do
//
//     final agencyDashboardModel = agencyDashboardModelFromJson(jsonString);
import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';


part 'dashboard_model.g.dart';

AgencyDashboardModel agencyDashboardModelFromJson(String str) => AgencyDashboardModel.fromJson(json.decode(str));

String agencyDashboardModelToJson(AgencyDashboardModel data) => json.encode(data.toJson());

@JsonSerializable()
class AgencyDashboardModel {
  @JsonKey(name: 'auditors')
  int auditors;
  @Json<PERSON>ey(name: 'pendingwithauditor')
  int pendingWithAuditor;
  int approved;
  int cancelled;
  int noncompliance;
  @JsonKey(name: 'applicationreceived')
  int applicationReceived;

  AgencyDashboardModel({
    this.auditors = 0,
    this.pendingWithAuditor = 0,
    this.approved = 0,
    this.cancelled = 0,
    this.noncompliance = 0,
    this.applicationReceived = 0,
  });

  factory AgencyDashboardModel.fromJson(Map<String, dynamic> json) => _$AgencyDashboardModelFromJson(json);

  Map<String, dynamic> toJson() => _$AgencyDashboardModelToJson(this);
}
