import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'pending_with_auditor_model.g.dart';

List<AgencyPendingWithAuditorModel> agencyPendingWithAuditorModelFromJson(String str) => List<AgencyPendingWithAuditorModel>.from(json.decode(str).map((x) => AgencyPendingWithAuditorModel.fromJson(x)));

String agencyPendingWithAuditorModelToJson(List<AgencyPendingWithAuditorModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class AgencyPendingWithAuditorModel {
  @JsonKey(name: 'inspectionreportid')
  int inspectionreportid;
  @JsonKey(name: 'inspectionrequestid')
  int inspectionrequestid;
  @Json<PERSON>ey(name: 'reportno')
  String reportno;
  @JsonKey(name: 'licenseno')
  String licenseno;
  @<PERSON>son<PERSON>ey(name: 'fboname')
  String fboname;
  @Json<PERSON>ey(name: 'address')
  String address;
  @Json<PERSON>ey(name: 'state')
  dynamic state;
  @Json<PERSON>ey(name: 'district')
  dynamic district;
  @Json<PERSON>ey(name: 'agencyname')
  String agencyname;
  @JsonKey(name: 'auditorname')
  String auditorname;
  @JsonKey(name: 'contactperson')
  String contactperson;
  @JsonKey(name: 'auditstartdate')
  dynamic auditstartdate;
  @JsonKey(name: 'auditenddate')
  dynamic auditenddate;
  @JsonKey(name: 'auditsubmissiondate')
  dynamic auditsubmissiondate;
  @JsonKey(name: 'dateofrequest')
  String dateofrequest;
  @JsonKey(name: 'rating')
  dynamic rating;
  @JsonKey(name: 'score')
  int score;
  @JsonKey(name: 'maxscore')
  int maxscore;
  @JsonKey(name: 'kob')
  String kob;

  AgencyPendingWithAuditorModel({
   this.inspectionreportid = 0,
   this.inspectionrequestid = 0,
   this.reportno = '',
   this.licenseno = '',
   this.fboname = '',
   this.address = '',
   this.state = '',
   this.district = '',
   this.agencyname = '',
   this.auditorname = '',
   this.contactperson = '',
   this.auditstartdate = '',
   this.auditenddate = '',
   this.auditsubmissiondate = '',
   this.dateofrequest = '',
   this.rating = '',
   this.score = 0,
   this.maxscore = 0,
   this.kob = '',
  });

  factory AgencyPendingWithAuditorModel.fromJson(Map<String, dynamic> json) => _$AgencyPendingWithAuditorModelFromJson(json);

  Map<String, dynamic> toJson() => _$AgencyPendingWithAuditorModelToJson(this);
}
