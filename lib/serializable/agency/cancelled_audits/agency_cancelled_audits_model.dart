import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'agency_cancelled_audits_model.g.dart';

List<AgencyCancelledAuditsModel> agencyCancelledAuditsModelFromJson(String str) => List<AgencyCancelledAuditsModel>.from(json.decode(str).map((x) => AgencyCancelledAuditsModel.fromJson(x)));

String agencyCancelledAuditsModelToJson(List<AgencyCancelledAuditsModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class AgencyCancelledAuditsModel {
  @JsonKey(name: 'inspectionreportid')
  int inspectionreportid;
  @JsonKey(name: 'inspectionrequestid')
  int inspectionrequestid;
  @JsonKey(name: 'reportno')
  dynamic reportno;
  @JsonKey(name: 'licenseno')
  String licenseno;
  @JsonKey(name: 'fboname')
  String fboname;
  @JsonKey(name: 'address')
  String address;
  @JsonKey(name: 'state')
  dynamic state;
  @Json<PERSON>ey(name: 'district')
  dynamic district;
  @JsonKey(name: 'agencyname')
  String agencyname;
  @JsonKey(name: 'auditorname')
  String auditorname;
  @JsonKey(name: 'contactperson')
  String contactperson;
  @JsonKey(name: 'auditstartdate')
  dynamic auditstartdate;
  @JsonKey(name: 'auditenddate')
  dynamic auditenddate;
  @JsonKey(name: 'auditsubmissiondate')
  dynamic auditsubmissiondate;
  @JsonKey(name: 'dateofrequest')
  String dateofrequest;
  @JsonKey(name: 'rating')
  dynamic rating;
  @JsonKey(name: 'score')
  int score;
  @JsonKey(name: 'maxscore')
  int maxscore;
  @JsonKey(name: 'kob')
  String kob;

  AgencyCancelledAuditsModel({
   this.inspectionreportid = 0,
   this.inspectionrequestid = 0,
   this.reportno = '',
   this.licenseno = '',
   this.fboname = '',
   this.address = '',
   this.state = '',
   this.district = '',
   this.agencyname = '',
   this.auditorname = '',
   this.contactperson = '',
   this.auditstartdate = '',
   this.auditenddate = '',
   this.auditsubmissiondate = '',
   this.dateofrequest = '',
   this.rating = '',
   this.score = 0,
   this.maxscore = 0,
   this.kob = '',
  });

  factory AgencyCancelledAuditsModel.fromJson(Map<String, dynamic> json) => _$AgencyCancelledAuditsModelFromJson(json);

  Map<String, dynamic> toJson() => _$AgencyCancelledAuditsModelToJson(this);
}
