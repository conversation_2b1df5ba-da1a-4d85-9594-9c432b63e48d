// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'non_compliance_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AgencyNonComplianceModel _$AgencyNonComplianceModelFromJson(
  Map<String, dynamic> json,
) =>
    AgencyNonComplianceModel(
      inspectionreportid: (json['inspectionreportid'] as num).toInt(),
      inspectionrequestid: (json['inspectionrequestid'] as num).toInt(),
      reportno: json['reportno'],
      licenseno: json['licenseno'] as String,
      fboname: json['fboname'] as String,
      address: json['address'] as String,
      state: json['state'],
      district: json['district'],
      agencyname: json['agencyname'] as String,
      auditorname: json['auditorname'] as String,
      contactperson: json['contactperson'] as String,
      auditstartdate: json['auditstartdate'],
      auditenddate: json['auditenddate'],
      auditsubmissiondate: json['auditsubmissiondate'],
      dateofrequest: json['dateofrequest'] as String,
      rating: json['rating'],
      score: (json['score'] as num).toInt(),
      maxscore: (json['maxscore'] as num).toInt(),
      kob: json['kob'] as String,
    );

Map<String, dynamic> _$AgencyNonComplianceModelToJson(
  AgencyNonComplianceModel instance,
) =>
    <String, dynamic>{
      'inspectionreportid': instance.inspectionreportid,
      'inspectionrequestid': instance.inspectionrequestid,
      'reportno': instance.reportno,
      'licenseno': instance.licenseno,
      'fboname': instance.fboname,
      'address': instance.address,
      'state': instance.state,
      'district': instance.district,
      'agencyname': instance.agencyname,
      'auditorname': instance.auditorname,
      'contactperson': instance.contactperson,
      'auditstartdate': instance.auditstartdate,
      'auditenddate': instance.auditenddate,
      'auditsubmissiondate': instance.auditsubmissiondate,
      'dateofrequest': instance.dateofrequest,
      'rating': instance.rating,
      'score': instance.score,
      'maxscore': instance.maxscore,
      'kob': instance.kob,
    };
