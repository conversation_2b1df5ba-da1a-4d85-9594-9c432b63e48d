import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'non_compliance_model.g.dart';

List<AgencyNonComplianceModel> agencyNonComplianceModelFromJson(String str) => List<AgencyNonComplianceModel>.from(json.decode(str).map((x) => AgencyNonComplianceModel.fromJson(x)));

String agencyNonComplianceModelToJson(List<AgencyNonComplianceModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@JsonSerializable()
class AgencyNonComplianceModel {
  @JsonKey(name: 'inspectionreportid')
  int inspectionreportid;
  @JsonKey(name: 'inspectionrequestid')
  int inspectionrequestid;
  @Json<PERSON>ey(name: 'reportno')
  dynamic reportno;
  @JsonKey(name: 'licenseno')
  String licenseno;
  @JsonKey(name: 'fboname')
  String fboname;
  @Json<PERSON>ey(name: 'address')
  String address;
  @JsonKey(name: 'state')
  dynamic state;
  @Json<PERSON>ey(name: 'district')
  dynamic district;
  @JsonKey(name: 'agencyname')
  String agencyname;
  @JsonKey(name: 'auditorname')
  String auditorname;
  @JsonKey(name: 'contactperson')
  String contactperson;
  @JsonKey(name: 'auditstartdate')
  dynamic auditstartdate;
  @JsonKey(name: 'auditenddate')
  dynamic auditenddate;
  @JsonKey(name: 'auditsubmissiondate')
  dynamic auditsubmissiondate;
  @JsonKey(name: 'dateofrequest')
  String dateofrequest;
  @JsonKey(name: 'rating')
  dynamic rating;
  @JsonKey(name: 'score')
  int score;
  @JsonKey(name: 'maxscore')
  int maxscore;
  @JsonKey(name: 'kob')
  String kob;

  AgencyNonComplianceModel({
    required this.inspectionreportid,
    required this.inspectionrequestid,
    required this.reportno,
    required this.licenseno,
    required this.fboname,
    required this.address,
    required this.state,
    required this.district,
    required this.agencyname,
    required this.auditorname,
    required this.contactperson,
    required this.auditstartdate,
    required this.auditenddate,
    required this.auditsubmissiondate,
    required this.dateofrequest,
    required this.rating,
    required this.score,
    required this.maxscore,
    required this.kob,
  });

  factory AgencyNonComplianceModel.fromJson(Map<String, dynamic> json) => _$AgencyNonComplianceModelFromJson(json);

  Map<String, dynamic> toJson() => _$AgencyNonComplianceModelToJson(this);
}
