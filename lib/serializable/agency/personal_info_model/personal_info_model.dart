import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';


part 'personal_info_model.g.dart';

AgencyPersonalInfoModel agencyPersonalInfoModelFromJson(String str) => AgencyPersonalInfoModel.fromJson(json.decode(str));

String agencyPersonalInfoModelToJson(AgencyPersonalInfoModel data) => json.encode(data.toJson());

@JsonSerializable()
class AgencyPersonalInfoModel {
  @JsonKey(name: 'agencyid')
  int? agencyId;
  @JsonKey(name: 'agencyname')
  String? agencyName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'contactpersonname')
  dynamic contactPersonName;
  @Json<PERSON>ey(name: 'statename')
  dynamic stateName;
  @JsonKey(name: 'districtname')
  dynamic districtName;
  dynamic address;
  @Json<PERSON>ey(name: 'pincode')
  dynamic pinCode;
  dynamic phone;
  dynamic mobile;
  dynamic email;
  @<PERSON>son<PERSON>ey(name: 'certificatenumber')
  String? certificateNumber;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'validupto')
  DateTime? validUpto;
  @J<PERSON><PERSON><PERSON>(name: 'recognitionnumber')
  String? recognitionNumber;
  @Json<PERSON><PERSON>(name: 'legalentrystatus')
  String? legalEntryStatus;
  dynamic status;
  @JsonKey(name: 'noofauditors')
  int? noOfAuditors;
  @JsonKey(name: 'noofaudits')
  dynamic noOfAudits;
  dynamic remarks;
  @JsonKey(name: 'auditingscope')
  dynamic auditingScope;
  @JsonKey(name: 'geographicalarea')
  dynamic geographicalArea;
  List<KobDetails> kob;
  List<DocumentDetail> docDetails;

  AgencyPersonalInfoModel({
    this.agencyId,
    this.agencyName,
    this.contactPersonName,
    this.stateName,
    this.districtName,
    this.address,
    this.pinCode,
    this.phone,
    this.mobile,
    this.email,
    this.certificateNumber,
    this.validUpto,
    this.recognitionNumber,
    this.legalEntryStatus,
    this.status,
    this.noOfAuditors,
    this.noOfAudits,
    this.remarks,
    this.auditingScope,
    this.geographicalArea,
    this.kob=const <KobDetails>[],
    this.docDetails=const <DocumentDetail>[],
  });

  factory AgencyPersonalInfoModel.fromJson(Map<String, dynamic> json) => _$AgencyPersonalInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$AgencyPersonalInfoModelToJson(this);
}

@JsonSerializable()
class DocumentDetail {
  @JsonKey(name: 'id')
  int? id;
  @JsonKey(name: 'filename')
  String? filename;

  DocumentDetail({
    this.id,
    this.filename,
  });

  factory DocumentDetail.fromJson(Map<String, dynamic> json) => _$DocumentDetailFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentDetailToJson(this);
}

@JsonSerializable()
class KobDetails {
  @JsonKey(name: 'kobid')
  int? kobId;
  @JsonKey(name: 'kobmasterid')
  int? kobMasterId;
  @JsonKey(name: 'kobmastername')
  dynamic kobmasterName;
  @JsonKey(name: 'kobname')
  String? kobName;

  KobDetails({
    this.kobId,
    this.kobMasterId,
    this.kobmasterName,
    this.kobName,
  });

  factory KobDetails.fromJson(Map<String, dynamic> json) => _$KobDetailsFromJson(json);

  Map<String, dynamic> toJson() => _$KobDetailsToJson(this);
}
