// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'personal_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AgencyPersonalInfoModel _$AgencyPersonalInfoModelFromJson(
  Map<String, dynamic> json,
) =>
    AgencyPersonalInfoModel(
      agencyId: (json['agencyid'] as num?)?.toInt(),
      agencyName: json['agencyname'] as String?,
      contactPersonName: json['contactpersonname'],
      stateName: json['statename'],
      districtName: json['districtname'],
      address: json['address'],
      pinCode: json['pincode'],
      phone: json['phone'],
      mobile: json['mobile'],
      email: json['email'],
      certificateNumber: json['certificatenumber'] as String?,
      validUpto: json['validupto'] == null ? null : DateTime.parse(json['validupto'] as String),
      recognitionNumber: json['recognitionnumber'] as String?,
      legalEntryStatus: json['legalentrystatus'] as String?,
      status: json['status'],
      noOfAuditors: (json['noofauditors'] as num?)?.toInt(),
      noOfAudits: json['noofaudits'],
      remarks: json['remarks'],
      auditingScope: json['auditingscope'],
      geographicalArea: json['geographicalarea'],
      kob: (json['kob'] as List<dynamic>?)?.map((e) => KobDetails.fromJson(e as Map<String, dynamic>)).toList() ??
          const <KobDetails>[],
      docDetails:
          (json['docDetails'] as List<dynamic>?)?.map((e) => DocumentDetail.fromJson(e as Map<String, dynamic>)).toList() ??
              const <DocumentDetail>[],
    );

Map<String, dynamic> _$AgencyPersonalInfoModelToJson(
  AgencyPersonalInfoModel instance,
) =>
    <String, dynamic>{
      'agencyid': instance.agencyId,
      'agencyname': instance.agencyName,
      'contactpersonname': instance.contactPersonName,
      'statename': instance.stateName,
      'districtname': instance.districtName,
      'address': instance.address,
      'pincode': instance.pinCode,
      'phone': instance.phone,
      'mobile': instance.mobile,
      'email': instance.email,
      'certificatenumber': instance.certificateNumber,
      'validupto': instance.validUpto?.toIso8601String(),
      'recognitionnumber': instance.recognitionNumber,
      'legalentrystatus': instance.legalEntryStatus,
      'status': instance.status,
      'noofauditors': instance.noOfAuditors,
      'noofaudits': instance.noOfAudits,
      'remarks': instance.remarks,
      'auditingscope': instance.auditingScope,
      'geographicalarea': instance.geographicalArea,
      'kob': instance.kob,
      'docDetails': instance.docDetails,
    };

DocumentDetail _$DocumentDetailFromJson(Map<String, dynamic> json) => DocumentDetail(
      id: (json['id'] as num?)?.toInt(),
      filename: json['filename'] as String?,
    );

Map<String, dynamic> _$DocumentDetailToJson(DocumentDetail instance) => <String, dynamic>{
      'id': instance.id,
      'filename': instance.filename,
    };

KobDetails _$KobDetailsFromJson(Map<String, dynamic> json) => KobDetails(
      kobId: (json['kobid'] as num?)?.toInt(),
      kobMasterId: (json['kobmasterid'] as num?)?.toInt(),
      kobmasterName: json['kobmastername'],
      kobName: json['kobname'] as String?,
    );

Map<String, dynamic> _$KobDetailsToJson(KobDetails instance) => <String, dynamic>{
      'kobid': instance.kobId,
      'kobmasterid': instance.kobMasterId,
      'kobmastername': instance.kobmasterName,
      'kobname': instance.kobName,
    };
