// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'registration_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AgencyRegistrationModel _$AgencyRegistrationModelFromJson(
        Map<String, dynamic> json) =>
    AgencyRegistrationModel(
      steps: (json['steps'] as num).toInt(),
      agencyName: json['agencyname'] as String,
      contactPersonName: json['contactpersonname'] as String,
      state: (json['state'] as num).toInt(),
      district: (json['district'] as num).toInt(),
      address: json['address'] as String,
      pincode: json['pincode'] as String,
      phone: json['phone'] as String,
      mobile: json['mobile'] as String,
      email: json['email'] as String,
      website: json['website'] as String,
      legalEntryStatus: json['legalentrystatus'] as String,
      certificateNumber: json['certificatenumber'] as String,
      validUpTo: DateTime.parse(json['validupto'] as String),
      recognitionNumber: json['recognitionnumber'] as String,
      numberOfAuditor: (json['numberofauditor'] as num).toInt(),
      feeStatus: json['feestatus'] as String,
      status: json['status'] as String,
      verifyStatus: json['verifystatus'] as String,
      secondFeeStatus: json['secondfeestatus'] as String,
      emailFlag: json['emailflag'] as String,
      activeFlag: json['activeflag'] as String,
      scopeOfAuditing: json['scopeofauditing'] as String,
      geographicalArea: json['geographicalarea'] as String,
      kOb: (json['kOB'] as List<dynamic>)
          .map((e) => KOb.fromJson(e as Map<String, dynamic>))
          .toList(),
      auditAgencyRegistrationId:
          (json['auditagencyregistrationid'] as num?)?.toInt(),
    );

Map<String, dynamic> _$AgencyRegistrationModelToJson(
        AgencyRegistrationModel instance) =>
    <String, dynamic>{
      'auditagencyregistrationid': instance.auditAgencyRegistrationId,
      'steps': instance.steps,
      'agencyname': instance.agencyName,
      'contactpersonname': instance.contactPersonName,
      'state': instance.state,
      'district': instance.district,
      'address': instance.address,
      'pincode': instance.pincode,
      'phone': instance.phone,
      'mobile': instance.mobile,
      'email': instance.email,
      'website': instance.website,
      'legalentrystatus': instance.legalEntryStatus,
      'certificatenumber': instance.certificateNumber,
      'validupto': instance.validUpTo.toIso8601String(),
      'recognitionnumber': instance.recognitionNumber,
      'numberofauditor': instance.numberOfAuditor,
      'feestatus': instance.feeStatus,
      'status': instance.status,
      'verifystatus': instance.verifyStatus,
      'secondfeestatus': instance.secondFeeStatus,
      'emailflag': instance.emailFlag,
      'activeflag': instance.activeFlag,
      'scopeofauditing': instance.scopeOfAuditing,
      'geographicalarea': instance.geographicalArea,
      'kOB': instance.kOb,
    };

KOb _$KObFromJson(Map<String, dynamic> json) => KOb(
      kobRegistrationId: (json['kobregistrationid'] as num?)?.toInt(),
      auditAgencyRegistrationId: json['auditagencyregistrationid'],
      kobId: (json['kobid'] as num).toInt(),
      agencyRegistration: (json['agencyRegistration'] as num?)?.toInt(),
    );

Map<String, dynamic> _$KObToJson(KOb instance) => <String, dynamic>{
      'kobregistrationid': instance.kobRegistrationId,
      'auditagencyregistrationid': instance.auditAgencyRegistrationId,
      'kobid': instance.kobId,
      'agencyRegistration': instance.agencyRegistration,
    };
