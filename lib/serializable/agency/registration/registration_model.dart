// To parse this JSON data, do
//
//     final agencyRegistrationModel = agencyRegistrationModelFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'registration_model.g.dart';

AgencyRegistrationModel agencyRegistrationModelFromJson(String str) => AgencyRegistrationModel.fromJson(json.decode(str));

String agencyRegistrationModelToJson(AgencyRegistrationModel data) => json.encode(data.toJson());

@JsonSerializable()
class AgencyRegistrationModel {
  @JsonKey(name: 'auditagencyregistrationid')
  int? auditAgencyRegistrationId;
  @Json<PERSON>ey(name: 'steps')
  int steps;
  @JsonKey(name: 'agencyname')
  String agencyName;
  @JsonKey(name: 'contactpersonname')
  String contactPersonName;
  @JsonKey(name: 'state')
  int state;
  @Json<PERSON>ey(name: 'district')
  int district;
  @Json<PERSON>ey(name: 'address')
  String address;
  @JsonKey(name: 'pincode')
  String pincode;
  @JsonKey(name: 'phone')
  String phone;
  @JsonKey(name: 'mobile')
  String mobile;
  @JsonKey(name: 'email')
  String email;
  @JsonKey(name: 'website')
  String website;
  @JsonKey(name: 'legalentrystatus')
  String legalEntryStatus;
  @JsonKey(name: 'certificatenumber')
  String certificateNumber;
  @JsonKey(name: 'validupto')
  DateTime validUpTo;
  @JsonKey(name: 'recognitionnumber')
  String recognitionNumber;
  @JsonKey(name: 'numberofauditor')
  int numberOfAuditor;
  @JsonKey(name: 'feestatus')
  String feeStatus;
  @JsonKey(name: 'status')
  String status;
  @JsonKey(name: 'verifystatus')
  String verifyStatus;
  @JsonKey(name: 'secondfeestatus')
  String secondFeeStatus;
  @JsonKey(name: 'emailflag')
  String emailFlag;
  @JsonKey(name: 'activeflag')
  String activeFlag;
  @JsonKey(name: 'scopeofauditing')
  String scopeOfAuditing;
  @JsonKey(name: 'geographicalarea')
  String geographicalArea;
  @JsonKey(name: 'kOB')
  List<KOb> kOb;

  AgencyRegistrationModel({
    required this.steps,
    required this.agencyName,
    required this.contactPersonName,
    required this.state,
    required this.district,
    required this.address,
    required this.pincode,
    required this.phone,
    required this.mobile,
    required this.email,
    required this.website,
    required this.legalEntryStatus,
    required this.certificateNumber,
    required this.validUpTo,
    required this.recognitionNumber,
    required this.numberOfAuditor,
    required this.feeStatus,
    required this.status,
    required this.verifyStatus,
    required this.secondFeeStatus,
    required this.emailFlag,
    required this.activeFlag,
    required this.scopeOfAuditing,
    required this.geographicalArea,
    required this.kOb,
    this.auditAgencyRegistrationId,
  });

  factory AgencyRegistrationModel.fromJson(Map<String, dynamic> json) => _$AgencyRegistrationModelFromJson(json);

  Map<String, dynamic> toJson() => _$AgencyRegistrationModelToJson(this);
}

@JsonSerializable()
class KOb {
  @JsonKey(name: 'kobregistrationid')
  int? kobRegistrationId;
  @JsonKey(name: 'auditagencyregistrationid')
  dynamic auditAgencyRegistrationId;
  @JsonKey(name: 'kobid')
  int kobId;
  @JsonKey(name: 'agencyRegistration')
  int? agencyRegistration;

  KOb({
    required this.kobRegistrationId,
    required this.auditAgencyRegistrationId,
    required this.kobId,
    required this.agencyRegistration,
  });

  factory KOb.fromJson(Map<String, dynamic> json) => _$KObFromJson(json);

  Map<String, dynamic> toJson() => _$KObToJson(this);
}
