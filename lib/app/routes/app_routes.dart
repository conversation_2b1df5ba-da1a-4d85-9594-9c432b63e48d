import 'package:ams/app_imports.dart';

void gotoBack() => router.pop();

void gotoLoginPage({String message = ''}) => router.go(
      RouteHelper.loginPage,
      extra: {
        'message': message,
      },
    );

void gotoForgotPasswordPage() => router.push(RouteHelper.forgotPasswordPage);

void gotoChangePasswordPage() => router.push(RouteHelper.changePasswordPage);

void gotoNoInternetPage() => router.push(RouteHelper.noInternet);

void gotoSignupPage() => router.push(RouteHelper.signupPage);

void gotoUploadDocumentPage(AgencyRegistrationModel agencyRegistrationModel) => router.push(
      '${RouteHelper.signupPage}/${RouteHelper.uploadDocumentPage}',
      extra: {
        'agencyRegistrationModel': agencyRegistrationModel,
      },
    );

void gotoInspectionReportPage() => router.push(RouteHelper.inspectionReportPage);

///--------------------------Super Admin-----------------------------///

void gotoSuperAdminDashboardPage() => router.go(RouteHelper.superAdminDashboardPage);

void gotoSuperAdminApplicationReceivedPage() => router.push(RouteHelper.superAdminApplicationReceived);

void gotoSuperAdminEmpanelledAgenciesPage() => router.push(RouteHelper.superAdminEmpanelledAgencies);

void gotoSuperAdminAuditsDonePage() => router.push(RouteHelper.superAdminAuditsDone);

void gotoSuperAdminEnlistedAuditorsPage() => router.push(RouteHelper.superAdminEnlistedAuditors);

void gotoSuperAdminRejectedAgenciesPage() => router.push(RouteHelper.superAdminRejectedAgencies);

void gotoSuperAdminReportsPage() => router.push(RouteHelper.superAdminReports);

void gotoSuperAdminAgencyDetailPage({required int? agencyId}) => router.push(
      '${RouteHelper.superAdminApplicationReceived}/${RouteHelper.superAdminViewAgencyDetails}',
      extra: {
        'agencyId': agencyId,
      },
    );

void gotoSuperViewAgencyAuditorsPage({int? agencyId}) => router.push(
      '${RouteHelper.superAdminApplicationReceived}/${RouteHelper.superAdminViewAgencyAuditors}',
      extra: {
        'agencyId': agencyId,
      },
    );

void gotoSuperAdminConductAuditReportPage() => router.push(
      '${RouteHelper.superAdminApplicationReceived}/${RouteHelper.superAdminViewAgencyAuditors}/${RouteHelper.superAdminAuditReport}',
    );

void gotoSuperAdminExperiencePage() => router.push(RouteHelper.superAdminExperience);

void gotoSuperAdminQualificationPage() => router.push(RouteHelper.superAdminQualification);

void gotoSuperAdminViewAuditorsPage(int? agencyId) => router.push(
      RouteHelper.superAdminViewAuditors,
      extra: {
        'agencyId': agencyId,
      },
    );

void gotoSuperAdminAuditConductByAuditorPage({required List<AuditModel> audits}) =>
    router.push('${RouteHelper.superAdminViewAuditors}/${RouteHelper.superAdminAuditConductByAuditor}', extra: audits);

///--------------------------Agency-----------------------------///
void gotoAgencyDashboardPage() => router.go(RouteHelper.agencyDashboardPage);

void gotoAgencyYourAuditorPage({required int agencyId}) => router.push(
      RouteHelper.agencyYourAuditorPage,
      extra: {
        'agencyId': agencyId,
      },
    );

void gotoAuditReportPage() => router.push(RouteHelper.auditReportPage);

void gotoQualificationPage({required Map<String, dynamic> item}) => router.push(
      RouteHelper.qualificationPage,
      extra: {'auditor': item},
    );

void gotoExperiencePage({required Map<String, dynamic> item}) => router.push(
      RouteHelper.experiencePage,
      extra: {'auditor': item},
    );

void gotoPersonalInfoPage() => router.push(RouteHelper.personalInfoPage);

void gotoAssignAuditPage() => router.push(RouteHelper.assignAuditPage);

void gotoRegisterAuditorPage() => router.push(RouteHelper.registerAuditorPage);

void gotoAgencyCancelledAuditPage() => router.push(RouteHelper.agencyCancelledAuditPage);

void gotoPendingWithAuditorPage() => router.push(RouteHelper.pendingWithAuditorPage);

void gotoAgencyApprovedPage() => router.push(RouteHelper.agencyApprovedPage);

void gotApplicationReceivedPage() => router.push(RouteHelper.applicationReceivedPage);

void gotoAgencyInspectionReportPage() => router.push(RouteHelper.agencyInspectionReportPage);

void gotoAgencyNonCompliancePage() => router.push(RouteHelper.agencyNonCompliancePage);

///--------------------------Auditor-----------------------------///
void gotoAuditorDashboardPage() => router.go(RouteHelper.auditorDashboardPage);

void gotoAuditorPersonalInfoPage() => router.push(RouteHelper.auditorPersonalInfoPage);

void gotoAuditorApprovedAuditPage() => router.push(RouteHelper.auditorApprovedAuditPage);

void gotoAuditorApprovalPendingAuditPage() => router.push(RouteHelper.auditorApprovalPendingAuditPage);

void gotoAuditorCancelledAuditPage() => router.push(RouteHelper.auditorCancelledAuditPage);

void gotoAuditorReceivedAuditPage() => router.push(RouteHelper.auditorReceivedAuditPage);

void gotoConductAuditPage() => router.push('${RouteHelper.auditorReceivedAuditPage}/${RouteHelper.conductAuditPage}');

void gotoAuditorNonCompliancePage() => router.push(RouteHelper.auditorNonCompliancePage);

void gotoAuditorAuditModificationPage() =>
    router.push('${RouteHelper.auditorReceivedAuditPage}/${RouteHelper.auditorAuditModificationPage}');

void gotoChecklistPage() =>
    router.push('${RouteHelper.auditorReceivedAuditPage}/${RouteHelper.conductAuditPage}/${RouteHelper.checklistPage}');
