import 'package:ams/app_imports.dart';

class DownloadHelper {
  DownloadHelper._privateConstructor();

  static final DownloadHelper instance = DownloadHelper._privateConstructor();

  Future<void> downloadPdf(String fileName, BuildContext context) async {
    try {
      String filePath = await PermissionHelper.instance.getPath(context: context);
      var response = await RestHelper.instance
          .downloadPdfRestCall(endpoint: RestConstants.instance.downloadPdfEndpoint, fileName: fileName);
      if (response?.statusCode == 200) {
        var file = File('$filePath/$fileName');
        logs('file --> ${file.path}');
        File file1 = await file.writeAsBytes(response!.bodyBytes);
        logs('file1 --> ${file1.path}');
        'File Downloaded Successfully'.showSuccessToast();
      } else {
        'Something went wrong'.showErrorToast();
      }
    } on SocketException catch (e) {
      errorLogs('SocketException in downloadPdf -->$e');
    }
  }
}
