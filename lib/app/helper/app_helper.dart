import 'package:ams/app_imports.dart';
import 'package:url_launcher/url_launcher_string.dart';


void logs(r) {
  if (kDebugMode) {
    print('$r');
  }
}


Future<String?> pasteFromClipboard() async {
  ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
  if (data != null) {
    logs('Text pasted from clipboard: ${data.text}');
    return data.text;
  } else {
    logs('Clipboard is empty');
    return '';
  }
}

void infoLogs(String message) {
  if (kDebugMode) {
    Logger(printer: PrettyPrinter(methodCount: 0)).i(message);
  }
}

void traceLogs(String message) {
  if (kDebugMode) {
    Logger(printer: PrettyPrinter(methodCount: 0)).t(message);
  }
}

void warningLogs(String message) {
  if (kDebugMode) {
    Logger(printer: PrettyPrinter(methodCount: 0)).w(message);
  }
}

void errorLogs(String message) {
  if (kDebugMode) {
    Logger(printer: <PERSON><PERSON>rinter(methodCount: 0)).e(message);
  }
}

bool isEngLanguage() {
  Locale? locale = Get.locale;
  return locale != null && locale.languageCode == 'en';
}

String parseHtmlString(String htmlString) {
  RegExp exp = RegExp(r'<[^>]*>', multiLine: true, caseSensitive: true);
  return htmlString.replaceAll(exp, '');
}

String makeFirstLetterLowerCase(String input) {
  if (input.isEmpty) {
    return input;
  }
  return input[0].toLowerCase() + input.substring(1);
}

Future<void> launchDocumentUrl(String urlPath) async {
  if (await canLaunchUrl(Uri.parse(urlPath))) {
    await launchUrlString(urlPath);
  } else {
    AppStringConstants.docCouldNotOpen.tr.showErrorToast();
  }
}


String replaceLastSixChars(String input) {
  if (input.length <= 6) {
    return input;
  } else {
    return input.replaceRange(input.length - 6, input.length, 'x' * 6);
  }
}
