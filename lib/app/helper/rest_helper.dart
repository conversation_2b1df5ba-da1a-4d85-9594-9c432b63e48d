import 'package:ams/app_imports.dart';

class RestConstants {
  RestConstants._privateConstructor();

  static final RestConstants instance = RestConstants._privateConstructor();

  String getStateEndpoint = 'api/v1/states';
  String getDistrictsEndpoint = 'api/v1/districts';
  String getKobDetailsEndpoint = 'api/v1/getKobMasterKobDetailList';
  String changePassword = 'api/v1/change-password-submit';
  String loginEndpoint = 'auth/login';
  String logOutEndpoint = 'auth/logout';
  String downloadPdfEndpoint = 'pdf/download';

  //:::::::::::::::::::::::::::::::::::::: authentication Endpoints ::::::::::::::::::::::::::::::::::::::::::
  String getSixDigitCodeEndPoint = 'api/v1/sendSixDigitCode';
  String resetPasswordEndpoint = 'api/v1/resetForgottonPassword';

  //:::::::::::::::::::::::::::::::::::::: Superadmin Endpoints ::::::::::::::::::::::::::::::::::::::::::

  String getSuperAdminDashboardEndpoint = 'api/v1/getAdminDashBoardData';
  String getSuperAdminAllAgenciesEndpoint = 'api/v1/allagencies';
  String getSuperAdminAgencyDetailsEndpoint = 'api/v1/getAgencyDetailsById';
  String getAuditorsEndpoint = 'api/v1/allauditors';
  String superAdminApproveRejectAgencyEndpoint = 'api/v1/approveRejectAgency';
  String getSuperAdminAgenciesEndpoint = 'api/v1/agencies';
  String superAdminUpdateAgencyDetailsEndpoint = 'api/v1/updateAgencyDetails';
  String superAdminActivateDeactivateAgencyEndpoint = 'api/v1/activateDeactivateAgency';
  String superAdminGetChecklistCategoriesEndpoint = 'api/v1/getChecklistCategories';

  //:::::::::::::::::::::::::::::::::::::: Agency Endpoints ::::::::::::::::::::::::::::::::::::::::::

  String registerAgencyEndpoint = 'api/v1/regAgency';
  String getAgencyDashboardDataEndpoint = 'api/v1/getAgencyDashBoardData';
  String getAgencyApplicationReceivedEndpoint = 'api/v1/getApplicationReceived/323';
  String getAgencyNonComplianceReportsEndpoint = 'api/v1/getNonComplianceReports';
  String getAgencyApprovedReportsEndpoint = 'api/v1/getApprovedReports/416/2';
  String getAgencyAuditReportsPendingWithAuditorEndpoint = 'api/v1/getAuditReportsPendingWithAuditor/392';
  String getAgencyCancelledReportsEndpoint = 'api/v1/getCancelledReports/416';
  String getAgencyYourAuditorsEndpoint = 'api/v1/allauditors';
  String getAgencyYourAuditorsQualificationEndpoint = 'api/v1/getAuditorQualification';
  String getAgencyYourAuditorsExperienceEndpoint = 'api/v1/getAuditorExperience';
  String getAgencyPersonalInfoByIdEndpoint = 'api/v1/getAgencyDetailsById';

  //:::::::::::::::::::::::::::::::::::::: Auditor Endpoints ::::::::::::::::::::::::::::::::::::::::::
  String getAuditorDashboardEndpoint = 'api/v1/getAuditorDashBoardData';
}

class RestHelper {
  RestHelper._privateConstructor();

  static final RestHelper instance = RestHelper._privateConstructor();

  static final Map<String, String> _headers = {'Content-Type': 'application/json'};
  static final Map<String, String> _multiPartHeaders = {'Content-Type': 'multipart/form-data'};

  void showRequestAndResponseLogs(Response? response) {
    infoLogs('•••••••••• Network debugLogs ••••••••••');
    infoLogs('Request url --> ${response?.request!.url}');
    infoLogs('Request code --> ${response?.statusCode}');
    infoLogs('Request headers --> ${response?.request?.headers}');
    infoLogs('Response headers --> ${response?.headers}');
    infoLogs('Response body --> ${response?.body}');
    infoLogs('••••••••••••••••••••••••••••••••••');
  }

  Future<String> getRestCall({required String? endpoint, String? addOns}) async {
    String responseData = '';
    bool? isConnected = await ConnectivityHelper.instance.isConnectNetworkWithMessage();
    if (!isConnected) {
      return responseData;
    }
    try {
      String accessToken = await getPrefStringValue(AppPrefConstants.token) ?? '';
      if (accessToken.isNotEmpty) {
        _headers['Authorization'] = 'Bearer $accessToken';
      }
      String requestUrl =
          addOns != null ? '${AppConfig.shared.baseUrl}/$endpoint$addOns' : '${AppConfig.shared.baseUrl}/$endpoint';

      Uri requestUri = Uri.parse(requestUrl);
      Response response = await get(requestUri, headers: _headers);

      showRequestAndResponseLogs(response);

      switch (response.statusCode) {
        case 200:
        case 201:
          responseData = response.body;
          break;
        case 400:
        case 401:
        case 404:
          jsonDecode(response.body)['error'].toString().showErrorToast();
          break;
        case 422:
          responseData = response.body;
          break;
        case 500:
          logs('${response.statusCode}');
          break;
        default:
          logs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      errorLogs('PlatformException in getRestCall --> ${e.message}');
    }
    return responseData;
  }

  Future<String> postRestCall({
    required String? endpoint,
    required Map<String, dynamic>? body,
    String? addOns,
  }) async {
    String responseData = '';
    bool? isConnected = await ConnectivityHelper.instance.isConnectNetworkWithMessage();
    if (!isConnected) {
      return responseData;
    }
    try {
      String accessToken = await getPrefStringValue(AppPrefConstants.token) ?? '';
      if (accessToken.isNotEmpty) {
        _headers['Authorization'] = 'Bearer $accessToken';
      }
      String requestUrl =
          addOns != null ? '${AppConfig.shared.baseUrl}/$endpoint$addOns' : '${AppConfig.shared.baseUrl}/$endpoint';
      Uri requestedUri = Uri.parse(requestUrl);
      logs('Body map --> $body');

      Response response = (body == null)
          ? await post(requestedUri, headers: _headers)
          : await post(requestedUri, body: jsonEncode(body), headers: _headers);
      showRequestAndResponseLogs(response);
      switch (response.statusCode) {
        case 200:
        case 201:
          responseData = response.body;
          break;
        case 400:
        case 401:
        case 404:
          jsonDecode(response.body)['error'].toString().showErrorToast();
          break;
        case 422:
          responseData = response.body;
          break;
        case 500:
          logs('${response.statusCode} : ${response.body}');
          break;
        default:
          logs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      errorLogs('PlatformException in postRestCall --> ${e.message}');
    }
    return responseData;
  }

  Future<String> putRestCall({
    required String? endpoint,
    required Map<String, dynamic>? body,
    String? addOns,
  }) async {
    String responseData = '';
    bool? isConnected = await ConnectivityHelper.instance.isConnectNetworkWithMessage();
    if (!isConnected) {
      return responseData;
    }
    try {
      String accessToken = await getPrefStringValue(AppPrefConstants.token) ?? '';
      if (accessToken.isNotEmpty) {
        _headers['Authorization'] = 'Bearer $accessToken';
      }
      String requestUrl =
          addOns != null ? '${AppConfig.shared.baseUrl}/$endpoint$addOns' : '${AppConfig.shared.baseUrl}/$endpoint';
      Uri requestedUri = Uri.parse(requestUrl);
      logs('Body map --> $body');

      Response response = (body == null)
          ? await put(requestedUri, headers: _headers)
          : await put(requestedUri, body: jsonEncode(body), headers: _headers);
      showRequestAndResponseLogs(response);
      switch (response.statusCode) {
        case 200:
        case 201:
          responseData = response.body;
          break;
        case 400:
        case 401:
        case 404:
          jsonDecode(response.body)['error'].toString().showErrorToast();
          break;
        case 422:
          responseData = response.body;
          break;
        case 500:
          logs('${response.statusCode} : ${response.body}');
          break;
        default:
          logs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      errorLogs('PlatformException in postRestCall --> ${e.message}');
    }
    return responseData;
  }

  Future<String> multiPartRestCall({
    required String? endpoint,
    required Map<String, String>? body,
    required List<MultipartFile> fileList,
  }) async {
    String responseData = '';
    bool? isConnected = await ConnectivityHelper.instance.isConnectNetworkWithMessage();
    if (!isConnected) {
      return responseData;
    }
    try {
      String accessToken = await getPrefStringValue(AppPrefConstants.token) ?? '';
      if (accessToken.isNotEmpty) {
        _multiPartHeaders['Authorization'] = 'Bearer $accessToken';
      }

      String requestUrl = '${AppConfig.shared.baseUrl}/$endpoint';
      Uri? requestedUri = Uri.tryParse(requestUrl);
      MultipartRequest request = MultipartRequest('POST', requestedUri!);

      if (body?.isNotEmpty ?? false) {
        request.fields.addAll(body!);
      }
      logs('fileList --> ${fileList.length}');
      if (fileList.isNotEmpty) {
        request.files.addAll(fileList);
      }
      request.headers.addAll(_multiPartHeaders);

      StreamedResponse responseStream = await request.send();
      Response response = await Response.fromStream(responseStream);

      showRequestAndResponseLogs(response);

      switch (response.statusCode) {
        case 200:
        case 201:
          responseData = response.body;
          break;
        case 400:
        case 401:
        case 404:
          jsonDecode(response.body)['error'].toString().showErrorToast();
          break;
        case 500:
        case 502:
          logs('${response.statusCode} : ${response.body}');
          break;
        default:
          logs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      errorLogs('PlatformException in multiPartRestCall --> ${e.message}');
    }
    return responseData;
  }

  Future<Response?> downloadPdfRestCall({required String? endpoint, required String fileName}) async {
    Response? responseData;
    bool? isConnected = await ConnectivityHelper.instance.isConnectNetworkWithMessage();
    if (!isConnected) {
      return responseData;
    }
    try {
      String accessToken = await getPrefStringValue(AppPrefConstants.token) ?? '';
      if (accessToken.isNotEmpty) {
        _headers['Authorization'] = 'Bearer $accessToken';
      }
      String requestUrl =
          '${AppConfig.shared.baseUrl}/$endpoint?fileName=$fileName';

      Uri requestUri = Uri.parse(requestUrl);
      Response response = await get(requestUri, headers: _headers);

      showRequestAndResponseLogs(response);

      switch (response.statusCode) {
        case 200:
        case 201:
          responseData = response;
          break;
        case 400:
        case 401:
        case 404:
          jsonDecode(response.body)['error'].toString().showErrorToast();
          break;
        case 500:
          logs('${response.statusCode}');
          break;
        default:
          logs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      errorLogs('PlatformException in getRestCall --> ${e.message}');
    }
    return responseData;
  }
}
