import 'package:ams/app_imports.dart';

class ValidationHelper {
  ValidationHelper._privateConstructor();

  static final ValidationHelper instance = ValidationHelper._privateConstructor();

  //     ======================= Regular Expressions =======================     //
  String phoneRegExp = r'^[+]?[(]?[0-9]{3}[)]?[-s.]?[0-9]{3}[-s.]?[0-9]{4,6}$';
  String emailRegExp = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  String nameRegExp = r'^[a-zA-Z ]+$';
  String passwordRegexp =
      r'^(?=^.{8,}$)(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{0,}$';

  //     ======================= Validation methods =======================     //
  bool validateEmptyController(TextEditingController textEditingController) {
    return textEditingController.text.trim().isEmpty;
  }

  bool lengthValidator(TextEditingController textEditingController, int length) {
    return textEditingController.text.trim().length < length;
  }

  bool regexValidator(TextEditingController textEditingController, String regex) {
    return !RegExp(regex).hasMatch(textEditingController.text.trim());
  }

  bool compareValidator(
    TextEditingController textEditingController,
    TextEditingController secondController,
  ) {
    return textEditingController.text.trim() != secondController.text.trim();
  }
}
