import 'package:ams/app_imports.dart';

class DateHelper {
  DateHelper._privateConstructor();

  static String getDMMMYYYYFormatDate(DateTime dateTime) {
    DateFormat formatter = DateFormat('d MMM, yy');
    return formatter.format(dateTime).toString();
  }

  static String getDDMMYYYYFormatDate(DateTime dateTime, {String replaceString = '/'}) {
    DateFormat formatter = DateFormat('dd${replaceString}MM${replaceString}yyyy');
    return formatter.format(dateTime).toLowerCase();
  }

  static String getMMDDYYYYFormatDate(DateTime dateTime, {String replaceString = '/'}) {
    DateFormat formatter = DateFormat('MM${replaceString}dd${replaceString}yyyy');
    return formatter.format(dateTime).toLowerCase();
  }

  static String getDDMMYYYYHHMMFormatDate(DateTime dateTime, {String replaceString = '/'}) {
    DateFormat formatter = DateFormat('dd${replaceString}MM${replaceString}yyyy HH:mm');
    return formatter.format(dateTime).toLowerCase();
  }

  static String getDDMMMMYYYYHHMMFormatDate(DateTime dateTime) {
    DateFormat formatter = DateFormat('dd MMMM, yyyy HH:mm');
    return formatter.format(dateTime).toString();
  }

  static String getDDMMMMYYYYDateFormatDate(DateTime dateTime) {
    DateFormat formatter = DateFormat('dd MMMM, yyyy');
    return formatter.format(dateTime).toString();
  }

  static String getMMMMDYYYYFormatDate(DateTime dateTime) {
    DateFormat formatter = DateFormat('MMMM d, yyyy');
    return formatter.format(dateTime).toString();
  }

  static String getYYYYMMDDHHMMSSFormatDate(DateTime dateTime) {
    DateFormat formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
    return formatter.format(dateTime).toString();
  }

  static String getYYYYMMDDFormatDate(DateTime dateTime) {
    DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(dateTime).toString();
  }

  static String getDDMMMMYYYYHHMMAFormatDate(DateTime dateTime) {
    DateFormat formatter = DateFormat('dd MMMM, yyyy hh:mm a');
    return formatter.format(dateTime).toString();
  }

  static String getDDMMMYYYYFormatDate(DateTime dateTime) {
    DateFormat formatter = DateFormat('dd MMM, yyyy');
    return formatter.format(dateTime).toString();
  }

  static String timeToHHMMFormat(TimeOfDay time) {
    if (time.hour < 10 && time.minute < 10) {
      return '0${time.hour}:0${time.minute}';
    } else if (time.minute < 10) {
      return '${time.hour}:0${time.minute}';
    } else if (time.hour < 10) {
      return '0${time.hour}:${time.minute}';
    }
    return '${time.hour}:${time.minute}';
  }
}
