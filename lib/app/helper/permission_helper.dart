import 'package:ams/app_imports.dart';


class PermissionHelper {
  PermissionHelper._privateConstructor();

  static final PermissionHelper instance = PermissionHelper._privateConstructor();

  Future<bool> checkStoragePermission(BuildContext context) async {
    late PermissionStatus status;
    if (Platform.isIOS) {
      status = await Permission.storage.request();
    }
    if (Platform.isAndroid) {
      int? version = await getDeviceVersionDetail();
      if (version != null && version > 32) {
        status = await Permission.photos.request();
      } else {
        status = await Permission.storage.request();
      }
    }
    if (status.isPermanentlyDenied) {
      if (context.mounted) {
        appDialog(
          context: context,
          title: AppStringConstants.permissionRequired.tr,
          buttonText: AppStringConstants.settings.tr,
          buttonTap: () => openAppSettings(),
          subTitle: AppStringConstants.docUploadDescription.tr,
        );
      }
      return false;
    }
    if (status.isGranted) {
      return true;
    }
    return false;
  }

  Future<bool> checkPhotoLibraryPermission(BuildContext context) async {
    late PermissionStatus status;

    if (Platform.isIOS) {
      status = await Permission.photos.request();
    }
    if (Platform.isAndroid) {
      int? version = await getDeviceVersionDetail();
      if (version != null && version > 32) {
        status = await Permission.photos.request();
      } else {
        status = await Permission.storage.request();
      }
    }
    if (status.isPermanentlyDenied) {
      if (context.mounted) {
        appDialog(
          context: context,
          title: AppStringConstants.permissionRequired.tr,
          buttonText: AppStringConstants.settings.tr,
          buttonTap: () => openAppSettings(),
          subTitle: AppStringConstants.imgUploadDescription.tr,
        );
      }
      return false;
    }
    if (status.isGranted) {
      return true;
    }
    return false;
  }

  Future<String> getPath({required BuildContext context}) async {
    String directory = '';
    try {
      bool permissionStatus = false;

      if (Platform.isAndroid) {
        permissionStatus = await checkStoragePermission(context);

        if (permissionStatus) {
          directory = await ExternalPath.getExternalStoragePublicDirectory(ExternalPath.DIRECTORY_DOWNLOADS);
          return directory;
        }
      } else {
        directory = (await getApplicationDocumentsDirectory()).path;
        return directory;
      }
    }on PlatformException catch (e) {
      logs('Error downloading file: $e');
    }
    return directory;
  }

  Future<int?> getDeviceVersionDetail() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      var androidDeviceInfo = await deviceInfo.androidInfo;
      return androidDeviceInfo.version.sdkInt;
    }
    return null;
  }
}
