import 'package:ams/app/routes/app_routes.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

class ConnectivityHelper {
  ConnectivityHelper._privateConstructor();

  static final ConnectivityHelper instance = ConnectivityHelper._privateConstructor();
  final InternetConnectionChecker _internetConnectionChecker = InternetConnectionChecker();
  bool _isConnected = true;

  Future<bool> isConnectNetworkWithMessage() async {
    _isConnected = await _internetConnectionChecker.hasConnection;
    if (!_isConnected){
     gotoNoInternetPage();
    }
    return _isConnected;
  }
}
