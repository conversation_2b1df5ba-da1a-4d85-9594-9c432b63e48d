import 'package:ams/app_imports.dart';

class FilePickerHelper {
  FilePickerHelper._privateConstructor();

  static final FilePickerHelper instance = FilePickerHelper._privateConstructor();

  Future<File?> pickFiles(
    BuildContext context, {
    List<String> allowedExtensions = const ['pdf','jpg'],
    int? fileSizeLimit,
  }) async {
    File? uploadedFile;
    try {
      bool isGranted = await PermissionHelper.instance.checkStoragePermission(context);
      if (isGranted) {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: allowedExtensions,
        );
        if (result != null) {
          logs('path --> ${result.files.first.path}');
          int sizeInBytes = result.files.first.size;
          double sizeInMb = sizeInBytes / (1024 * 1024);
          if (fileSizeLimit != null && sizeInMb > fileSizeLimit) {
            'Please pick up to $fileSizeLimit MB files'.showErrorToast();
          } else if (!allowedExtensions.contains(result.files.first.path!.split('.').last)) {
            AppStringConstants.pleaseSelectValidFileFormat.tr.showErrorToast();
          } else {
            uploadedFile = File(result.files.first.path!);
          }
        }
      }
    } on PlatformException catch (e) {
      errorLogs('PlatformException in pickFiles --> ${e.message}');
    }
    return uploadedFile;
  }

  Future<XFile?> pickImageFromGallery(BuildContext context, {int? fileSizeLimit}) async {
    XFile? imageFile;
    try {
      bool isGranted = await PermissionHelper.instance.checkPhotoLibraryPermission(context);
      if (isGranted) {
        ImagePicker imagePicker = ImagePicker();
        XFile? result = await imagePicker.pickImage(source: ImageSource.gallery);
        if (result != null) {
          int sizeInBytes = await result.length();
          double sizeInMb = sizeInBytes / (1024 * 1024);
          if (fileSizeLimit != null && sizeInMb > fileSizeLimit) {
            'Please pick up to $fileSizeLimit MB image'.showErrorToast();
          } else if (result.path.toString().split('.').last != 'jpg' && result.path.toString().split('.').last != 'png') {
            AppStringConstants.pleaseSelectValidFileFormat.tr.showErrorToast();
          } else {
            imageFile = XFile(result.path);
          }
        }
      }
      return imageFile;
    } on PlatformException catch (e) {
      errorLogs('PlatformException in pickImageFromGallery --> ${e.message}');
    }
    return null;
  }
}
