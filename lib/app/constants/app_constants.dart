import 'package:ams/app_imports.dart';

// ::::::::::::::::::::::::::::::::::::::::::: string constants :::::::::::::::::::::::::::::::::::::::::://

class AppStringConstants {
  static const String appDevName = 'AMS Dev';
  static const String appProdName = 'AMS';
  static const String userName = 'userName';
  static const String password = 'password';
  static const String signIn = 'signIn';
  static const String logIn = 'logIn';
  static const String signUp = 'signUp';
  static const String forgotPassword = 'forgotPassword';
  static const String logInId = 'logInId';
  static const String registeredEmailId = 'registeredEmailId';
  static const String sendSixDigitLetterCode = 'sendSixDigitLetterCode';
  static const String dashboard = 'dashboard';
  static const String applicationFormForAuditingAgency = 'applicationFormForAuditingAgency';
  static const String pleaseEnterLoginID = 'pleaseEnterLoginID';
  static const String pleaseEnterValidEmail = 'pleaseEnterValidEmail';
  static const String pleaseEnterEmail = 'pleaseEnterEmail';
  static const String pleaseEnterYourUserName = 'pleaseEnterYourUserName';
  static const String pleaseEnterValidPassword = 'pleaseEnterValidPassword';
  static const String pleaseEnterPassword = 'pleaseEnterPassword';
  static const String nameOfAuditingAgency = 'nameOfAuditingAgency';
  static const String addressOfHeadOfficeOfCompanyAgency = 'addressOfHeadOfficeOfCompanyAgency';
  static const String pincode = 'pincode';
  static const String state = 'state';
  static const String district = 'district';
  static const String address = 'address';
  static const String selectDistrict = 'selectDistrict';
  static const String notSelected = 'notSelected';
  static const String nameOfContactPersonAsProvided = 'nameOfContactPersonAsProvided';
  static const String nameOfContactPerson = 'nameOfContactPerson';
  static const String mobile = 'mobile';
  static const String mobileNo = 'mobileNo';
  static const String telNo = 'telNo';
  static const String email = 'email';
  static const String enterEmailId = 'enterEmailId';
  static const String website = 'website';
  static const String organizationWebsite = 'organizationWebsite';
  static const String accreditationCertificateNoProvidedByAgency = 'accreditationCertificateNoProvidedByAgency';
  static const String accreditationCertificateNo = 'accreditationCertificateNo';
  static const String validityOfCertificateOfRecognitionIssuedByFssai = 'validityOfCertificateOfRecognitionIssuedByFssai';
  static const String numberOfAuditor = 'numberOfAuditor';
  static const String totalNumberOfAuditors = 'totalNumberOfAuditors';
  static const String fssaiRecognitionNumber = 'fssaiRecognitionNumber';
  static const String legalEntityStatus = 'legalEntityStatus';
  static const String proprietorPartnerLlp = 'proprietorPartnerLlp';
  static const String scopeOfAuditing = 'scopeOfAuditing';
  static const String geographicalAreasWhereTheyCanAudit = 'geographicalAreasWhereTheyCanAudit';
  static const String geographicalAreasWhereAuditCanBeDone = 'geographicalAreasWhereAuditCanBeDone';
  static const String chooseKindOfBusiness = 'chooseKindOfBusiness';
  static const String register = 'register';
  static const String alreadyRegistered = 'alreadyRegistered';
  static const String plzEnterNameOfAuditingAgency = 'plzEnterNameOfAuditingAgency';
  static const String plzEnterAddressOfHeadOfficeOfCompanyAgency = 'plzEnterAddressOfHeadOfficeOfCompanyAgency';
  static const String plzEnterPinCode = 'plzEnterPinCode';
  static const String plzEnterValidPinCode = 'plzEnterValidPinCode';
  static const String plzSelectState = 'plzSelectState';
  static const String plzSelectDistrict = 'plzSelectDistrict';
  static const String plzEnterNameOfContactPerson = 'plzEnterNameOfContactPerson';
  static const String plzEnterMobileNumber = 'plzEnterMobileNumber';
  static const String plzEnterValidMobileNumber = 'plzEnterValidMobileNumber';
  static const String plzEnterTellNumber = 'plzEnterTellNumber';
  static const String plzEnterValidTellNumber = 'plzEnterValidTellNumber';
  static const String plzEnterEmail = 'plzEnterEmail';
  static const String plzEnterValidEmail = 'plzEnterValidEmail';
  static const String plzEnterAccreditationCertificateNo = 'plzEnterAccreditationCertificateNo';
  static const String plzPickValidityOfCertificateOfRecognitionDate = 'plzPickValidityOfCertificateOfRecognitionDate';
  static const String plzEnterNumOfAuditor = 'plzEnterNumOfAuditor';
  static const String plzEnterFssaiRecognitionNumber = 'plzEnterFssaiRecognitionNumber';
  static const String plzEnterLegalEntityStatus = 'plzEnterLegalEntityStatus';
  static const String plzEnterScopeOfAuditing = 'plzEnterScopeOfAuditing';
  static const String plzEnterGeographicalAreasWhereTheyCanAudit = 'plzEnterGeographicalAreasWhereTheyCanAudit';
  static const String legalEntityStatusHint = 'legalEntityStatusHint';
  static const String recognitionNumHint = 'recognitionNumHint';
  static const String successToast = 'successToast';
  static const String failedToast = 'failedToast';
  static const String requiredToast = 'requiredToast';
  static const String plzChooseOneKOB = 'plzChooseOneKOB';
  static const String chooseKOB = 'chooseKOB';
  static const String logInHere = 'logInHere';
  static const String uploadDocument = 'uploadDocument';
  static const String allDocAreMandatoryExpectNo6 = 'allDocAreMandatoryExpectNo6';
  static const String certificateOfAccereditation = 'certificateOfAccereditation';
  static const String confidentialityAgreement = 'confidentialityAgreement';
  static const String aCopyOfTheLastAssessmentReport = 'aCopyOfTheLastAssessmentReport';
  static const String leadAuditorCourseCertificate = 'leadAuditorCourseCertificate';
  static const String legalEnterStatus = 'legalEnterStatus';
  static const String anyOtherDocument = 'anyOtherDocument';
  static const String chooseFile = 'chooseFile';
  static const String noFileChoosen = 'noFileChoosen';
  static const String uploadAndSave = 'uploadAndSave';
  static const String pressHereToUploadFile = 'pressHereToUploadFile';
  static const String pleaseSelectValidFileFormat = 'pleaseSelectValidFileFormat';
  static const String cancel = 'cancel';
  static const String permissionRequired = 'permissionRequired';
  static const String settings = 'settings';
  static const String imgUploadDescription = 'imgUploadDescription';
  static const String docUploadDescription = 'docUploadDescription';
  static const String applicationReceived = 'applicationReceived';
  static const String auditsDone = 'auditsDone';
  static const String empaneledAgencies = 'empaneledAgencies';
  static const String rejectedAgencies = 'rejectedAgencies';
  static const String enlistedAuditors = 'enlistedAuditors';
  static const String changePassword = 'changePassword';
  static const String logOut = 'logOut';
  static const String reports = 'reports';
  static const String yourAuditors = 'yourAuditors';
  static const String pendingWithAuditor = 'pendingWithAuditor';
  static const String approved = 'approved';
  static const String cancelled = 'cancelled';
  static const String nonCompliance = 'nonCompliance';
  static const String copy = 'copy';
  static const String csv = 'csv';
  static const String pdf = 'pdf';
  static const String excel = 'excel';
  static const String print = 'print';
  static const String search = 'search';
  static const String select = 'select';
  static const String sortBy = 'sortBy';
  static const String fromDate = 'fromDate';
  static const String toDate = 'toDate';
  static const String submit = 'submit';
  static const String starSign = '*';
  static const String personalInfo = 'personalInfo';
  static const String registerAuditor = 'registerAuditor';
  static const String assignAudit = 'assignAudit';
  static const String areYourSureWantToLogout = 'areYourSureWantToLogout';
  static const String yes = 'yes';
  static const String yourAuditor = 'yourAuditor';
  static const String auditors = 'auditors';
  static const String srNo = 'srNo';
  static const String applicationSubmittedSuccessfully = 'applicationSubmittedSuccessfully';
  static const String yourUsernameIs = 'yourUsernameIs';
  static const String yourPasswordIs = 'yourPasswordIs';
  static const String and = 'and';
  static const String auditApproved = 'auditApproved';
  static const String approvalPending = 'approvalPending';
  static const String auditorName = 'auditorName';
  static const String auditorDetails = 'auditorDetails';
  static const String auditAgencyInfo = 'auditAgencyInfo';
  static const String name = 'name';
  static const String agencyName = 'agencyName';
  static const String documentAttached = 'documentAttached';
  static const String emailAndMobile = 'emailAndMobile';
  static const String auditsConducted = 'auditsConducted';
  static const String experience = 'experience';
  static const String qualification = 'qualification';
  static const String removeAuditor = 'removeAuditor';
  static const String leadAuditorCertificateEducationalQualificationCertificate =
      'leadAuditorCertificateEducationalQualificationCertificate';
  static const String oops = 'oops';
  static const String tryAgain = 'tryAgain';
  static const String passwordChange = 'passwordChange';
  static const String enterOldPassword = 'enterOldPassword';
  static const String enterNewPassword = 'enterNewPassword';
  static const String enterConfirmPassword = 'enterConfirmPassword';
  static const String pleaseEnterOldPassword = 'pleaseEnterOldPassword';
  static const String pleaseEnterNewPassword = 'pleaseEnterNewPassword';
  static const String pleaseEnterConfirmPassword = 'pleaseEnterConfirmPassword';
  static const String confirmPasswordDoesNotMatch = 'confirmPasswordDoesNotMatch';
  static const String pleaseEnterPasswordLength = 'pleaseEnterPasswordLength';
  static const String status = 'status';
  static const String remarks = 'remarks';
  static const String viewDetails = 'viewDetails';
  static const String verifyReject = 'verifyReject';
  static const String agencyDetails = 'agencyDetails';
  static const String agencyInfo = 'agencyInfo';
  static const String certificateNumber = 'certificateNumber';
  static const String validUpTo = 'validUpTo';
  static const String legalEntryStatus = 'legalEntryStatus';
  static const String recognitionNumber = 'recognitionNumber';
  static const String certificateOfAccreditation = 'certificateOfAccreditation';
  static const String aCopyOfTheLastAssessmentReportByTheAccreditationBody = 'aCopyOfTheLastAssessmentReportByTheAccreditationBody';
  static const String kindOfBusiness = 'kindOfBusiness';
  static const String idNo = 'idNo';
  static const String reportNo = 'reportNo';
  static const String licenseNo = 'licenseNo';
  static const String contactPerson = 'contactPerson';
  static const String fboName = 'fboName';
  static const String kob = 'kob';
  static const String date = 'date';
  static const String report = 'report';
  static const String stateDistrict = 'stateDistrict';
  static const String approvedAuditReports = 'approvedAuditReports';
  static const String viewDetail = 'viewDetail';
  static const String view = 'view';
  static const String agency = 'agency';
  static const String uploadFollowUpReport = 'uploadFollowUpReport';
  static const String followUpReport = 'followUpReport';
  static const String close = 'close';
  static const String plzSelectFile = 'plzSelectFile';
  static const String qualificationDetailsOf = 'qualificationDetailsOf';
  static const String qualificationCertificateName = 'qualificationCertificateName';
  static const String passingYear = 'passingYear';
  static const String boardUniversity = 'boardUniversity';
  static const String percentageGrade = 'percentageGrade';
  static const String subject = 'subject';
  static const String organizationName = 'organizationName';
  static const String from = 'from';
  static const String to = 'to';
  static const String experienceInMonth = 'experienceInMonth';
  static const String verifyRejectAgency = 'verifyRejectAgency';
  static const String enterRemarks = 'enterRemarks';
  static const String leadAuditorCertificate = 'leadAuditorCertificate';
  static const String educationalQualificationCertificate = 'educationalQualificationCertificate';
  static const String auditorLog = 'auditorLog';
  static const String sectorSpecificKnowledge = 'sectorSpecificKnowledge';
  static const String designation = 'designation';
  static const String noDataAvailable = 'noDataAvailable';
  static const String experienceDetailsOf = 'experienceDetailsOf';
  static const String auditStartDate = 'auditStartDate';
  static const String auditEndDate = 'auditEndDate';
  static const String auditSubmissionDate = 'auditSubmissionDate';
  static const String auditReport = 'auditReport';
  static const String fssaiThirdPartyAudits = 'fssaiThirdPartyAudits';
  static const String auditorContactNo = 'auditorContactNo';
  static const String fboRepresentativeName = 'fboRepresentativeName';
  static const String fboFssaiLicenseNo = 'fboFssaiLicenseNo';
  static const String companyName = 'companyName';
  static const String majorNonConformity = 'majorNonConformity';
  static const String minorNonConformity = 'minorNonConformity';
  static const String comments = 'comments';
  static const String filesUploaded = 'filesUploaded';
  static const String auditorAgreementFile = 'auditorAgreementFile';
  static const String confidentialityFile = 'confidentialityFile';
  static const String other = 'other';
  static const String question = 'question';
  static const String maxPoints = 'maxPoints';
  static const String pointsScored = 'pointsScored';
  static const String fileUploaded = 'fileUploaded';
  static const String docCouldNotOpen = 'docCouldNotOpen';
  static const String applicationsReceivedForApproval = 'applicationsReceivedForApproval';
  static const String requiredQuestionNote = 'requiredQuestionNote';
  static const String submitDate = 'submitDate';
  static const String dateOfRequest = 'dateOfRequest';
  static const String auditReportsCancelledByAgency = 'auditReportsCancelledByAgency';

  static const String approveRejectAgency = 'approveRejectAgency';
  static const String approve = 'Approve';
  static const String reject = 'Reject';
  static const String approvedEmpanelledAgencies = 'approvedEmpanelledAgencies';
  static const String audits = 'audits';
  static const String auditingScope = 'auditingScope';
  static const String actions = 'actions';
  static const String agencyStatus = 'agencyStatus';
  static const String geographicalArea = 'geographicalArea';
  static const String deActivate = 'deActivate';
  static const String activate = 'Activate';
  static const String edit = 'edit';
  static const String representativeName = 'representativeName';
  static const String auditInspectionPending = 'auditInspectionPending';
  static const String conductAuditing = 'conductAuditing';
  static const String validUpto = 'validUpto';
  static const String copyOfLastAssessmentReport = 'copyOfLastAssessmentReport';
  static const String geographicalAreas = 'geographicalAreas';
  static const String verifyLicense = 'verifyLicense';
  static const String enterLicenseNumber = 'enterLicenseNumber';
  static const String uploadInspectionReport = 'uploadInspectionReport';
  static const String licenseNumber = 'licenseNumber';
  static const String fbo = 'fbo';
  static const String auditorsName = 'auditorsName';
  static const String stateUtSponsoredAudit = 'stateUtSponsoredAudit';
  static const String districtState = 'districtState';
  static const String auditor = 'auditor';
  static const String submissionDate = 'submissionDate';
  static const String requestNo = 'requestNo';
  static const String requestDate = 'requestDate';
  static const String checkValidity = 'checkValidity';
  static const String auditAgency = 'auditAgency';
  static const String auditedBy = 'auditedBy';
  static const String numOfFoodHandlers = 'numOfFoodHandlers';
  static const String numOfTrainedFoodSafetySupervisor = 'numOfTrainedFoodSafetySupervisor';
  static const String plzSelectKob = 'plzSelectKob';
  static const String plzEnterNumOfFoodHandlers = 'plzEnterNumOfFoodHandlers';
  static const String plzEnterNumOfTrainedFoodSafetySupervisor = 'plzEnterNumOfTrainedFoodSafetySupervisor';
  static const String plzVerifyLicense = 'plzVerifyLicense';
  static const String proceed = 'proceed';
  static const String auditReportToBeSubmittedByAuditor = 'auditReportToBeSubmittedByAuditor';
  static const String expiryDate = 'expiryDate';
  static const String plzSelectAuditStartDate = 'plzSelectAuditStartDate';
  static const String plzSelectAuditEndDate = 'plzSelectAuditEndDate';
  static const String note = 'note';
  static const String uploadFileNote = 'uploadFileNote';
  static const String scoreErrorOfQuestion = 'scoreErrorOfQuestion';
  static const String scoreErrorOfMandatoryQuestion = 'scoreErrorOfMandatoryQuestion';
  static const String file = 'file';
  static const String auditorAgreement = 'auditorAgreement';
  static const String confidentiality = 'confidentiality';
  static const String othersIfAny = 'othersIfAny';
  static const String infoTrainFssaiGovSays = 'infoTrainFssaiGovSays';
  static const String agencyActivateString = 'agencyActivateString';
  static const String agencyDeActivateString = 'agencyDeActivateString';
  static const String scopeOfAudition = 'scopeOfAudition';
  static const String updateAgencyDetails = 'updateAgencyDetails';
  static const String ok = 'ok';
  static const String update = 'update';
  static const String selectState = 'selectState';
  static const String nameOfAuditor = 'nameOfAuditor';
  static const String fatherNameOfAuditor = 'fatherNameOfAuditor';
  static const String degree = 'degree';
  static const String collegeUniversity = 'collegeUniversity';
  static const String subjectStream = 'subjectStream';
  static const String qualificationDetail = 'qualificationDetail';
  static const String addMore = 'addMore';
  static const String experienceDetail = 'experienceDetail';
  static const String remove = 'remove';
  static const String attachDocuments = 'attachDocuments';
  static const String educationQualification = 'educationQualification';
  static const String auditLog = 'auditLog';
  static const String auditModification = 'auditModification';
  static const String viewReport = 'viewReport';
  static const String viewFile = 'viewFile';
  static const String otherFile = 'otherFile';
  static const String fileWillBeRemovedPermanently = 'fileWillBeRemovedPermanently';
  static const String narration = 'narration';
  static const String deleteFile = 'deleteFile';
  static const String updateFile = 'updateFile';
  static const String updateNarration = 'updateNarration';
  static const String auditReportCancelled = 'auditReportCancelled';
  static const String category = 'category';
  static const String maxScore = 'maxScore';
  static const String marksScored = 'marksScored';
  static const String rating = 'rating';
  static const String auditDate = 'auditDate';
  static const String compliance = 'compliance';
  static const String auditReportData = 'auditReportData';
  static const String all = 'all';
  static const String filter = 'filter';
  static const String infotrainSays = 'infotrainSays';
  static const String auditRequestCancellationMessage = 'auditRequestCancellationMessage';
  static const String lessThan = 'lessThan';
  static const String greaterThan = 'greaterThan';
  static const String equalTo = 'equalTo';
  static const String lessThanEqualTo = 'lessThanEqualTo';
  static const String greaterThanEqualTo = 'greaterThanEqualTo';
  static const String totalPoints = 'totalPoints';
  static const String needsImprovement = 'needsImprovement';
  static const String pointsAndCompliance = 'pointsAndCompliance';
  static const String sendBack = 'sendBack';
  static const String previewReport = 'previewReport';
  static const String show = 'show';
  static const String pleaseEnterAuditorName = 'pleaseEnterAuditorName';
  static const String pleaseEnterFatherName = 'pleaseEnterFatherName';
  static const String pleaseEnterAddress = 'pleaseEnterAddress';
  static const String pleaseEnterPinCodeNumber = 'pleaseEnterPinCodeNumber';
  static const String pleaseChooseFile = 'pleaseChooseFile';
  static const String pleaseEnterMobileNumber = 'pleaseEnterMobileNumber';
  static const String pleaseEnterExperienceInMonth = 'pleaseEnterExperienceInMonth';
  static const String pleaseSelectToDate = 'pleaseSelectToDate';
  static const String pleaseSelectFromDate = 'pleaseSelectFromDate';
  static const String pleaseEnterDesignation = 'pleaseEnterDesignation';
  static const String pleaseEnterOrganizationName = 'pleaseEnterOrganizationName';
  static const String pleaseEnterGraduation = 'pleaseEnterGraduation';
  static const String pleaseEnterStream = 'pleaseEnterStream';
  static const String pleaseEnterCollegeOrUniversityName = 'pleaseEnterCollegeOrUniversityName';
  static const String pleaseEnterPassingYear = 'pleaseEnterPassingYear';
  static const String pleaseEnterPercentageOrGrade = 'pleaseEnterPercentageOrGrade';
  static const String pleaseSelectState = 'pleaseSelectState';
  static const String pleaseSelectDistrict = 'pleaseSelectDistrict';
  static const String detailsUpdated = 'detailsUpdated';
  static const String sixDigitCodeHasBeenSentToTheEmail = 'sixDigitCodeHasBeenSentToTheEmail';
  static const String enterSixDigitCode = 'enterSixDigitCode';
  static const String reEnterPassword = 'reEnterPassword';
  static const String bothPasswordShouldBeSame = 'bothPasswordShouldBeSame';
  static const String pleaseEnterSixDigitCode = 'pleaseEnterSixDigitCode';
  static const String theCodeShouldBeSixLetterLong = 'theCodeShouldBeSixLetterLong';
  static const String yourPasswordIsResetSuccessfully = 'yourPasswordIsResetSuccessfully';
  static const String passwordContainsOneUpperCase = 'passwordContainsOneUpperCase';
  static const String passwordContainsOneDigit = 'passwordContainsOneDigit';
  static const String passwordContainsOneSpecialCharacter = 'passwordContainsOneSpecialCharacter';
  static const String passwordShouldBe7To12LetterLong = 'passwordShouldBe7To12LetterLong';
  static const String enterUsername = 'enterUsername';
  static const String pleaseEnterUsername = 'pleaseEnterUsername';
  static const String passwordChangeSuccessfully = 'passwordChangeSuccessfully';
  static const String incorrectUsernameOrPassword = 'incorrectUsernameOrPassword';
  static const String logOutSuccessMsg = 'logOutSuccessMsg';
  static const String logInSuccessMsg = 'logInSuccessMsg';

  //::::::::::::::::::::::::::::::::::::::::: Non Localization Strings :::::::::::::::::::::::::::::::::::::::://
  static const String mmDdYyyy = 'MM/DD/YYYY';
  static const String internetConnectionNotFoundCheckTheConnection = 'Internet Connection Not Found Check The Connection';
  static const String auditReportSubmitted = 'Audit Reports Submitted By';
  static const String ddMmYyyy = 'dd/mm/yyyy';
  static const String inspectionChecklistFor = 'INSPECTION CHECKLIST FOR';
  static const String na = 'NA';
  static const String pendingStatus = 'Pending';
  static const String rejectedStatus = 'Rejected';
  static const String approvedStatus = 'Approved';
  static const String registerAuditorNoteString = 'While registering Auditors, you will be asked toupload PDF files (2MB) of (1) Lead auditor certificate(2) Education qualification of Auditors (3) Audit log and(3) Sector specific knowledge,without which, Auditors cannot be registered.';
  static const String active = 'Active';
  static const String deActive = 'Deactive';
}

// ::::::::::::::::::::::::::::::::::::::::::: color constants :::::::::::::::::::::::::::::::::::::::::://
class AppColorConstants {
  static const Color colorBlack = Color(0xff000000);
  static const Color colorWhite = Color(0xffFFFFFF);
  static const Color colorRedAccent = Colors.redAccent;
  static const Color colorTransparent = Colors.transparent;
  static const Color colorWhite100 = Color(0xFFFCFAFF);
  static const Color colorWhite200 = Color(0xffF2F2F2);
  static const Color colorAppPrimary = Color(0xff3C3C6A);
  static const Color colorRed = Colors.red;
  static const Color colorGrey = Color(0xffB0B0B0);
  static const Color colorDarkGrey = Color(0xff4D4D4D);
  static const Color colorMediumGrey = Color(0xff757575);
  static const Color colorBlue = Color(0xff337AB7);
  static const Color colorPastelGreen = Color(0xff63E37F);
  static const Color colorCoralRed = Color(0xffF35C5E);
  static const Color colorWhiteLightGreen = Color(0xffF1FFF6);
  static const Color colorLinen = Color(0xffFAEFEB);
  static const Color colorVividOrange = Color(0xffFF9E40);
  static const Color colorBisque = Color(0xffFFF6ED);
  static const Color colorLightGrey = Color(0xFFEFEFEF);
  static const Color colorYellow = Color(0xffF0AD4E);
  static const Color colorLightGreen = Color(0xff5CB85C);
  static const Color colorLightRed = Color(0xffD9534F);
  static const Color colorLightPurple = Color(0xffECECF1);
  static const Color colorSkyBlue = Color(0xff5BC0DE);
  static const Color colorLightGrey300 = Color(0xffABABAB);
  static const Color colorLightYellow100 = Color(0xffF9F9F9);
  static const Color colorYellowShade400 = Color(0xfff1ad4f);
  static const Color colorPeach = Color(0xffFFE6CE);
  static const Color colorLightGrey500 = Color(0xff8D8D8D);
  static const Color colorLightGrey100 = Color(0xffeeeeee);
  static const Color colorLightPurple100 = Color(0xffC5C5D3);
  static const Color colorTeal = Color(0xff3E929E);
}

// ::::::::::::::::::::::::::::::::::::::::::: app assets :::::::::::::::::::::::::::::::::::::::::://

class AppAssetsConstants {
  AppAssetsConstants._privateConstructor();

  static const String defaultFont = 'DinRound';
  static const String imagePath = 'assets/images/';
  static const String iconPath = 'assets/icons/';
  static const String loaderAnimation = 'assets/animation/loader_animation.json';
  static const String configPath = 'assets/data/app_config.json';

  //====================== Images =======================//
  static String splashBgImage = '${AppAssetsConstants.imagePath}img_splash_bg.png';
  static String splashLogo = '${AppAssetsConstants.imagePath}img_app_logo.png';
  static String fssaiImage = '${AppAssetsConstants.imagePath}img_fssai.png';
  static String auditManagementImage = '${AppAssetsConstants.imagePath}img_audit_management.png';
  static String agencyDrawerProfile = '${AppAssetsConstants.imagePath}img_agency_drawer_profile.png';
  static String imgPersonalInfo = '${AppAssetsConstants.imagePath}img_personal_info.svg';
  static String imgAgencyInfo = '${AppAssetsConstants.imagePath}img_agency_info.svg';
  static String imgNoInternet = '${AppAssetsConstants.imagePath}img_no_internet.png';
  static String imgNoDataFound = '${AppAssetsConstants.imagePath}img_no_data_found.png';

//====================== Icons =======================//

  static String icMail = '${AppAssetsConstants.iconPath}ic_mail.svg';
  static String icLock = '${AppAssetsConstants.iconPath}ic_lock.svg';
  static String icMenu = '${AppAssetsConstants.iconPath}ic_menu.svg';
  static String icBack = '${AppAssetsConstants.iconPath}ic_back.svg';
  static String icCloseDropdown = '${AppAssetsConstants.iconPath}ic_close_dropdown.svg';
  static String icOpenDropdown = '${AppAssetsConstants.iconPath}ic_open_dropdown.svg';
  static String icCalender = '${AppAssetsConstants.iconPath}ic_calendar.svg';
  static String icSuccessToast = '${AppAssetsConstants.iconPath}ic_success_toast.svg';
  static String icErrorToast = '${AppAssetsConstants.iconPath}ic_error_toast.svg';
  static String icRequiredToast = '${AppAssetsConstants.iconPath}ic_required_toast.svg';
  static String icCancel = '${AppAssetsConstants.iconPath}ic_cancel.svg';
  static String icPersons = '${AppAssetsConstants.iconPath}ic_persons.svg';
  static String icSteeringWheel = '${AppAssetsConstants.iconPath}ic_steering_wheel.svg';
  static String icThumbUp = '${AppAssetsConstants.iconPath}ic_thumb_up.svg';
  static String icHome = '${AppAssetsConstants.iconPath}ic_home.svg';
  static String icCopy = '${AppAssetsConstants.iconPath}ic_copy.svg';
  static String icCSV = '${AppAssetsConstants.iconPath}ic_csv.svg';
  static String icExcel = '${AppAssetsConstants.iconPath}ic_excel.svg';
  static String icPdf = '${AppAssetsConstants.iconPath}ic_pdf.svg';
  static String icPrint = '${AppAssetsConstants.iconPath}ic_print.svg';
  static String icSearch = '${AppAssetsConstants.iconPath}ic_search.svg';
  static String icApplicationReceived = '${AppAssetsConstants.iconPath}ic_application_received.svg';
  static String icRejected = '${AppAssetsConstants.iconPath}ic_rejected.svg';
  static String icSuperAdminDashboard = '${AppAssetsConstants.iconPath}ic_super_admin_dashboard.svg';
  static String icSuperAdminAuditsDone = '${AppAssetsConstants.iconPath}ic_super_admin_audits_done.svg';
  static String icSuperAdminEmpaneledAgencies = '${AppAssetsConstants.iconPath}ic_super_admin_empaneled_agencies.svg';
  static String icSuperAdminEnlistedAuditors = '${AppAssetsConstants.iconPath}ic_super_admin_enlisted_auditors.svg';
  static String icDrawerDashboard = '${AppAssetsConstants.iconPath}ic_drawer_dashboard.svg';
  static String icChangePassword = '${AppAssetsConstants.iconPath}ic_change_password.svg';
  static String icLogOut = '${AppAssetsConstants.iconPath}ic_log_out.svg';
  static String icSuperAdminReports = '${AppAssetsConstants.iconPath}ic_super_admin_reports.svg';
  static String icApproved = '${AppAssetsConstants.iconPath}ic_approved.svg';
  static String icPeople = '${AppAssetsConstants.iconPath}ic_people.svg';
  static String icProfile = '${AppAssetsConstants.iconPath}ic_profile.svg';
  static String icRegister = '${AppAssetsConstants.iconPath}ic_register.svg';
  static String icPersonApproved = '${AppAssetsConstants.iconPath}ic_person_approved.svg';
  static String icUpDownArrow = '${AppAssetsConstants.iconPath}ic_updown_arrow.svg';
  static String icDownUpArrow = '${AppAssetsConstants.iconPath}ic_downup_arrow.svg';
  static String icPersonalInfo = '${AppAssetsConstants.iconPath}ic_personal_info.svg';
  static String icDelete = '${AppAssetsConstants.iconPath}ic_delete.svg';
  static String icEye = '${AppAssetsConstants.iconPath}ic_eye.svg';
  static String icEdit = '${AppAssetsConstants.iconPath}ic_edit.svg';
  static String icForward = '${AppAssetsConstants.iconPath}ic_forward.svg';
  static String icDownloadArrow = '${AppAssetsConstants.iconPath}ic_download_arrow.svg';
  static String icDownload = '${AppAssetsConstants.iconPath}ic_download.svg';
  static String icPhone = '${AppAssetsConstants.iconPath}ic_phone.svg';
  static String icPerson = '${AppAssetsConstants.iconPath}ic_person.svg';
}
