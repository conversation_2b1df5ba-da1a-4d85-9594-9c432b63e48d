import 'package:ams/app_imports.dart';

class CommonAppBar extends PreferredSize {
  final String title;
  final String? subTitle;
  final FontWeight titleWright;
  final double titleSize;
  final Widget? leading;
  final double appBarHeight;
  final List<Widget>? actions;
  final PreferredSizeWidget? bottomWidget;
  final String? searchPlaceHolder;
  final bool isBack;
  final GestureTapCallback? backOnTap;

  CommonAppBar({
    required this.title,
    super.key,
    this.subTitle,
    this.appBarHeight = 56,
    this.titleWright = FontWeight.w700,
    this.titleSize = 16,
    this.leading,
    this.actions,
    this.bottomWidget,
    this.searchPlaceHolder,
    this.isBack = true,
    this.backOnTap,
    Widget? child,
    Size? preferredSize,
  }) : super(
          child: Container(),
          preferredSize: Size.fromHeight(appBarHeight),
        );

  @override
  Widget build(BuildContext context) {
    return AppBar(
      toolbarHeight: appBarHeight,
      automaticallyImplyLeading: false,
      leading: leading,
      shape: const Border(
        bottom: BorderSide(
          color: AppColorConstants.colorDarkGrey,
        ),
      ),
      title: Padding(
        padding: const EdgeInsets.only(top: 15, bottom: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (isBack) ...[
                  InkWell(
                    onTap: backOnTap ?? () => gotoBack(),
                    child: Padding(
                      padding: EdgeInsets.only(
                        bottom: (subTitle != null && subTitle!.isNotEmpty) ? 20 : 0,
                        right: 15,
                      ),
                      child: AppImageAsset(
                        image: AppAssetsConstants.icBack,
                        height: 22,
                        width: 22,
                      ),
                    ),
                  ),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      AppText(
                        title,
                        fontWeight: titleWright,
                        fontSize: titleSize,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (subTitle != null && subTitle!.isNotEmpty) ...[
                        const SizedBox(height: 5),
                        AppText(
                          subTitle!,
                          fontWeight: FontWeight.w300,
                          fontSize: 12,
                          maxLines: 1,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      backgroundColor: AppColorConstants.colorWhite,
      surfaceTintColor: AppColorConstants.colorWhite,
      bottom: bottomWidget,
      actions: actions,
    );
  }
}
