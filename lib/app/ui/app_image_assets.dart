import 'package:ams/app_imports.dart';
// ignore_for_file: deprecated_member_use

class AppImageAsset extends StatelessWidget {
  final String? image;
  final double? height;
  final double? width;
  final Color? color;
  final BoxFit? fit;
  final bool isFile;
  final Widget? errorWidget;

  const AppImageAsset({
    @required this.image,
    super.key,
    this.fit = BoxFit.cover,
    this.height,
    this.width,
    this.color,
    this.isFile = false,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return (image?.contains('json') ?? false)
        ? Lottie.asset(image!, width: width, height: height, fit: fit)
        : image!.contains('http')
            ? CachedNetworkImage(
                imageUrl: '$image',
                height: height,
                width: width,
                fit: fit ?? BoxFit.cover,
                placeholder: (context, url) => AppShimmerEffectView(
                  height: height ?? double.maxFinite,
                  width: width ?? double.maxFinite,
                ),
                errorWidget: (context, url, error) =>
                    errorWidget ?? const Icon(Icons.error, color: AppColorConstants.colorRedAccent),
              )
            : isFile
                ? Image.file(File(image!), fit: fit, height: height, width: width, color: color)
                : image!.isEmpty || image!.split('.').last != 'svg'
                    ? Image.asset(
                        image!,
                        fit: fit,
                        height: height,
                        width: width,
                        color: color,
                        errorBuilder: (context, url, error) => errorWidget ?? const SizedBox(),
                      )
                    : SvgPicture.asset(
                        image!,
                        height: height,
                        width: width,
                        color: color,
                      );
  }
}
