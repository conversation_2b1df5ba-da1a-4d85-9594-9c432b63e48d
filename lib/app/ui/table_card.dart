import 'package:ams/app_imports.dart';

Widget tableCard({
  required List<Widget> children,
  Color? headerColor,
  ScrollPhysics? physics,
  double? headerSize,
  TextEditingController? searchController,
  TextEditingController? fromDateController,
  TextEditingController? toDateController,
  Color? bannerColor,
  String? dropdownValue,
  ValueChanged? dropDownOnChanged,
  ValueChanged<String>? searchOnChanged,
  List<String>? dropdownList,
  bool isAscending = false,
  bool isShowSearch = false,
  bool isShowOptions = false,
  bool isShowDropdown = false,
  bool isDateRangeShow = false,
  GestureTapCallback? sortOnTap,
  GestureTapCallback? copyOnTap,
  GestureTapCallback? excelOnTap,
  GestureTapCallback? pdfOnTap,
  GestureTapCallback? submitDateOnTap,
  GestureTapCallback? clearSearchOnTap,
  GestureTapCallback? csvOnTap,
  GestureTapCallback? printOnTap,
  GestureTapCallback? fromDateOnTap,
  GestureTapCallback? toDateOnTap,
  EdgeInsetsGeometry? tableCarPadding,
  bool isMainListEmpty = false,
}) {
  return Padding(
    padding: tableCarPadding ?? const EdgeInsets.all(15),
    child: Container(
      decoration: BoxDecoration(
        border: Border.all(color: bannerColor ?? AppColorConstants.colorAppPrimary),
        borderRadius: BorderRadius.circular(10),
      ),
      width: double.infinity,
      child: Column(
        children: [
          isMainListEmpty ? const Expanded(child: NoDataFoundView()) : Expanded(
            child: ListView(
              shrinkWrap: true,
              physics: physics,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
              children: [
                isDateRangeShow
                    ? Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AppText(
                                      AppStringConstants.fromDate.tr,
                                      fontSize: 12,
                                    ),
                                    const AppText(
                                      AppStringConstants.starSign,
                                      fontSize: 15,
                                      color: AppColorConstants.colorRed,
                                    ),
                                  ],
                                ),
                                AppTextFormField(
                                  readOnly: true,
                                  border: const OutlineInputBorder(
                                    borderSide: BorderSide(color: AppColorConstants.colorAppPrimary, width: 0.5),
                                  ),
                                  onTap: fromDateOnTap,
                                  hintTextSize: 12,
                                  textFieldFontSize: 12,
                                  hintText: AppStringConstants.ddMmYyyy,
                                  textFieldHeight: 25,
                                  textFieldColor: AppColorConstants.colorLightPurple,
                                  controller: fromDateController,
                                  suffixIcon: Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                                    child: AppImageAsset(
                                      image: AppAssetsConstants.icCalender,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            width: 15,
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AppText(
                                      AppStringConstants.toDate.tr,
                                      fontSize: 12,
                                    ),
                                    const AppText(
                                      AppStringConstants.starSign,
                                      fontSize: 15,
                                      color: AppColorConstants.colorRed,
                                    ),
                                  ],
                                ),
                                AppTextFormField(
                                  readOnly: true,
                                  border: const OutlineInputBorder(
                                    borderSide: BorderSide(color: AppColorConstants.colorAppPrimary, width: 0.5),
                                  ),
                                  onTap: toDateOnTap,
                                  hintTextSize: 12,
                                  textFieldFontSize: 12,
                                  hintText: AppStringConstants.ddMmYyyy,
                                  textFieldHeight: 25,
                                  textFieldColor: AppColorConstants.colorLightPurple,
                                  controller: toDateController,
                                  suffixIcon: Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                                    child: AppImageAsset(
                                      image: AppAssetsConstants.icCalender,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      )
                    : const SizedBox(),
                SizedBox(
                  height: isDateRangeShow ? 15 : 0,
                ),
                isDateRangeShow
                    ? InkWell(
                        onTap: submitDateOnTap,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 3),
                          decoration: BoxDecoration(
                            color: AppColorConstants.colorLightRed,
                            borderRadius: BorderRadiusDirectional.circular(4),
                          ),
                          child: Center(
                            child: AppText(
                              color: AppColorConstants.colorWhite,
                              AppStringConstants.submit.tr,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      )
                    : const SizedBox(),
                SizedBox(
                  height: isDateRangeShow ? 15 : 0,
                ),
                isShowOptions
                    ? SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            commonWidget(icon: AppAssetsConstants.icCopy, title: AppStringConstants.copy.tr, onTap: copyOnTap),
                            const SizedBox(width: 8),
                            commonWidget(icon: AppAssetsConstants.icExcel, title: AppStringConstants.excel.tr, onTap: excelOnTap),
                            const SizedBox(width: 8),
                            commonWidget(icon: AppAssetsConstants.icCSV, title: AppStringConstants.csv.tr, onTap: csvOnTap),
                            const SizedBox(width: 8),
                            commonWidget(icon: AppAssetsConstants.icPdf, title: AppStringConstants.pdf.tr, onTap: pdfOnTap),
                            const SizedBox(width: 8),
                            commonWidget(icon: AppAssetsConstants.icPrint, title: AppStringConstants.print.tr, onTap: printOnTap),
                          ],
                        ),
                      )
                    : const SizedBox(),
                SizedBox(
                  height: isShowOptions ? 15 : 0,
                ),
                isShowSearch
                    ? AppTextFormField(
                        border: const OutlineInputBorder(
                          borderSide: BorderSide(color: AppColorConstants.colorAppPrimary, width: 0.5),
                        ),
                        hintColor: AppColorConstants.colorBlack,
                        hintTextSize: 12,
                        suffixIcon: (searchController?.text.isNotEmpty ?? false)
                            ? InkWell(
                          onTap: clearSearchOnTap,
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 8, left: 10, bottom: 8),
                                  child: AppImageAsset(image: AppAssetsConstants.icCancel),
                                ),
                              )
                            : null,
                        textFieldFontSize: 12,
                        contentPadding: EdgeInsets.zero,
                        controller: searchController,
                        hintText: AppStringConstants.search.tr,
                        textFieldHeight: 25,
                        onChanged: searchOnChanged,
                        textFieldColor: AppColorConstants.colorLightPurple,
                        prefixIcon: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 5),
                          child: AppImageAsset(
                            image: AppAssetsConstants.icSearch,
                          ),
                        ),
                      )
                    : const SizedBox(),
                SizedBox(
                  height: isShowSearch ? 15 : 0,
                ),
                isShowDropdown
                    ? Row(
                        children: [
                          AppText(
                            AppStringConstants.sortBy.tr,
                            fontSize: 13,
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          dropdownList != null
                              ? Expanded(
                                child: AppDropdownButton(
                                    menuItemHeight: 30,
                                    hintTextSize: 12,
                                    buttonColor: AppColorConstants.colorLightPurple,

                                    buttonHeight: 25,
                                    hint: AppStringConstants.select.tr,
                                    value: dropdownValue,
                                    onChanged: dropDownOnChanged,
                                    itemBuilder: dropdownList
                                        .map(
                                          (item) => DropdownMenuItem<String>(
                                            value: item,
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 10),
                                              width: double.infinity,
                                              alignment: Alignment.centerLeft,
                                              decoration: BoxDecoration(
                                                border: Border(
                                                  bottom: (item == dropdownList.last)
                                                      ? BorderSide.none
                                                      : const BorderSide(color: AppColorConstants.colorGrey, width: 0.5),
                                                ),
                                              ),
                                              child: AppText(
                                                item.tr,
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 1,
                                                fontWeight: FontWeight.w400,
                                                color: AppColorConstants.colorBlack.withOpacity(0.5),
                                              ),
                                            ),
                                          ),
                                        )
                                        .toList(),
                                    selectedItemBuilder: (context) {
                                      return dropdownList
                                          .map(
                                            (e) => Padding(
                                              padding: const EdgeInsets.symmetric(horizontal: 8),
                                              child: Align(
                                                alignment: Alignment.centerLeft,
                                                child: AppText(
                                                  e.tr,
                                                  maxLines: 1,
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ),
                                          )
                                          .toList();
                                    },
                                    selectedMenuItemBuilder: (context, child) => Container(
                                      height: 30,
                                      width: double.infinity,
                                      color: AppColorConstants.colorAppPrimary,
                                      padding: const EdgeInsets.symmetric(horizontal: 10),
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: AppText(
                                          dropdownValue.toString().tr,
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                          fontWeight: FontWeight.w400,
                                          color: AppColorConstants.colorWhite,
                                        ),
                                      ),
                                    ),
                                  ),
                              )
                              : const SizedBox(),
                          const SizedBox(
                            width: 8,
                          ),
                          InkWell(
                            onTap: sortOnTap,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColorConstants.colorLightPurple,
                                borderRadius: BorderRadiusDirectional.circular(4),
                                border: Border.all(color: AppColorConstants.colorAppPrimary, width: 0.5),
                              ),
                              child: Center(
                                child: AppImageAsset(
                                  image: isAscending ? AppAssetsConstants.icUpDownArrow : AppAssetsConstants.icDownUpArrow,
                                  height: 14,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    : const SizedBox(),
                ...children,
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget commonWidget({required String icon, required String title, Function()? onTap}) {
  return InkWell(
    onTap: onTap,
    child: Container(
      width: 65,
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 3),
      decoration: BoxDecoration(
        color: AppColorConstants.colorLightPurple,
        borderRadius: BorderRadiusDirectional.circular(4),
        border: Border.all(color: AppColorConstants.colorAppPrimary, width: 0.5),
      ),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppImageAsset(image: icon),
            const SizedBox(
              width: 3,
            ),
            AppText(
              title,
              fontSize: 12,
            ),
          ],
        ),
      ),
    ),
  );
}
