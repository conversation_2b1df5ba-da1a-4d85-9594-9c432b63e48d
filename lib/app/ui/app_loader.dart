import 'package:ams/app_imports.dart';

class AppLoader extends StatelessWidget {
  const AppLoader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      width: double.infinity,
      alignment: Alignment.center,
      color: AppColorConstants.colorWhite.withOpacity(0.6),
      child: const Center(
        child: AppImageAsset(
          image: AppAssetsConstants.loaderAnimation,
          height: 150,
          width: 150,
        ),
      ),
    );
  }
}
