import 'package:ams/app_imports.dart';

class DashboardCard extends StatelessWidget {
  final String text;
  final String image;
  final String value;
  final Color? color;
  final VoidCallback onTap;

  const DashboardCard({
    required this.text,
    required this.image,
    required this.onTap,
    required this.value,
    this.color,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(13),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: color ?? AppColorConstants.colorLightGreen,
        ),
        child: Stack(
          alignment: Alignment.bottomRight,
          children: [
            Positioned(
              top: 1,
              left: 1,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColorConstants.colorWhite.withOpacity(0.1),
                ),
                child: AppImageAsset(
                  color: AppColorConstants.colorWhite,
                  image: image,
                  height: 24,
                  width: 24,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                top: 35.0,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  AppText(
                    value,
                    fontWeight: FontWeight.w700,
                    fontSize: 20,
                    color: AppColorConstants.colorWhite,
                  ),
                  const SizedBox(
                    height: 3,
                  ),
                  AppText(
                    text.tr,
                    fontSize: 12,
                    color: AppColorConstants.colorWhite,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
