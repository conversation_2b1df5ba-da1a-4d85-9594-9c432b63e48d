import 'package:ams/app_imports.dart';

class AppButton extends StatelessWidget {
  final GestureTapCallback onTap;
  final Color titleColor;
  final Color backColor;
  final String title;
  final double? height;
  final double textSize;
  final double? width;
  final BoxBorder? border;
  final FontWeight titleWeight;
  final BorderRadiusGeometry? borderRadius;
  final EdgeInsetsGeometry? buttonContentPadding;
  final bool isActive;
  final String? imageIcon;
  final double? iconSize;
  final Color? iconColor;

  const AppButton({
    required this.onTap,
    required this.title,
    super.key,
    this.height,
    this.width,
    this.textSize = 18,
    this.border,
    this.backColor = AppColorConstants.colorAppPrimary,
    this.titleColor = AppColorConstants.colorWhite,
    this.titleWeight = FontWeight.w500,
    this.borderRadius,
    this.buttonContentPadding,
    this.imageIcon,
    this.isActive = true,
    this.iconColor,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isActive ? onTap : null,
      child: Container(
        padding: buttonContentPadding ?? const EdgeInsets.symmetric(horizontal: 10),
        alignment: Alignment.center,
        height: height ?? 45,
        width: width,
        decoration: BoxDecoration(
          color: isActive ? backColor : backColor.withOpacity(0.3),
          borderRadius: borderRadius ?? BorderRadius.circular(100),
          border: border,
        ),
        child: Row(
          children: [
            if (imageIcon?.isNotEmpty ?? false) ...[
              AppImageAsset(
                image: imageIcon,
                height: iconSize,
                width: iconSize,
                color: iconColor,
              ),
              const SizedBox(width: 5),
            ],
            Expanded(
              child: AppText(
                title,
                textAlign: (imageIcon?.isNotEmpty ?? false) ? TextAlign.start : TextAlign.center,
                fontWeight: titleWeight,
                fontSize: textSize,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                color: titleColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AppIconButton extends StatelessWidget {
  final GestureTapCallback onTap;
  final double buttonHeight;
  final double buttonWidth;
  final BorderRadius? buttonRadius;
  final Color buttonColor;
  final IconData? buttonIcon;
  final String? iconImage;
  final double? iconSize;
  final Color? iconColor;
  final BoxBorder? buttonBorder;

  const AppIconButton({
    required this.onTap,
    super.key,
    this.buttonHeight = 40,
    this.buttonWidth = 40,
    this.buttonRadius,
    this.buttonColor = AppColorConstants.colorWhite,
    this.buttonIcon,
    this.iconImage,
    this.iconSize,
    this.iconColor,
    this.buttonBorder,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: buttonHeight,
        width: buttonWidth,
        decoration: BoxDecoration(
          borderRadius: buttonRadius ?? BorderRadius.circular(5),
          color: buttonColor,
          border: buttonBorder ?? Border.all(color: AppColorConstants.colorAppPrimary),
        ),
        alignment: Alignment.center,
        child: (iconImage != null && iconImage!.isNotEmpty)
            ? AppImageAsset(
                image: iconImage!,
                height: iconSize ?? 20,
                width: iconSize ?? 20,
                color: iconColor,
                fit: BoxFit.cover,
              )
            : Icon(
                buttonIcon,
                size: iconSize,
                color: iconColor,
              ),
      ),
    );
  }
}
