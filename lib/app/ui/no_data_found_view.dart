import 'package:ams/app_imports.dart';

class NoDataFoundView extends StatelessWidget {
  const NoDataFoundView({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AppImageAsset(
            image: AppAssetsConstants.imgNoDataFound,
            width: 157,
            height: 115,
          ),
          const SizedBox(
            height: 10,
          ),
          AppText(
            AppStringConstants.noDataAvailable.tr,
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: AppColorConstants.colorLightGrey500,
          ),
        ],
      ),
    );
  }
}
