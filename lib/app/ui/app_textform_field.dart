import 'package:ams/app_imports.dart';

class AppTextForm<PERSON>ield extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String errorText;
  final bool obscureText;
  final bool autofocus;
  final double textFieldHeight;
  final double textFieldFontSize;
  final ValueChanged<String>? onChanged;
  final bool readOnly;
  final GestureTapCallback? onTap;
  final Color? textFieldColor;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final int? maxLength;
  final int? maxLines;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Color? textFieldBorderColor;
  final Color? hintColor;
  final InputBorder? border;
  final InputBorder? errorBorder;
  final EdgeInsetsGeometry? contentPadding;
  final double? hintTextSize;
  final BoxConstraints? prefixIconConstraints;

  const AppTextFormField({
    required this.controller,
    required this.hintText,
    this.labelText,
    super.key,
    this.errorText = '',
    this.obscureText = false,
    this.autofocus = false,
    this.onChanged,
    this.readOnly = false,
    this.onTap,
    this.textFieldColor = AppColorConstants.colorWhite,
    this.inputFormatters,
    this.keyboardType,
    this.maxLength,
    this.textInputAction,
    this.maxLines,
    this.textFieldHeight = 52,
    this.textFieldBorderColor,
    this.prefixIcon,
    this.suffixIcon,
    this.textFieldFontSize = 16,
    this.hintColor,
    this.border,
    this.errorBorder,
    this.contentPadding,
    this.hintTextSize,
    this.prefixIconConstraints,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: textFieldHeight,
          child: TextFormField(
            onChanged: onChanged,
            onTap: onTap,
            autofocus: autofocus,
            inputFormatters: inputFormatters,
            keyboardType: keyboardType,
            textInputAction: textInputAction,
            controller: controller,
            obscureText: obscureText,
            readOnly: readOnly,
            maxLines: maxLines,
            maxLength: maxLength,
            style: TextStyle(fontSize: textFieldFontSize),
            decoration: InputDecoration(
              fillColor: textFieldColor,
              filled: textFieldColor != null,
              prefixIcon: prefixIcon,
              suffixIcon: suffixIcon,
              counterText: '',
              contentPadding: contentPadding ?? const EdgeInsets.only(top: 2, left: 8, bottom: 2, right: 5),
              border: border ??
                  OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: BorderSide(
                      color:
                          (errorText.isNotEmpty) ? AppColorConstants.colorRed : textFieldBorderColor ?? AppColorConstants.colorGrey,
                    ),
                  ),
              disabledBorder: errorBorder ?? border ??
                  OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: BorderSide(
                      color:
                          (errorText.isNotEmpty) ? AppColorConstants.colorRed : textFieldBorderColor ?? AppColorConstants.colorGrey,
                    ),
                  ),
              enabledBorder: border ??
                  OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: BorderSide(
                      color:
                          (errorText.isNotEmpty) ? AppColorConstants.colorRed : textFieldBorderColor ?? AppColorConstants.colorGrey,
                    ),
                  ),
              errorBorder: errorBorder ?? border ??
                  OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: BorderSide(
                      color: (errorText.isNotEmpty) ? AppColorConstants.colorRed : AppColorConstants.colorGrey,
                    ),
                  ),
              focusedBorder: border ??
                  OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: BorderSide(
                      color: (errorText.isNotEmpty) ? AppColorConstants.colorRed : AppColorConstants.colorGrey,
                    ),
                  ),
              focusedErrorBorder: errorBorder ?? border ??
                  OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: BorderSide(
                      color: (errorText.isNotEmpty) ? AppColorConstants.colorRed : AppColorConstants.colorGrey,
                    ),
                  ),
              hintText: hintText,
              floatingLabelBehavior: FloatingLabelBehavior.always,
              hintStyle: TextStyle(
                color: hintColor ?? AppColorConstants.colorGrey,
                fontSize: hintTextSize ?? 14,
              ),
              label: AppText(labelText ?? ''),
              prefixIconConstraints: prefixIconConstraints,
            ),
          ),
        ),
        if (errorText.isNotEmpty) AppText(errorText, color: AppColorConstants.colorRed, fontSize: 12),
      ],
    );
  }
}
