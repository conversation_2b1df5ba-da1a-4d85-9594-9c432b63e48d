import 'package:ams/app_imports.dart';

Future<DateTime?> appDatePicker(BuildContext context, {DateTime? firstDate, DateTime? lastDate, DateTime? initialDate}) {
  return showDatePicker(
    context: context,
    firstDate: firstDate ?? DateTime(1),
    lastDate: lastDate ?? DateTime(DateTime.now().year + 3000),
    initialDate: initialDate ?? DateTime.now(),
    builder: (context, child) {
      return Theme(
        data: ThemeData.light().copyWith(
          datePickerTheme: DatePickerThemeData(
            dayOverlayColor: const WidgetStatePropertyAll(AppColorConstants.colorAppPrimary),
            shape: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          colorScheme: const ColorScheme.dark(
            primary: AppColorConstants.colorAppPrimary,
            onPrimary: AppColorConstants.colorWhite,
            surface: AppColorConstants.colorWhite,
            onSurface: AppColorConstants.colorAppPrimary,
          ),
        ),
        child: child!,
      );
    },
  );
}
