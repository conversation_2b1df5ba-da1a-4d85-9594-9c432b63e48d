import 'package:ams/app_imports.dart';

class AppScaffold extends StatelessWidget {
  final Color? backgroundColor;
  final Widget? body;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final Widget? drawer;
  final PreferredSizeWidget? appBar;
  final bool resizeToAvoidBottomInset;
  final bool extendBody;
  final Color? statusBarColor;
  final bool isIconDark;
  final Key? scaffoldKey;
  final ApiStatus apiStatus;

  const AppScaffold({
    super.key,
    this.extendBody = false,
    this.resizeToAvoidBottomInset = true,
    this.floatingActionButton,
    this.appBar,
    this.body,
    this.backgroundColor,
    this.bottomNavigationBar,
    this.drawer,
    this.isIconDark = true,
    this.statusBarColor,
    this.scaffoldKey,
    this.apiStatus = ApiStatus.initial,
  });

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: getAppStatusBar(isIconDark: isIconDark, statusBarColor: statusBarColor),
      child: Stack(
        children: [
          Scaffold(
            backgroundColor: backgroundColor,
            body: body,
            appBar: appBar,
            floatingActionButton: floatingActionButton,
            resizeToAvoidBottomInset: resizeToAvoidBottomInset,
            bottomNavigationBar: bottomNavigationBar,
            key: scaffoldKey,
            drawer: drawer,
            extendBody: extendBody,
          ),
          if (apiStatus == ApiStatus.loading) const AppLoader(),
        ],
      ),
    );
  }
}
