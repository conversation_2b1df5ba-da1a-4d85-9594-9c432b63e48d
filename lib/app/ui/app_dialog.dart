import 'package:ams/app_imports.dart';

Future appDialog({
  required BuildContext context,
  required String title,
  required String buttonText,
  required GestureTapCallback buttonTap,
  String? subTitle,
  double? subtitleFontSize,
  double? titleFontSize,
  Widget? bodyWidget,
  bool isShowCloseButton = true,
}) {
  return showDialog(
    context: context,
    builder: (context) => Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 35),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      alignment: Alignment.center,
      surfaceTintColor: AppColorConstants.colorWhite,
      backgroundColor: AppColorConstants.colorWhite,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 15),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 5),
            AppText(
              title,
              color: AppColorConstants.colorAppPrimary,
              fontWeight: FontWeight.w600,
              fontSize: titleFontSize ?? 20,
            ),
            const SizedBox(height: 10),
            if (subTitle != null && subTitle.isNotEmpty)
              AppText(
                subTitle,
                fontWeight: FontWeight.w500,
                fontSize: subtitleFontSize ?? 16,
              ),
            if (bodyWidget != null) bodyWidget,
            const SizedBox(height: 30),
            Row(
              children: [
                Expanded(
                  child: AppButton(
                    onTap: buttonTap,
                    height: 35,
                    title: buttonText,
                    textSize: 18,
                    titleColor: AppColorConstants.colorWhite,
                    backColor: AppColorConstants.colorAppPrimary,
                  ),
                ),
                if (isShowCloseButton) ...[
                  const SizedBox(width: 10),
                  Expanded(
                    child: AppButton(
                      onTap: () => gotoBack(),
                      height: 35,
                      textSize: 18,
                      title: AppStringConstants.cancel.tr,
                      titleColor: AppColorConstants.colorAppPrimary,
                      titleWeight: FontWeight.w600,
                      border: Border.all(color: AppColorConstants.colorAppPrimary),
                      backColor: AppColorConstants.colorLightPurple,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    ),
  );
}

Future logoutDialog({
  required BuildContext context,
}) {
  return appDialog(
    context: context,
    title: AppStringConstants.logOut.tr,
    buttonText: AppStringConstants.yes.tr,
    buttonTap: () async {
      gotoBack();
      AuthenticationRepository authenticationRepository = getIt<AuthenticationRepository>();
      // bool isSuccess = await authenticationRepository.logout();
      // if (isSuccess) {
        await removePrefValue(AppPrefConstants.isLogin);
        await removePrefValue(AppPrefConstants.token);
        await removePrefValue(AppPrefConstants.id);
        await removePrefValue(AppPrefConstants.userTypeString);
        await removePrefValue(AppPrefConstants.loginId);
        await removePrefValue(AppPrefConstants.profileId);
        await removePrefValue(AppPrefConstants.registrationId);
      gotoLoginPage();
      //}
    },
    subTitle: AppStringConstants.areYourSureWantToLogout.tr,
  );
}
