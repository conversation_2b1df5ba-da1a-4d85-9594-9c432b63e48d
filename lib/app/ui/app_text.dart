import 'package:ams/app_imports.dart';

class AppText extends StatelessWidget {
  final String title;
  final Color? color;
  final FontWeight? fontWeight;
  final String? fontFamily;
  final double? fontSize;
  final TextAlign? textAlign;
  final double? height;
  final FontStyle? fontStyle;
  final TextOverflow? overflow;
  final int? maxLines;
  final TextDecoration? decoration;
  final double? letterSpacing;

  const AppText(
      this.title, {
        super.key,
        this.color,
        this.fontWeight,
        this.fontFamily = AppAssetsConstants.defaultFont,
        this.fontSize = 14,
        this.textAlign,
        this.height,
        this.fontStyle,
        this.maxLines,
        this.overflow,
        this.decoration = TextDecoration.none,
        this.letterSpacing,
      });

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      textAlign: textAlign,
      maxLines: maxLines,
      style: TextStyle(
        color: color ?? AppColorConstants.colorBlack,
        fontFamily: fontFamily,
        fontWeight: fontWeight??  FontWeight.w500,
        fontSize: fontSize,
        height: height,
        fontStyle: fontStyle,
        overflow: overflow,
        decoration: decoration,
        letterSpacing: letterSpacing,
      ),
    );
  }
}
