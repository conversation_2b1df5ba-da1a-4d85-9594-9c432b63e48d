import 'package:ams/app_imports.dart';
import 'package:dropdown_button2/dropdown_button2.dart';

class AppDropdownButton extends StatelessWidget {
  final String hint;
  final dynamic value;
  final String? labelText;
  final String errorText;
  final List<DropdownMenuItem>? itemBuilder;
  final List<String> dropdownItems;
  final ValueChanged? onChanged;
  final DropdownButtonBuilder? selectedItemBuilder;
  final double? buttonHeight;
  final double? buttonWidth;
  final EdgeInsetsGeometry? buttonPadding;
  final EdgeInsetsGeometry? itemPadding;
  final double? dropdownMenuHeight;
  final double? dropdownMenuWidth;
  final EdgeInsetsGeometry? dropdownPadding;
  final int? dropdownElevation;
  final SelectedMenuItemBuilder? selectedMenuItemBuilder;
  final Color? buttonColor;
  final double? hintTextSize;
  final double menuItemHeight;

  const AppDropdownButton({
    required this.hint,
    required this.value,
    required this.onChanged,
    this.labelText,
    this.dropdownItems = const <String>[],
    this.itemBuilder,
    this.selectedItemBuilder,
    this.buttonHeight,
    this.buttonWidth = double.infinity,
    this.buttonPadding,
    this.itemPadding,
    this.dropdownMenuHeight,
    this.dropdownMenuWidth,
    this.dropdownPadding,
    this.dropdownElevation,
    this.errorText = '',
    this.selectedMenuItemBuilder,
    this.buttonColor,
    this.hintTextSize,
    this.menuItemHeight = 40,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[AppText('$labelText'), const SizedBox(height: 2)],
        DropdownButtonHideUnderline(
          child: DropdownButton2(
            isExpanded: true,
            hint: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: AppText(
                hint,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                fontSize: hintTextSize ?? 14,
                color: AppColorConstants.colorBlack,
              ),
            ),
            value: value,
            items: itemBuilder ??
                dropdownItems
                    .map(
                      (item) => DropdownMenuItem<String>(
                        value: item,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          width: double.infinity,
                          alignment: Alignment.centerLeft,
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: (item == dropdownItems.last)
                                  ? BorderSide.none
                                  : const BorderSide(color: AppColorConstants.colorGrey, width: 0.5),
                            ),
                          ),
                          child: AppText(
                            item,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            fontWeight: FontWeight.w400,
                            color: AppColorConstants.colorBlack.withOpacity(0.5),
                          ),
                        ),
                      ),
                    )
                    .toList(),
            onChanged: onChanged,
            selectedItemBuilder: selectedItemBuilder ??
                (context) {
                  return dropdownItems
                      .map(
                        (e) => Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: AppText(
                              e,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      )
                      .toList();
                },
            iconStyleData: IconStyleData(
              openMenuIcon: Padding(
                padding: const EdgeInsets.only(right: 10),
                child: AppImageAsset(
                  image: AppAssetsConstants.icCloseDropdown,
                ),
              ),
              icon: Padding(
                padding: const EdgeInsets.only(right: 10),
                child: AppImageAsset(
                  image: AppAssetsConstants.icOpenDropdown,
                ),
              ),
            ),
            buttonStyleData: ButtonStyleData(
              height: buttonHeight ?? 50,
              width: buttonWidth,
              padding: EdgeInsets.zero,
              decoration: BoxDecoration(
                color: buttonColor ?? AppColorConstants.colorLightPurple,
                border: Border.all(color: errorText.isNotEmpty ? AppColorConstants.colorRedAccent : AppColorConstants.colorGrey),
                borderRadius: BorderRadius.circular(5),
              ),
            ),
            menuItemStyleData: MenuItemStyleData(
              height: menuItemHeight,
              padding: EdgeInsets.zero,
              selectedMenuItemBuilder: selectedMenuItemBuilder ??
                  (context, child) {
                    return Container(
                      height: menuItemHeight,
                      width: double.infinity,
                      color: AppColorConstants.colorAppPrimary,
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: AppText(
                          value ?? '',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          fontWeight: FontWeight.w400,
                          color: AppColorConstants.colorWhite,
                        ),
                      ),
                    );
                  },
            ),

            dropdownStyleData: DropdownStyleData(
              maxHeight: dropdownMenuHeight ?? 180,
              width: dropdownMenuWidth,
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: AppColorConstants.colorAppPrimary.withOpacity(0.2),
                    spreadRadius: 2,
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: AppColorConstants.colorGrey, width: 0.5),
                color: AppColorConstants.colorWhite,
              ),
              elevation: dropdownElevation ?? 0,
              isOverButton: false,
              offset: Offset.zero,
              scrollbarTheme: const ScrollbarThemeData(
                radius: Radius.circular(40),
                thumbColor: WidgetStatePropertyAll(AppColorConstants.colorTransparent),
                thumbVisibility: WidgetStatePropertyAll(false),
              ),
            ),
          ),
        ),
        if (errorText.isNotEmpty) ...[
          const SizedBox(height: 4),
          AppText(errorText, color: AppColorConstants.colorRed, fontSize: 12),
        ],
      ],
    );
  }
}
