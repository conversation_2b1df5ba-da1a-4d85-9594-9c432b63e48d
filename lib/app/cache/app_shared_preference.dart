import 'package:shared_preferences/shared_preferences.dart';

mixin AppPrefConstants {

  static String isLogin = 'isLogin';
  static String userTypeString = 'userTypeString';
  static String id='id';
  static String registrationId='registrationId';
  static String profileId='profileId';
  static String loginId='loginId';
  static String token = 'token';

}

Future<void> checkPrefKey(String key) async {
  var prefs = await SharedPreferences.getInstance();
  prefs.containsKey(key);
}

Future<String?>? getPrefStringValue(String key) async {
  var prefs = await SharedPreferences.getInstance();
  return prefs.getString(key);
}

Future<void> setPrefStringValue(String key, String value) async {
  var prefs = await SharedPreferences.getInstance();
  prefs.setString(key, value);
}

Future<bool> getPrefBoolValue(String key) async {
  var prefs = await SharedPreferences.getInstance();
  return prefs.getBool(key) ?? false;
}

Future<void> setPrefBoolValue(String key, bool value) async {
  var prefs = await SharedPreferences.getInstance();
  prefs.setBool(key, value);
}

Future<int?>? getPrefIntValue(String key) async {
  var prefs = await SharedPreferences.getInstance();
  return prefs.getInt(key);
}

Future<void> setPrefIntValue(String key, int value) async {
  var prefs = await SharedPreferences.getInstance();
  prefs.setInt(key, value);
}

Future<double?>? getPrefDoubleValue(String key) async {
  var prefs = await SharedPreferences.getInstance();
  return prefs.getDouble(key);
}

Future<void> setPrefDoubleValue(String key, double value) async {
  var prefs = await SharedPreferences.getInstance();
  prefs.setDouble(key, value);
}

Future<List<String>?>? getPrefListValue(String key) async {
  var prefs = await SharedPreferences.getInstance();
  return prefs.getStringList(key);
}

Future<void> setPrefListValue(String key, List<String> value) async {
  var prefs = await SharedPreferences.getInstance();
  prefs.setStringList(key, value);
}

Future<Set<String>> getPrefKeys() async {
  var prefs = await SharedPreferences.getInstance();
  return prefs.getKeys();
}

Future<void> removePrefValue(String key) async {
  var prefs = await SharedPreferences.getInstance();
  prefs.remove(key);
}
