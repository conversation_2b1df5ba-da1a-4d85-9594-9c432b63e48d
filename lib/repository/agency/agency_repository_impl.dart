import 'package:ams/app_imports.dart';

class AgencyRepositoryImpl implements AgencyRepository {
  final RestHelper _restHelper = RestHelper.instance;
  final RestConstants _restConstants = RestConstants.instance;

  @override
  Future<List<AgencyApplicationReceivedModel>> getAgencyReceivedApplicationData() async {
    List<AgencyApplicationReceivedModel> agencyApplicationReceivedList = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getAgencyApplicationReceivedEndpoint);
    if (response.isNotEmpty) {
      agencyApplicationReceivedList = agencyApplicationReceivedModelFromJson(response);
    }
    return agencyApplicationReceivedList;
  }

  @override
  Future<List<AgencyApprovedAuditsModel>> getAgencyApprovedAuditsData() async {
    List<AgencyApprovedAuditsModel> agencyApprovedAuditsList = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getAgencyApprovedReportsEndpoint);
    if (response.isNotEmpty) {
      agencyApprovedAuditsList = agencyApprovedAuditsModelFromJson(response);
    }
    return agencyApprovedAuditsList;
  }

  @override
  Future<List<AgencyCancelledAuditsModel>> getAgencyCancelledAuditsData() async {
    List<AgencyCancelledAuditsModel> agencyCancelledReportList = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getAgencyCancelledReportsEndpoint);
    if (response.isNotEmpty) {
      agencyCancelledReportList = agencyCancelledAuditsModelFromJson(response);
    }
    return agencyCancelledReportList;
  }

  @override
  Future<List<AgencyNonComplianceModel>> getAgencyNonComplianceData() async {
    List<AgencyNonComplianceModel> agencyNonComplianceList = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getAgencyNonComplianceReportsEndpoint);
    if (response.isNotEmpty) {
      agencyNonComplianceList = agencyNonComplianceModelFromJson(response);
    }
    return agencyNonComplianceList;
  }

  @override
  Future<List<AgencyPendingWithAuditorModel>> getAgencyPendingWithAuditorData() async {
    List<AgencyPendingWithAuditorModel> agencyPendingWithAuditorList = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getAgencyAuditReportsPendingWithAuditorEndpoint);
    if (response.isNotEmpty) {
      agencyPendingWithAuditorList = agencyPendingWithAuditorModelFromJson(response);
    }
    return agencyPendingWithAuditorList;
  }

  @override
  Future<List<AgencyYourAuditorsModel>> getYourAuditorData({required int agencyId}) async {
    List<AgencyYourAuditorsModel> agencyYourAuditorList = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getAgencyYourAuditorsEndpoint, addOns: '/$agencyId');
    if (response.isNotEmpty) {
      agencyYourAuditorList = agencyYourAuditorsModelFromJson(response);
    }
    return agencyYourAuditorList;
  }

  @override
  Future<List<YourAuditorsExperienceModel>> getAuditorExperienceData({required int agencyId}) async {
    List<YourAuditorsExperienceModel> yourAuditorExperienceList = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getAgencyYourAuditorsEndpoint, addOns: '/$agencyId');
    if (response.isNotEmpty) {
      yourAuditorExperienceList = yourAuditorsExperienceModelFromJson(response);
    }
    return yourAuditorExperienceList;
  }

  @override
  Future<List<YourAuditorsQualificationModel>> getAuditorQualificationData({required int agencyId}) async {
    List<YourAuditorsQualificationModel> yourAuditorsQualificationList = [];
    String response =
        await _restHelper.getRestCall(endpoint: _restConstants.getAgencyYourAuditorsQualificationEndpoint, addOns: '/$agencyId');
    if (response.isNotEmpty) {
      yourAuditorsQualificationList = yourAuditorsQualificationModelFromJson(response);
    }
    return yourAuditorsQualificationList;
  }

  @override
  Future<List<ExperienceModel>> getExperienceData({required int agencyId}) async {
    List<ExperienceModel> experienceModelList = [];
    String response =
        await _restHelper.getRestCall(endpoint: _restConstants.getAgencyYourAuditorsExperienceEndpoint, addOns: '/$agencyId');
    if (response.isNotEmpty) {
      experienceModelList = experienceModelFromJson(response);
    }
    return experienceModelList;
  }

  @override
  Future<AgencyDashboardModel> getAgencyDashboardData(int loginId, int registerId) async {
    String response =
        await _restHelper.getRestCall(endpoint: _restConstants.getAgencyDashboardDataEndpoint, addOns: '/$loginId/$registerId');
    if (response.isNotEmpty) {
      AgencyDashboardModel agencyDashboardModel = agencyDashboardModelFromJson(response);
      return agencyDashboardModel;
    }
    return AgencyDashboardModel();
  }

  @override
  Future<AgencyPersonalInfoModel> getPersonalInfo(int id) async {
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getAgencyPersonalInfoByIdEndpoint, addOns: '/$id');
    if (response.isNotEmpty) {
      AgencyPersonalInfoModel personalInfoModel = agencyPersonalInfoModelFromJson(response);
      return personalInfoModel;
    }
    return AgencyPersonalInfoModel();
  }

  @override
  Future<List<AgencyNonComplianceModel>> getNonComplianceData(int agencyId) async {
    List<AgencyNonComplianceModel> nonComplianceList = [];
    String response =
        await _restHelper.getRestCall(endpoint: _restConstants.getAgencyNonComplianceReportsEndpoint, addOns: '/416');
    if (response.isNotEmpty) {
      nonComplianceList = agencyNonComplianceModelFromJson(response);
    }
    return nonComplianceList;
  }
}
