import 'package:ams/app_imports.dart';

abstract class AgencyRepository {
  Future<AgencyDashboardModel> getAgencyDashboardData(int loginId, int registerId);

  Future<List<AgencyApplicationReceivedModel>> getAgencyReceivedApplicationData();

  Future<List<AgencyNonComplianceModel>> getAgencyNonComplianceData();

  Future<List<AgencyApprovedAuditsModel>> getAgencyApprovedAuditsData();

  Future<List<AgencyCancelledAuditsModel>> getAgencyCancelledAuditsData();

  Future<List<AgencyPendingWithAuditorModel>> getAgencyPendingWithAuditorData();

  Future<List<AgencyYourAuditorsModel>> getYourAuditorData({required int agencyId});

  Future<List<YourAuditorsExperienceModel>> getAuditorExperienceData({required int agencyId});

  Future<List<YourAuditorsQualificationModel>> getAuditorQualificationData({required int agencyId});

  Future<List<ExperienceModel>> getExperienceData({required int agencyId});

  Future<AgencyPersonalInfoModel> getPersonalInfo(int agencyId);

  Future<List<AgencyNonComplianceModel>> getNonComplianceData(int agencyId);
}
