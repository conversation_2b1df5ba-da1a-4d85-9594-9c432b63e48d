import 'package:ams/app_imports.dart';

class AuditorRepositoryImpl implements AuditorRepository {
  final RestHelper restHelper = RestHelper.instance;
  final RestConstants restConstants = RestConstants.instance;

  @override
  Future<AuditorDashboardModel> getDashboardData() async{
    AuditorDashboardModel auditorDashboardModel = AuditorDashboardModel();
    int auditorId = await getPrefIntValue(AppPrefConstants.id) ?? 0;
    String response = await restHelper.getRestCall(endpoint: restConstants.getAuditorDashboardEndpoint,addOns: '/0/$auditorId');
    if(response.isNotEmpty){
      auditorDashboardModel = auditorDashboardModelFromJson(response);
    }
    return auditorDashboardModel;
  }
}
