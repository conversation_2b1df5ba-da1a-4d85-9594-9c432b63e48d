import 'dart:developer';

import 'package:ams/app_imports.dart';

class AuthenticationRepositoryImpl implements AuthenticationRepository {
  final RestHelper restHelper = RestHelper.instance;
  final RestConstants restConstants = RestConstants.instance;

  @override
  Future<List<KobDetailsModel>> getKobDetailsData() async {
    List<KobDetailsModel> detailsList = [];
    String responseData = await restHelper.getRestCall(endpoint: restConstants.getKobDetailsEndpoint);
    if (responseData.isNotEmpty) {
      detailsList = kobDetailsModelFromJson(responseData);
    }
    return detailsList;
  }

  @override
  Future<String> registerAgency({
    required AgencyRegistrationModel registrationModel,
    required String certificateFilePath,
    required String confidentialFilePath,
    required String accreditationFilePath,
    required String leadAuditorFilePath,
    required String legalEnterStatusFilePath,
    required String? otherFilePath,
  }) async {
    String filename =
        '{"filename0":  "accereditationtext.pdf",\n"filename1":  "confidentialitytext.pdf",\n"filename2":  "assessmenttext.pdf",\n"filename3":  "leadauditortext.pdf",\n"filename4":  "legaltext.pdf",\n"filename5":  "anyotherdoctext.pdf"}}';
    String docNumbers = '{"docid0":  2,\n"docid1":  4,\n"docid2":  5,\n"docid3":  6,\n"docid4":  7,\n"docid5":  8}';
    Map<String, String> map = {
      'agencyregistrationdata': jsonEncode(registrationModel),
      'filename': filename,
      'docidnumber': docNumbers,
    };

    log('map --> $map');

    List<MultipartFile> fileList = [
      await MultipartFile.fromPath(
        'file',
        accreditationFilePath,
      ),
      await MultipartFile.fromPath(
        'file',
        confidentialFilePath,
      ),
      await MultipartFile.fromPath(
        'file',
        certificateFilePath,
      ),
      await MultipartFile.fromPath(
        'file',
        leadAuditorFilePath,
      ),
      await MultipartFile.fromPath(
        'file',
        legalEnterStatusFilePath,
      ),
      if (otherFilePath?.isNotEmpty ?? false)
        await MultipartFile.fromPath(
          'file',
          otherFilePath!,
        ),
    ];

    String response = await restHelper.multiPartRestCall(
      endpoint: restConstants.registerAgencyEndpoint,
      body: map,
      fileList: fileList,
    );
    if (response.isNotEmpty) {
      return jsonDecode(response)['message'].toString();
    }
    return '';
  }

  @override
  Future<bool> getSixDigitCode({required String logInId, required String registerId}) async {
    String? response = await restHelper.postRestCall(
      endpoint: restConstants.getSixDigitCodeEndPoint,
      body: {'userid': logInId, 'emailid': registerId},
    );
    if (response.isNotEmpty) {
      return true;
    }
    return false;
  }

  @override
  Future<bool> resetPassword({
    required String logInId,
    required String registerId,
    required String code,
    required String newPassword,
    required String reEnterPassword,
  }) async {
    String? response = await restHelper.postRestCall(
      endpoint: restConstants.resetPasswordEndpoint,
      body: {
        'userid': logInId,
        'emailid': registerId,
        'password': newPassword,
        'retypedpassword': reEnterPassword,
        'sixdigitcode': code,
      },
    );
    if (response.isNotEmpty) {
      return true;
    }
    return false;
  }

  @override
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    String? loginId = await getPrefStringValue(AppPrefConstants.loginId);

    String? responseData = await restHelper.putRestCall(
      endpoint: restConstants.changePassword,
      body: {
        'loginId': loginId,
        'newPassword': newPassword,
        'oldPassword': oldPassword,
      },
    );
    return jsonDecode(responseData)['updated'];
  }

  @override
  Future<bool> login(String loginId, String password) async {
    String response = await restHelper.postRestCall(
      endpoint: restConstants.loginEndpoint,
      body: {
        'loginid': loginId,
        'encrypt_password': password,
      },
    );

    if (response.isNotEmpty) {
      Map<String, dynamic> map = jsonDecode(response);
      setPrefIntValue(AppPrefConstants.id, map['id']);
      setPrefStringValue(AppPrefConstants.loginId, map['loginid']);
      setPrefIntValue(AppPrefConstants.registrationId, map['registrationid']);
      setPrefIntValue(AppPrefConstants.profileId, map['profileid']);
      setPrefBoolValue(AppPrefConstants.isLogin, true);
      return true;
    }
    jsonDecode(response)['error'].toString().showErrorToast();

    return false;
  }

  @override
  Future<bool> logout() async {
    String response = await restHelper.postRestCall(
      endpoint: restConstants.logOutEndpoint,
      body: null,
    );
    if (response.isNotEmpty) {
      response.toString().showSuccessToast();
      return true;
    } else {
      jsonDecode(response)['message'].toString().showErrorToast();
    }
    return false;
  }
}
