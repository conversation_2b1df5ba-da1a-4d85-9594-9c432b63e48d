import 'package:ams/app_imports.dart';

abstract class AuthenticationRepository {
  Future<List<KobDetailsModel>> getKobDetailsData();

  Future<String> registerAgency({
    required AgencyRegistrationModel registrationModel,
    required String certificateFilePath,
    required String confidentialFilePath,
    required String accreditationFilePath,
    required String leadAuditorFilePath,
    required String legalEnterStatusFilePath,
    required String? otherFilePath,
  });

  Future<bool> getSixDigitCode({required String logInId, required String registerId});

  Future<bool> resetPassword(
      {required String logInId,
      required String registerId,
      required String code,
      required String newPassword,
      required String reEnterPassword,});

  Future<bool> changePassword(String oldPassword, String newPassword);

  Future<bool> login(String loginId, String password);

  Future<bool> logout();
}
