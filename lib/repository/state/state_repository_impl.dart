import 'package:ams/app_imports.dart';

class StateRepositoryImpl implements StateRepository {
  final RestHelper _restHelper = RestHelper.instance;
  final RestConstants _restConstants = RestConstants.instance;

  @override
  Future<List<StateModel>> getStateListData() async {

    List<StateModel> stateListModel = [];
    String responseData = await _restHelper.getRestCall(endpoint: _restConstants.getStateEndpoint);
    if (responseData.isNotEmpty) {
      List dataList = jsonDecode(responseData);
      for (var element in dataList) {
        stateListModel.add(StateModel.fromJson(element));
      }
    }
    return stateListModel;
  }

  @override
  Future<List<DistrictModel>> getDistrictListData(int stateId) async {
    List<DistrictModel> districtListModel = [];
    String responseData = await _restHelper.getRestCall(endpoint: '${_restConstants.getDistrictsEndpoint}/$stateId');
    if (responseData.isNotEmpty) {
      List dataList = jsonDecode(responseData);
      for (var element in dataList) {
        districtListModel.add(DistrictModel.fromJson(element));
      }
    }
    return districtListModel;
  }
}
