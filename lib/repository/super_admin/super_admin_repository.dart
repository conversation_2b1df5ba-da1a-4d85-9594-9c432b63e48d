import 'package:ams/app_imports.dart';
abstract class SuperAdminRepository {
  Future<SuperAdminDashboardModel> getDashboardData();

  Future<List<SuperAdminApplicationReceivedModel>> getApplicationReceivedData();

  Future<List<SuperAdminApplicationReceivedModel>> getSuperAdminRejectAgencyData();

  Future<List<SuperAdminEmpanelledAgenciesModel>> getEmpaneledAgencyData();

  Future<SuperAdminAgencyDetailsModel> getAgencyDetailsData(int agencyId);

  Future<List<SuperAdminEnlistedAuditorsModel>> getSuperAdminEnlistedAuditorsData({String? agencyId = '0'});

  Future<bool> approveRejectAgency({
    required int agencyId,
    required String status,
    required String remarks,
  });

  Future<List<SuperAdminEmpanelledAgenciesModel>> getSuperAdminEmpanelledAgenciesData({String? agencyState});

  Future<List<SuperAdminAuditorModel>> getAuditorsByAgencyId(int id);
  
  Future<List<CheckListCategoryModel>> getCheckListCategory();

  Future<bool> updateEmpanelledAgency({
    required int? agencyId,
    required String agencyName,
    required String mobileNumber,
    required String scopeOfAuditing,
    required String geographicalArea,
  });

  Future<bool> activateDeactivateAgency({
    required int agencyId,
    required String status,
  });
}
