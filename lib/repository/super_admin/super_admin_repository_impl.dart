import 'package:ams/app_imports.dart';

class SuperAdminRepositoryImpl implements SuperAdminRepository {
  final RestHelper _restHelper = RestHelper.instance;
  final RestConstants _restConstants = RestConstants.instance;

  @override
  Future<SuperAdminDashboardModel> getDashboardData() async {
    SuperAdminDashboardModel superAdminDashboardModel = SuperAdminDashboardModel();
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getSuperAdminDashboardEndpoint);
    if (response.isNotEmpty) {
      superAdminDashboardModel = superAdminDashboardModelFromJson(response);
    }
    return superAdminDashboardModel;
  }

  @override
  Future<List<SuperAdminApplicationReceivedModel>> getApplicationReceivedData() async {
    List<SuperAdminApplicationReceivedModel> superAdminApplicationReceivedModel = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getSuperAdminAllAgenciesEndpoint);
    if (response.isNotEmpty) {
      superAdminApplicationReceivedModel = superAdminApplicationReceivedModelFromJson(response);
    }
    return superAdminApplicationReceivedModel;
  }

  @override
  Future<SuperAdminAgencyDetailsModel> getAgencyDetailsData(int agencyId) async {
    SuperAdminAgencyDetailsModel superAdminAgencyDetailsModel = SuperAdminAgencyDetailsModel();
    String response =
        await _restHelper.getRestCall(endpoint: _restConstants.getSuperAdminAgencyDetailsEndpoint, addOns: '/$agencyId');
    if (response.isNotEmpty) {
      superAdminAgencyDetailsModel = superAdminAgencyDetailsModelFromJson(response);
    }
    return superAdminAgencyDetailsModel;
  }

  @override
  Future<List<SuperAdminEnlistedAuditorsModel>> getSuperAdminEnlistedAuditorsData({String? agencyId = '0'}) async {
    List<SuperAdminEnlistedAuditorsModel> superAdminEnlistedAuditorsModel = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getAuditorsEndpoint, addOns: '/$agencyId');
    if (response.isNotEmpty) {
      superAdminEnlistedAuditorsModel = superAdminEnlistedAuditorsModelFromJson(response);
    }
    return superAdminEnlistedAuditorsModel;
  }

  @override
  Future<bool> approveRejectAgency({required int agencyId, required String status, required String remarks}) async {
    bool isSuccess = false;
    String response = await _restHelper.putRestCall(
      endpoint: _restConstants.superAdminApproveRejectAgencyEndpoint,
      body: {
        'id': agencyId,
        'narration': remarks,
        'action': status,
      },
    );
    if (response.isNotEmpty) {
      Map<String, dynamic> responseMap = jsonDecode(response);
      isSuccess = responseMap['response'] ?? false;
    }
    return isSuccess;
  }

  @override
  Future<List<SuperAdminEmpanelledAgenciesModel>> getSuperAdminEmpanelledAgenciesData({String? agencyState}) async {
    List<SuperAdminEmpanelledAgenciesModel> superAdminEmpanelledAgencyList = [];
    String response =
        await _restHelper.getRestCall(endpoint: _restConstants.getSuperAdminAgenciesEndpoint, addOns: '/$agencyState');
    if (response.isNotEmpty) {
      superAdminEmpanelledAgencyList = superAdminEmpanelledAgenciesModelFromJson(response);
    }
    return superAdminEmpanelledAgencyList;
  }

  @override
  Future<List<SuperAdminApplicationReceivedModel>> getSuperAdminRejectAgencyData() async {
    List<SuperAdminApplicationReceivedModel> superAdminApplicationReceivedModel = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getSuperAdminAgenciesEndpoint, addOns: '/R');
    if (response.isNotEmpty) {
      superAdminApplicationReceivedModel = superAdminApplicationReceivedModelFromJson(response);
    }
    return superAdminApplicationReceivedModel;
  }

  @override
  Future<List<SuperAdminEmpanelledAgenciesModel>> getEmpaneledAgencyData() async {
    List<SuperAdminEmpanelledAgenciesModel> superAdminApplicationReceivedModel = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getSuperAdminAgenciesEndpoint, addOns: '/Y');
    if (response.isNotEmpty) {
      superAdminApplicationReceivedModel = superAdminEmpanelledAgenciesModelFromJson(response);
    }
    return superAdminApplicationReceivedModel;
  }

  @override
  Future<List<SuperAdminAuditorModel>> getAuditorsByAgencyId(int id) async {
    List<SuperAdminAuditorModel> superAdminAuditorModel = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.getAuditorsEndpoint, addOns: '/$id');
    if (response.isNotEmpty) {
      superAdminAuditorModel = superAdminAuditorModelFromJson(response);
    }
    return superAdminAuditorModel;
  }

  @override
  Future<bool> updateEmpanelledAgency({
    required int? agencyId,
    required String agencyName,
    required String mobileNumber,
    required String scopeOfAuditing,
    required String geographicalArea,
  }) async {
    bool isSuccess = false;
    String response = await _restHelper.putRestCall(
      endpoint: _restConstants.superAdminUpdateAgencyDetailsEndpoint,
      body: {
        'id': agencyId,
        'agency_name': agencyName,
        'mobile_number': mobileNumber,
        'scope_of_auditing': scopeOfAuditing,
        'geographical_area': geographicalArea,
      },
    );
    if (response.isNotEmpty) {
      isSuccess = jsonDecode(response)['response'] ?? false;
    }
    return isSuccess;
  }

  @override
  Future<bool> activateDeactivateAgency({required int agencyId, required String status}) async {
    bool isSuccess = false;
    String response = await _restHelper.putRestCall(
      endpoint: _restConstants.superAdminActivateDeactivateAgencyEndpoint,
      body: {
        'id': agencyId,
        'action': status,
      },
    );
    if (response.isNotEmpty) {
      Map<String, dynamic> responseMap = jsonDecode(response);
      isSuccess = responseMap['response'] ?? false;
    }
    return isSuccess;
  }

  @override
  Future<List<CheckListCategoryModel>> getCheckListCategory() async {
    List<CheckListCategoryModel> checkListCategory = [];
    String response = await _restHelper.getRestCall(endpoint: _restConstants.superAdminGetChecklistCategoriesEndpoint);
    if (response.isNotEmpty) {
      checkListCategory = checkListCategoryModelFromJson(response);
    }
    return checkListCategory;
  }
}
