import 'package:ams/app_imports.dart';

class RegisterAuditorController extends GetxController{
  final StateRepository _stateRepository=getIt<StateRepository>();

  Future<List<StateModel>> getStateList()async{
    return await _stateRepository.getStateListData();
  }
  Future<List<DistrictModel>> getDistrictList(int stateId)async{
    return await _stateRepository.getDistrictListData(stateId);
  }
}
