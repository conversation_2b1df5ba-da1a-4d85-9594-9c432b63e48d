import 'package:ams/app_imports.dart';

class YourAuditorsController extends GetxController {
  final AgencyRepository _agencyRepository = getIt<AgencyRepository>();

  Future<List<AgencyYourAuditorsModel>> getAgencyPendingWithAuditorData({required int agencyId}) async {
    return await _agencyRepository.getYourAuditorData(agencyId: agencyId);
  }

  Future<List<YourAuditorsQualificationModel>> getYourAuditorQualificationData({required int auditorId}) async {
    return await _agencyRepository.getAuditorQualificationData(agencyId: auditorId);
  }

  Future<List<ExperienceModel>> getExperienceList({required int auditorId}) async {
    return await _agencyRepository.getExperienceData(agencyId: auditorId);
  }
}
