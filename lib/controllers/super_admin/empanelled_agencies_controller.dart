import 'package:ams/app_imports.dart';

class SuperAdminEmpanelledAgenciesController extends GetxController {
  final SuperAdminRepository _superAdminRepository = getIt<SuperAdminRepository>();
  final StateRepository _stateRepository = getIt<StateRepository>();

  Future<List<SuperAdminEmpanelledAgenciesModel>> getEmpaneledAgencyData() async {
    return await _superAdminRepository.getEmpaneledAgencyData();
  }

  Future<List<StateModel>> getStateListData() async {
    return await _stateRepository.getStateListData();
  }

  Future<bool> updateEmpanelledAgency({
    required int? agencyId,
    required String agencyName,
    required String mobileNumber,
    required String scopeOfAuditing,
    required String geographicalArea,
  }) async {
    return await _superAdminRepository.updateEmpanelledAgency(
      agencyId: agencyId,
      agencyName: agencyName,
      mobileNumber: mobileNumber,
      scopeOfAuditing: scopeOfAuditing,
      geographicalArea: geographicalArea,
    );
  }

  Future<bool> activateDeactivateAgency({
    required int agencyId,
    required String status,
  }) async {
    return await _superAdminRepository.activateDeactivateAgency(agencyId: agencyId, status: status);
  }
}
