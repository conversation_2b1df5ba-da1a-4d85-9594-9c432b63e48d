import 'package:ams/app_imports.dart';

class SuperAdminApplicationReceivedController extends GetxController {
  final SuperAdminRepository _superAdminRepository = getIt<SuperAdminRepository>();

  Future<List<SuperAdminApplicationReceivedModel>> getSuperAdminApplicationReceivedData() async {
    return await _superAdminRepository.getApplicationReceivedData();
  }

  Future<SuperAdminAgencyDetailsModel> getAgencyDetailsData(int agencyId) async {
    return await _superAdminRepository.getAgencyDetailsData(agencyId);
  }

  Future<List<SuperAdminEnlistedAuditorsModel>> getEnlistedAuditorsOfAgency({required String agencyId}) async {
    return await _superAdminRepository.getSuperAdminEnlistedAuditorsData(agencyId: agencyId);
  }

  Future<bool> approveRejectAgency({
    required int agencyId,
    required String status,
    required String remarks,
  }) async {
    return await _superAdminRepository.approveRejectAgency(agencyId: agencyId, status: status, remarks: remarks);
  }
}
