import 'package:ams/app_imports.dart';

class AuthenticationController extends GetxController {
  final AuthenticationRepository _authenticationRepository = getIt<AuthenticationRepository>();
  final StateRepository _stateRepository = getIt<StateRepository>();

  Future<List<StateModel>> getStateListData() async {
    return await _stateRepository.getStateListData();
  }

  Future<List<DistrictModel>> getDistrictListData(int stateId) async {
    return await _stateRepository.getDistrictListData(stateId);
  }

  Future<List<KobDetailsModel>> getKobDetailsData() async {
    return await _authenticationRepository.getKobDetailsData();
  }


  Future<String> registerAgency({
    required AgencyRegistrationModel registrationModel,
    required String certificateFilePath,
    required String confidentialFilePath,
    required String accreditationFilePath,
    required String leadAuditorFilePath,
    required String legalEnterStatusFilePath,
    required String? otherFilePath,
  }) async {
    return await _authenticationRepository.registerAgency(
      registrationModel: registrationModel,
      certificateFilePath: certificateFilePath,
      confidentialFilePath: confidentialFilePath,
      accreditationFilePath: accreditationFilePath,
      leadAuditorFilePath: leadAuditorFilePath,
      legalEnterStatusFilePath: legalEnterStatusFilePath,
      otherFilePath: otherFilePath,
    );
  }

  Future<bool> getSixDigitCode({required String loginId, required String registerId}) async {
    return await _authenticationRepository.getSixDigitCode(logInId: loginId, registerId: registerId);
  }

  Future<bool> resetPassword(
      {required String loginId,
      required String registerId,
      required String code,
      required String newPassword,
      required String reEnterPassword,}) async {
    return await _authenticationRepository.resetPassword(
      logInId: loginId,
      registerId: registerId,
      code: code,
      newPassword: newPassword,
      reEnterPassword: reEnterPassword,
    );
  }

  Future<bool> changePassword(String oldPassword,String newPassword)async{
    return await _authenticationRepository.changePassword(oldPassword, newPassword);
  }
  Future<bool> login(String loginId,String password)async{
    return await _authenticationRepository.login(loginId, password);
  }
}
