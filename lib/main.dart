import 'package:ams/app_imports.dart';

final getIt = GetIt.instance;


void initRepositories() {
  getIt.registerSingleton<AuthenticationRepository>(AuthenticationRepositoryImpl());
  getIt.registerSingleton<StateRepository>(StateRepositoryImpl());
  getIt.registerSingleton<SuperAdminRepository>(SuperAdminRepositoryImpl());
  getIt.registerSingleton<AgencyRepository>(AgencyRepositoryImpl());
  getIt.registerSingleton<AuditorRepository>(AuditorRepositoryImpl());
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  AppConfig.create(
    appName: AppStringConstants.appProdName,
    flavor: Flavor.prod,
  );
  initRepositories();
  HttpOverrides.global = MyHttpOverrides();
  SystemChrome.setPreferredOrientations(
    [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown],
  );
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    var botToastBuilder = BotToastInit();
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus!.unfocus(),
      child: GetMaterialApp.router(
        title: AppStringConstants.appProdName,
        theme: ThemeData(
          scaffoldBackgroundColor: AppColorConstants.colorWhite,
          fontFamily: AppAssetsConstants.defaultFont,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
        routerDelegate: router.routerDelegate,
        backButtonDispatcher: router.backButtonDispatcher,
        routeInformationParser: router.routeInformationParser,
        routeInformationProvider: router.routeInformationProvider,
        debugShowCheckedModeBanner: false,
        defaultTransition: Transition.rightToLeft,
        translations: AppLocalization(),
        locale: Get.deviceLocale,
        transitionDuration: const Duration(microseconds: 800),
        navigatorObservers: <NavigatorObserver>[
          BotToastNavigatorObserver(),
        ],
        scrollBehavior: ScrollHelper(),
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
            child: child = botToastBuilder(context, child),
          );
        },
      ),
    );
  }
}

class MyHttpOverrides extends HttpOverrides{
  @override
  HttpClient createHttpClient(SecurityContext? context){
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port)=> true;
  }
}
