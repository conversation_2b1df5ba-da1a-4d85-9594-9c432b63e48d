import 'package:ams/app/constants/app_constants.dart';

class SuperAdminReportModel {
  num? srNo;
  String? reportNo;
  String? licenceNo;
  String? contactPerson;
  String? companyName;
  String? category;
  num? maxScore;
  num? marksScored;
  String? rating;
  String? compliance;
  String? auditDate;
  String? report;

  SuperAdminReportModel({
    this.srNo,
    this.reportNo,
    this.contactPerson,
    this.companyName,
    this.category,
    this.maxScore,
    this.marksScored,
    this.rating,
    this.compliance,
    this.auditDate,
    this.report,
  });

  Map<String,dynamic> toJson() => {
    AppStringConstants.srNo : srNo,
    AppStringConstants.reportNo : reportNo,
    AppStringConstants.contactPerson : contactPerson,
    AppStringConstants.companyName : companyName,
    AppStringConstants.category : category,
    AppStringConstants.maxScore : maxScore,
    AppStringConstants.marksScored : marksScored,
    AppStringConstants.rating : rating,
    AppStringConstants.compliance : compliance,
    AppStringConstants.auditDate : auditDate,
    AppStringConstants.report : report,

  };
}
