import 'package:ams/app/constants/app_constants.dart';

class SuperAdminAuditorViewModel {
  num? srNo;
  String? auditorName;
  String? address;
  int? audits;

  SuperAdminAuditorViewModel({
    this.srNo,
    this.auditorName,
    this.address,
    this.audits,
  });

  factory SuperAdminAuditorViewModel.fromJson(Map<String, dynamic> json) => SuperAdminAuditorViewModel(
        srNo: json[AppStringConstants.srNo],
        auditorName: json[AppStringConstants.auditorName],
        address: json[AppStringConstants.address],
        audits: json[AppStringConstants.audits],
      );

  Map<String, dynamic> toJson() => {
        AppStringConstants.srNo: srNo,
        AppStringConstants.auditorName: auditorName,
        AppStringConstants.address: address,
        AppStringConstants.audits: audits,
      };
}

class AuditModel {
  num? srNo;
  String? licenceNo;
  String? companyName;
  String? representativeName;
  String? auditReport;

  AuditModel({
    this.srNo,
    this.licenceNo,
    this.companyName,
    this.representativeName,
    this.auditReport,
  });

  factory AuditModel.fromJson(Map<String, dynamic> json) => AuditModel(
        srNo: json[AppStringConstants.srNo],
        licenceNo: json[AppStringConstants.licenseNo],
        companyName: json[AppStringConstants.companyName],
        representativeName: json[AppStringConstants.representativeName],
        auditReport: json[AppStringConstants.auditReport],
      );

  Map<String, dynamic> toJson() => {
        AppStringConstants.srNo : srNo,
        AppStringConstants.licenseNo: licenceNo,
        AppStringConstants.companyName: companyName,
        AppStringConstants.representativeName: representativeName,
        AppStringConstants.auditReport: auditReport,
      };
}
