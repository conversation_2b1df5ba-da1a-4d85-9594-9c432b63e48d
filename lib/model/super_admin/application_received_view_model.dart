import 'package:ams/app_imports.dart';

class SuperAdminApplicationReceivedViewModel {
  num? srNo;
  int? agencyId;
  String? agencyName;
  String? state;
  String? address;
  String? mobile;
  String? status;
  String? remarks;
  num? auditors;
  num? audits;
  String? viewDetails;
  String? verification;

  SuperAdminApplicationReceivedViewModel({
    this.srNo,
    this.agencyName,
    this.state,
    this.address,
    this.mobile,
    this.status,
    this.remarks,
    this.auditors,
    this.audits,
    this.viewDetails,
    this.verification,
    this.agencyId,
  });

  Map<String, dynamic> toJson() => {
    AppStringConstants.srNo: srNo,
    AppStringConstants.agencyName: agencyName,
    AppStringConstants.state: state,
    AppStringConstants.address: address,
    AppStringConstants.mobile: mobile,
    AppStringConstants.status: status,
    AppStringConstants.remarks: remarks,
    AppStringConstants.auditors: auditors,
    AppStringConstants.audits: audits,
    AppStringConstants.viewDetails: viewDetails,
    AppStringConstants.verifyReject: verification,
  };
}
