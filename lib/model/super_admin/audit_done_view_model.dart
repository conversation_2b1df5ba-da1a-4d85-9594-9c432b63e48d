import 'package:ams/app_imports.dart';

class SuperAdminAuditDoneModel {
  final int? srNo;
  final String? reportNo;
  final String? fboName;
  final String? licenseNo;
  final String? stateDistrict;
  final String? agencyName;
  final String? auditorName;
  final String? kob;
  final DateTime? submitDate;
  final DateTime? auditStartDate;
  final DateTime? auditEndDate;
  final String report;

  SuperAdminAuditDoneModel({
    this.srNo,
    this.reportNo,
    this.licenseNo,
    this.auditorName,
    this.agencyName,
    this.fboName,
    this.stateDistrict,
    this.kob,
    this.submitDate,
    this.auditStartDate,
    this.auditEndDate,
    this.report = '',
  });

  Map<String, dynamic> toJson() => {
        AppStringConstants.srNo: srNo,
        AppStringConstants.reportNo: reportNo,
        AppStringConstants.fboName: fboName,
        AppStringConstants.licenseNo: licenseNo,
        AppStringConstants.stateDistrict: stateDistrict,
        AppStringConstants.agency: agencyName,
        AppStringConstants.auditorName: auditorName,
        AppStringConstants.kob: kob,
        AppStringConstants.auditStartDate: auditStartDate,
        AppStringConstants.auditEndDate: auditEndDate,
        AppStringConstants.submitDate: submitDate,
        AppStringConstants.report: report,
      };
}
