import 'package:ams/app_imports.dart';

class SuperAdminEmpanelledAgencyModel {
  num? srNo;
  int? agencyId;
  String? agencyName;
  String? state;
  String? address;
  String? mobile;
  String? status;
  String? remarks;
  num? auditors;
  num? audits;
  String? viewDetails;
  String? actions;
  String? agencyStatus;
  String? auditingScope;
  String? geoGraphicalArea;

  SuperAdminEmpanelledAgencyModel({
    this.srNo,
    this.agencyName,
    this.state,
    this.address,
    this.mobile,
    this.status,
    this.remarks,
    this.auditors,
    this.viewDetails,
    this.actions,
    this.agencyStatus,
    this.auditingScope,
    this.audits,
    this.geoGraphicalArea,
    this.agencyId,
  });

  factory SuperAdminEmpanelledAgencyModel.fromJson(Map<String, dynamic> json) => SuperAdminEmpanelledAgencyModel(
        srNo: json[AppStringConstants.srNo],
        agencyName: json[AppStringConstants.agencyName],
        state: json[AppStringConstants.state],
        address: json[AppStringConstants.address],
        mobile: json[AppStringConstants.mobile],
        status: json[AppStringConstants.status],
        remarks: json[AppStringConstants.remarks],
        auditors: json[AppStringConstants.auditors],
        viewDetails: json[AppStringConstants.viewDetails],
        actions: json[AppStringConstants.actions],
        agencyStatus: json[AppStringConstants.agencyStatus],
        auditingScope: json[AppStringConstants.auditingScope],
        audits: json[AppStringConstants.audits],
        geoGraphicalArea: json[AppStringConstants.geographicalArea],
      );

  Map<String, dynamic> toJson() => {
        AppStringConstants.srNo: srNo,
        AppStringConstants.agencyName: agencyName,
        AppStringConstants.state: state,
        AppStringConstants.address: address,
        AppStringConstants.mobile: mobile,
        AppStringConstants.status: status,
        AppStringConstants.remarks: remarks,
        AppStringConstants.auditors: auditors,
        AppStringConstants.audits: audits,
        AppStringConstants.viewDetail: viewDetails,
        AppStringConstants.actions: actions,
        AppStringConstants.agencyStatus: agencyStatus,
        AppStringConstants.auditingScope: auditingScope,
        AppStringConstants.geographicalArea: geoGraphicalArea,
        AppStringConstants.agency : agencyId,
      };
}
