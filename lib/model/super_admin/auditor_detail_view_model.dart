import 'package:ams/app_imports.dart';

class SuperAdminAuditorDetailsViewModel {
  final num? srNo;
  final String? auditorName;
  final String? emailAndMobile;
  final String? agencyName;
  final String? auditsConducted;
  final String? experience;
  final String? qualification;
  final String? auditorStatus;
  final List<String> documentAttached;

  SuperAdminAuditorDetailsViewModel({
    this.srNo,
    this.auditorName,
    this.emailAndMobile,
    this.agencyName,
    this.auditsConducted,
    this.experience,
    this.qualification,
    this.auditorStatus,
    this.documentAttached = const [AppStringConstants.leadAuditorCertificateEducationalQualificationCertificate],
  });


  Map<String, dynamic> toJson() => {
    AppStringConstants.srNo: srNo,
    AppStringConstants.auditorName: auditorName,
    AppStringConstants.emailAndMobile: emailAndMobile,
    AppStringConstants.agencyName: agencyName,
    AppStringConstants.auditsConducted: auditsConducted,
    AppStringConstants.experience: experience,
    AppStringConstants.qualification: qualification,
    AppStringConstants.status: auditorStatus,
    AppStringConstants.documentAttached: documentAttached,
  };

}

class AuditorQualificationDetails {
  final num? srNo;
  final String? qualificationCertificate;
  final String? passingYear;
  final String? university;
  final num? percentageGrade;
  final String? subject;

  const AuditorQualificationDetails(
      {this.srNo, this.passingYear, this.percentageGrade, this.qualificationCertificate, this.subject, this.university,});

  Map<String, dynamic> toJson() => {
    AppStringConstants.srNo: srNo,
    AppStringConstants.qualificationCertificateName: qualificationCertificate,
    AppStringConstants.passingYear: passingYear,
    AppStringConstants.boardUniversity: university,
    AppStringConstants.percentageGrade: percentageGrade,
    AppStringConstants.subject: subject,
  };
}

class AuditorExperienceDetails{
  final num? srNo;
  final String? organizationName;
  final String? designation;
  final String? from;
  final String? to;
  final String? experienceInMonth;
  AuditorExperienceDetails({this.srNo,this.organizationName,this.designation,this.from,this.to,this.experienceInMonth});
  Map<String,dynamic> toJson()=>{
    AppStringConstants.srNo: srNo,
    AppStringConstants.organizationName: organizationName,
    AppStringConstants.designation: designation,
    AppStringConstants.from: from,
    AppStringConstants.to: to,
    AppStringConstants.experienceInMonth: experienceInMonth,

  };

}
class ConductAuditDetails {
  final num? srNo;
  final String? licenseNo;
  final String? fboName;
  final String? state;
  final String? district;
  final String? agency;
  final String? auditStartDate;
  final String? auditEndDate;
  final String? auditReport;

  ConductAuditDetails({
    this.srNo,
    this.licenseNo,
    this.fboName,
    this.state,
    this.district,
    this.agency,
    this.auditStartDate,
    this.auditEndDate,
    this.auditReport,
  });

  Map<String, dynamic> toJson() => {
    AppStringConstants.srNo: srNo,
    AppStringConstants.licenseNo: licenseNo,
    AppStringConstants.fboName: fboName,
    AppStringConstants.state: state,
    AppStringConstants.district: district,
    AppStringConstants.agency: agency,
    AppStringConstants.auditStartDate: auditStartDate,
    AppStringConstants.auditEndDate: auditEndDate,
    AppStringConstants.auditReport: auditReport,
  };
}
