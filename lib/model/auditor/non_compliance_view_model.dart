import 'package:ams/app_imports.dart';

class AuditorNonComplianceAuditViewModel {
  final num? srNo;
  final String? idNo;
  final String? agencyName;
  final String? auditorName;
  final String? licenseNo;
  final String? contactPerson;
  final String? fboName;
  final String? address;
  final String? kob;
  final DateTime? dateOfRequest;
  final String? mobileNo;

  AuditorNonComplianceAuditViewModel({
    this.srNo,
    this.idNo,
    this.auditorName,
    this.agencyName,
    this.licenseNo,
    this.contactPerson,
    this.fboName,
    this.address,
    this.kob,
    this.dateOfRequest,
    this.mobileNo,
  });

  Map<String, dynamic> toJson() => {
    AppStringConstants.srNo: srNo,
    AppStringConstants.idNo: idNo,
    AppStringConstants.auditorName: auditorName,
    AppStringConstants.agencyName: agencyName,
    AppStringConstants.licenseNo: licenseNo,
    AppStringConstants.contactPerson: contact<PERSON>erson,
    AppStringConstants.fboName: fboName,
    AppStringConstants.address: address,
    AppStringConstants.kob: kob,
    AppStringConstants.dateOfRequest: dateOfRequest,
  };
}
