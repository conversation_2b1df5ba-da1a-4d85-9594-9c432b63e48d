import 'package:ams/app_imports.dart';

class CheckListModel {
  String? title;
  List<CheckListItem> checkListItem;
  bool isGroupOpen;

  CheckListModel({
    this.title,
    this.checkListItem = const <CheckListItem>[],
    this.isGroupOpen = true,
  });
}

class CheckListItem {
  String? srNo;
  String? question;
  String scoring;
  bool isMandatory;
  File? file;
  TextEditingController? comments;

  CheckListItem({
    this.srNo,
    this.question,
    this.scoring = AppStringConstants.na,
    this.isMandatory = false,
    this.file,
    this.comments,
  });
}
