import 'package:ams/app_imports.dart';

class AuditorApprovalPendingAuditViewModel {
  final num? srNo;
  final String? reportNo;
  final String? auditorName;
  final String? licenseNo;
  final String? contactPerson;
  final String? fboName;
  final String? address;
  final String? kob;
  final DateTime? date;
  final String report;
  final String? mobileNo;

  AuditorApprovalPendingAuditViewModel({
    this.srNo,
    this.reportNo,
    this.auditorName,
    this.licenseNo,
    this.contactPerson,
    this.fboName,
    this.address,
    this.kob,
    this.date,
    this.report = '',
    this.mobileNo,
  });

  Map<String, dynamic> toJson() => {
    AppStringConstants.srNo: srNo,
    AppStringConstants.reportNo: reportNo,
    AppStringConstants.auditorName: auditorName,
    AppStringConstants.licenseNo: licenseNo,
    AppStringConstants.contactPerson: contactPerson,
    AppStringConstants.fboName: fboName,
    AppStringConstants.address: address,
    AppStringConstants.kob: kob,
    AppStringConstants.date: date,
    AppStringConstants.report: report,
  };
}
