import 'package:ams/app_imports.dart';

class AuditorReceivedAuditViewModel {
  final num? srNo;
  final String? idNo;
  final String? agencyName;
  final String? auditorName;
  final String? licenseNo;
  final String? contactPerson;
  final String? fboName;
  final String? address;
  final String? kob;
  final DateTime? dateOfRequest;
  final String report;
  final String? mobileNo;
  final bool isResubmit;

  AuditorReceivedAuditViewModel({
    this.srNo,
    this.idNo,
    this.auditorName,
    this.agencyName,
    this.licenseNo,
    this.contactPerson,
    this.fboName,
    this.address,
    this.kob,
    this.dateOfRequest,
    this.report = '',
    this.mobileNo,
    this.isResubmit = false,
  });

  Map<String, dynamic> toJson() => {
        AppStringConstants.srNo: srNo,
        AppStringConstants.idNo: idNo,
        AppStringConstants.auditorName: auditorName,
        AppStringConstants.agencyName: agencyName,
        AppStringConstants.licenseNo: licenseNo,
        AppStringConstants.contactPerson: contactPerson,
        AppStringConstants.fboName: fboName,
        AppStringConstants.address: address,
        AppStringConstants.kob: kob,
        AppStringConstants.dateOfRequest: dateOfRequest,
        AppStringConstants.report: report,
      };
}
