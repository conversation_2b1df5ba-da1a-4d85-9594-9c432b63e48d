import 'package:ams/app_imports.dart';

class ThirdPartyModificationAuditModel {
  final String? reportNo;
  final DateTime? auditStartDate;
  final DateTime? auditEndDate;
  final String? auditorName;
  final String? auditorContactNumber;
  final String? fboRepresentativeName;
  final String? email;
  final String? fboFssaiLicenseNo;
  final String? companyName;
  final String? address;
  final String? mobileNumber;
  final TextEditingController? majorNonConformity;
  final TextEditingController? minorNonConformity;
  final TextEditingController? comments;
  File? auditorAgreementFile;
  File? confidentialityFile;
  File? otherFile;
  String? auditorAgreementUrl;
  String? confidentialityUrl;
  String? otherUrl;

  ThirdPartyModificationAuditModel({
    this.reportNo,
    this.auditStartDate,
    this.auditEndDate,
    this.auditorName,
    this.auditorContactNumber,
    this.fboRepresentativeName,
    this.email,
    this.fboFssaiLicenseNo,
    this.companyName,
    this.address,
    this.mobileNumber,
    this.majorNonConformity,
    this.minorNonConformity,
    this.comments,
    this.auditorAgreementFile,
    this.confidentialityFile,
    this.otherFile,
    this.auditorAgreementUrl,
    this.confidentialityUrl,
    this.otherUrl,
  });

  Map<String, dynamic> toJson() => {
        AppStringConstants.reportNo: reportNo,
        AppStringConstants.auditStartDate: auditStartDate,
        AppStringConstants.auditEndDate: auditEndDate,
        AppStringConstants.auditorName: auditorName,
        AppStringConstants.auditorContactNo: auditorContactNumber,
        AppStringConstants.companyName: companyName,
        AppStringConstants.fboRepresentativeName: fboRepresentativeName,
        AppStringConstants.address: address,
        AppStringConstants.email: email,
        AppStringConstants.mobileNo: mobileNumber,
        AppStringConstants.fboFssaiLicenseNo: fboFssaiLicenseNo,
      };
}

class AuditModificationItem {
  String? srNo;
  String? question;
  String? pointScored;
  String? maxPoints;
  bool isMandatory;
  String? fileUrl;
  String? narration;
  File? file;
  TextEditingController? comments;

  AuditModificationItem({
    this.srNo,
    this.question,
    this.pointScored = AppStringConstants.na,
    this.isMandatory = false,
    this.file,
    this.comments,
    this.fileUrl,
    this.maxPoints,
    this.narration,
  });

  Map<String, dynamic> toJson() => {
    AppStringConstants.srNo: srNo,
    AppStringConstants.question: question,
    AppStringConstants.maxPoints: maxPoints,
    AppStringConstants.pointsScored: pointScored,
    AppStringConstants.filesUploaded: fileUrl,
    AppStringConstants.narration: narration,
  };
}
