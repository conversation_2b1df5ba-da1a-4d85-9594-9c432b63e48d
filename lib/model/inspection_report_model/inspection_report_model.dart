import 'package:ams/app_imports.dart';

class ThirdPartyAuditModel {
  final String? reportNo;
  final DateTime? auditStartDate;
  final DateTime? auditEndDate;
  final String? auditorName;
  final String? auditorContactNumber;
  final String? fboRepresentativeName;
  final String? email;
  final String? fboFssaiLicenseNo;
  final String? companyName;
  final String? address;
  final String? mobileNumber;
  final String? majorNonConformity;
  final String? minorNonConformity;
  final String? comments;
  final String? filesUploaded;
  final String? auditorAgreementFile;
  final String? confidentialityFile;
  final String? otherFile;

  ThirdPartyAuditModel({
    this.reportNo,
    this.auditStartDate,
    this.auditEndDate,
    this.auditorName,
    this.auditorContactNumber,
    this.fboRepresentativeName,
    this.email,
    this.fboFssaiLicenseNo,
    this.companyName,
    this.address,
    this.mobileNumber,
    this.majorNonConformity,
    this.minorNonConformity,
    this.comments,
    this.filesUploaded,
    this.auditorAgreementFile,
    this.confidentialityFile,
    this.otherFile,
  });

  Map<String, dynamic> toJson() => {
        AppStringConstants.reportNo: reportNo,
        AppStringConstants.auditStartDate: auditStartDate,
        AppStringConstants.auditEndDate: auditEndDate,
        AppStringConstants.auditorName: auditorName,
        AppStringConstants.auditorContactNo: auditorContactNumber,
        AppStringConstants.companyName: companyName,
        AppStringConstants.fboRepresentativeName: fboRepresentativeName,
        AppStringConstants.address: address,
        AppStringConstants.email: email,
        AppStringConstants.mobileNo: mobileNumber,
        AppStringConstants.fboFssaiLicenseNo: fboFssaiLicenseNo,
        AppStringConstants.majorNonConformity: majorNonConformity,
        AppStringConstants.minorNonConformity: minorNonConformity,
        AppStringConstants.comments: comments,
        AppStringConstants.filesUploaded: filesUploaded,
      };
}

class InspectionItem {
  int? srNo;
  String? question;
  String? maxPoints;
  String? pointScored;
  String? fileUrl;
  String? comments;
  bool isMandatory;

  InspectionItem({
    this.srNo,
    this.question,
    this.maxPoints,
    this.pointScored,
    this.fileUrl,
    this.comments,
    this.isMandatory = false,
  });

  Map<String, dynamic> toJson() => {
        AppStringConstants.srNo: srNo,
        AppStringConstants.question: question,
        AppStringConstants.maxPoints: maxPoints,
        AppStringConstants.pointsScored: pointScored,
        AppStringConstants.fileUploaded: fileUrl,
        AppStringConstants.comments: comments,
      };
}
