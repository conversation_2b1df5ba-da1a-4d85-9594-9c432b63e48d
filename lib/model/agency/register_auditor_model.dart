import 'package:ams/app_imports.dart';

class ExperienceModelController {
  TextEditingController orgNameController;
  String orgNameError;
  TextEditingController designationController;
  String designationErrorText;
  TextEditingController fromController;
  String fromErrorText;
  TextEditingController toController;
  String toErrorText;
  TextEditingController experienceMonthController;
  String experienceMonthError;
  ExperienceModelController({
    required this.orgNameController,
    required this.designationController,
    required this.fromController,
    required this.toController,
    required this.experienceMonthController,
    this.toErrorText = '',
    this.fromErrorText = '',
    this.designationErrorText = '',
    this.experienceMonthError = '',
    this.orgNameError = '',
  });
}

class QualificationModel {
  TextEditingController degreeController;
  String degreeError;
  TextEditingController subjectController;
  String subjectErrorText;
  TextEditingController collegeController;
  String collegeErrorText;
  TextEditingController passingYearController;
  String passingYearErrorText;
  TextEditingController percentageController;
  String percentageError;
  QualificationModel({
    required this.degreeController,
    required this.subjectController,
    required this.collegeController,
    required this.passingYearController,
    required this.percentageController,
    this.collegeErrorText = '',
    this.degreeError = '',
    this.passingYearErrorText = '',
    this.percentageError = '',
    this.subjectErrorText = '',
  });
}
