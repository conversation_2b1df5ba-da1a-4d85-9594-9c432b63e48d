import 'package:ams/app/constants/app_constants.dart';
import 'package:ams/app_imports.dart';
class ApprovedAuditDetails {
  final num? sNo;
  final String? reportNo;
  final String? fboName;
  final String? licenseNo;
  final String? districtState;
  final String? agency;
  final String? auditor;
  final String? kob;
  final DateTime? auditStartDate;
  final DateTime? auditEndDate;
  final DateTime? submissionDate;
  final String viewDownload;

  ApprovedAuditDetails({
    this.sNo,
    this.reportNo,
    this.fboName,
    this.licenseNo,
    this.districtState,
    this.agency,
    this.auditor,
    this.kob,
    this.auditStartDate,
    this.auditEndDate,
    this.submissionDate,
    this.viewDownload='',
  });

  Map<String, dynamic> toJson() => {
    AppStringConstants.srNo: sNo,
    AppStringConstants.reportNo: reportNo,
    AppStringConstants.fboName: fboName,
    AppStringConstants.licenseNo: licenseNo,
    AppStringConstants.districtState: districtState,
    AppStringConstants.agency: agency,
    AppStringConstants.auditor: auditor,
    AppStringConstants.kob: kob,
    AppStringConstants.auditStartDate: auditStartDate,
    AppStringConstants.auditEndDate: auditEndDate,
    AppStringConstants.submissionDate: submissionDate,
    AppStringConstants.report: viewDownload,
  };
}
