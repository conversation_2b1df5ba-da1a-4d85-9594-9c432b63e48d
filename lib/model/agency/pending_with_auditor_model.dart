import 'package:ams/app/constants/app_constants.dart';
import 'package:ams/app_imports.dart';
class PendingWithAuditorDetails {
  final num? srNo;
  final String? idNo;
  final String? agencyName;
  final String? auditorName;
  final String? licenseNo;
  final String? contactPerson;
  final String? fboName;
  final String? address;
  final String? kob;
  final DateTime? dateOfRequest;
  final String isView;

  PendingWithAuditorDetails({
    this.srNo,
    this.idNo,
    this.agencyName,
    this.auditorName,
    this.licenseNo,
    this.contactPerson,
    this.fboName,
    this.address,
    this.kob,
    this.dateOfRequest,
    this.isView='view',
  });

  Map<String, dynamic> toJson() => {
    AppStringConstants.srNo: srNo,
    AppStringConstants.idNo: idNo,
    AppStringConstants.agencyName: agencyName,
    AppStringConstants.auditorName: auditorName,
    AppStringConstants.licenseNo: licenseNo,
    AppStringConstants.contactPerson: contactPerson,
    AppStringConstants.fboName: fboName,
    AppStringConstants.address: address,
    AppStringConstants.kob: kob,
    AppStringConstants.dateOfRequest: dateOfRequest,
    AppStringConstants.report:isView,
  };
}
