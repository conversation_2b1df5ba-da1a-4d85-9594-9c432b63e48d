import 'package:ams/app_imports.dart';

class YourAuditorDetails {
  final num? srNo;
  final String? auditorName;
  final String? emailAndMobile;
  final String? agencyName;
  final String? auditsConducted;
  final String? experience;
  final String? qualification;
  final bool removeAuditor;
  final int? auditorId;
  final String? leadAuditorCertificate;
  final String? qualificationFile;
  final String? auditorLogFile;
  final String? sectorSpecificFile;

  YourAuditorDetails({
    this.srNo,
    this.auditorName,
    this.emailAndMobile,
    this.agencyName,
    this.auditsConducted,
    this.experience,
    this.qualification,
    this.removeAuditor = true,
    this.auditorId,
    this.leadAuditorCertificate,
    this.qualificationFile,
    this.auditorLogFile,
    this.sectorSpecificFile,
  });


  Map<String, dynamic> toJson() => {
        AppStringConstants.srNo: srNo,
        AppStringConstants.auditorName: auditorName,
        AppStringConstants.emailAndMobile: emailAndMobile,
        AppStringConstants.agencyName: agencyName,
        AppStringConstants.auditsConducted: auditsConducted,
        AppStringConstants.experience: experience,
        AppStringConstants.qualification: qualification,
        AppStringConstants.removeAuditor: removeAuditor,
        AppStringConstants.leadAuditorCertificateEducationalQualificationCertificate: leadAuditorCertificate,
        AppStringConstants.educationalQualificationCertificate: qualificationFile,
        AppStringConstants.auditorLog: auditorLogFile,
        AppStringConstants.sectorSpecificKnowledge: sectorSpecificFile,
      };

}

class QualificationDetails {
  final num? srNo;
  final String? qualificationCertificate;
  final String? passingYear;
  final String? university;
  final String? percentageGrade;
  final String? subject;

  const QualificationDetails(
      {this.srNo, this.passingYear, this.percentageGrade, this.qualificationCertificate, this.subject, this.university,});

  Map<String, dynamic> toJson() => {
        AppStringConstants.srNo: srNo,
        AppStringConstants.qualificationCertificateName: qualificationCertificate,
        AppStringConstants.passingYear: passingYear,
        AppStringConstants.boardUniversity: university,
        AppStringConstants.percentageGrade: percentageGrade,
        AppStringConstants.subject: subject,
      };
}

class ExperienceDetails{
  final num? srNo;
  final String? organizationName;
  final String? designation;
  final String? from;
  final String? to;
  final String? experienceInMonth;
  ExperienceDetails({this.srNo,this.organizationName,this.designation,this.from,this.to,this.experienceInMonth});
  Map<String,dynamic> toJson()=>{
    AppStringConstants.srNo: srNo,
    AppStringConstants.organizationName: organizationName,
    AppStringConstants.designation: designation,
    AppStringConstants.from: from,
    AppStringConstants.to: to,
    AppStringConstants.experienceInMonth: experienceInMonth,

  };

}
class AuditDetailsReport {
  final num? srNo;
  final String? licenseNo;
  final String? fboName;
  final String? state;
  final String? district;
  final String? agency;
  final String? auditStartDate;
  final String? auditEndDate;
  final String? auditSubmissionDate;
  final String? auditReport;

  AuditDetailsReport({
    this.srNo,
    this.licenseNo,
    this.fboName,
    this.state,
    this.district,
    this.agency,
    this.auditStartDate,
    this.auditEndDate,
    this.auditSubmissionDate,
    this.auditReport,
  });

  Map<String, dynamic> toJson() => {
    AppStringConstants.srNo: srNo,
    AppStringConstants.licenseNo: licenseNo,
    AppStringConstants.fboName: fboName,
    AppStringConstants.state: state,
    AppStringConstants.district: district,
    AppStringConstants.agency: agency,
    AppStringConstants.auditStartDate: auditStartDate,
    AppStringConstants.auditEndDate: auditEndDate,
    AppStringConstants.auditSubmissionDate: auditSubmissionDate,
    AppStringConstants.auditReport: auditReport,
  };
}
