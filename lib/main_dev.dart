import 'package:ams/app_imports.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  AppConfig.create(
    appName: AppStringConstants.appDevName,
    flavor: Flavor.dev,
  );
  HttpOverrides.global = MyHttpOverrides();
  SystemChrome.setPreferredOrientations(
    [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown],
  );
  initRepositories();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    var botToastBuilder = BotToastInit();
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus!.unfocus(),
      child: GetMaterialApp.router(
        routerDelegate: router.routerDelegate,
        backButtonDispatcher: router.backButtonDispatcher,
        routeInformationParser: router.routeInformationParser,
        routeInformationProvider: router.routeInformationProvider,
        title: AppStringConstants.appDevName,
        theme: ThemeData(
          scaffoldBackgroundColor: AppColorConstants.colorWhite,
          fontFamily: AppAssetsConstants.defaultFont,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
        debugShowCheckedModeBanner: false,
        defaultTransition: Transition.rightToLeft,
        translations: AppLocalization(),
        locale: Get.deviceLocale,
        transitionDuration: const Duration(microseconds: 800),
        navigatorObservers: <NavigatorObserver>[
          BotToastNavigatorObserver(),
        ],
        scrollBehavior: ScrollHelper(),
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
            child: child = botToastBuilder(context, child),
          );
        },
      ),
    );
  }
}
