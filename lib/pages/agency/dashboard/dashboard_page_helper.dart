import 'package:ams/app_imports.dart';

class AgencyDashboardPageHelper {
  final AgencyDashboardPageState _state;
  final GlobalKey<ScaffoldState> agencyDrawerKey = GlobalKey<ScaffoldState>();
  String selectedDrawerItem = AppStringConstants.dashboard;
  String selectedDashboardItem = '';
  AgencyDashboardModel agencyDashboardModel = AgencyDashboardModel();
  final List<Map<String, dynamic>> drawerItem = [
    {
      'icon': AppAssetsConstants.icDrawerDashboard,
      'title': AppStringConstants.dashboard,
    },
    {
      'icon': AppAssetsConstants.icPersonalInfo,
      'title': AppStringConstants.personalInfo,
    },
    {
      'icon': AppAssetsConstants.icPeople,
      'title': AppStringConstants.yourAuditors,
    },
    {
      'icon': AppAssetsConstants.icRegister,
      'title': AppStringConstants.registerAuditor,
    },
    {
      'icon': AppAssetsConstants.icPersonApproved,
      'title': AppStringConstants.assignAudit,
    },
    {
      'icon': AppAssetsConstants.icChangePassword,
      'title': AppStringConstants.changePassword,
    },
    {
      'icon': AppAssetsConstants.icLogOut,
      'title': AppStringConstants.logOut,
    },
  ];
  ApiStatus apiStatus = ApiStatus.initial;

  AgencyDashboardPageHelper(this._state){
    Future.delayed((const Duration(milliseconds: 200)),()=>getDashboardData());
  }

  ///--------------------------------Functions------------------------///
  
  Future<void> getDashboardData()async{
    apiStatus=ApiStatus.loading;
    _state.agencyDashboardController.update();
    _state.agencyDashboardController.getAgencyDashboardModel(323,392).then((value){
      agencyDashboardModel=value;
      apiStatus=ApiStatus.success;
      _state.agencyDashboardController.update();
    });
  }
  void drawerOnTap(String item) {
    agencyDrawerKey.currentState!.closeDrawer();
    selectedDrawerItem = item;
    if (item == AppStringConstants.dashboard) {
    } else if (item == AppStringConstants.yourAuditors) {
      Future.delayed((const Duration(milliseconds: 300)), () => gotoAgencyYourAuditorPage(agencyId: 323));
    } else if (item == AppStringConstants.changePassword) {
      Future.delayed((const Duration(milliseconds: 300)), () => gotoChangePasswordPage());
    } else if (item == AppStringConstants.assignAudit) {
      gotoAssignAuditPage();
    } else if (item == AppStringConstants.personalInfo) {
      Future.delayed((const Duration(milliseconds: 300)), () => gotoPersonalInfoPage());
    } else if (item == AppStringConstants.registerAuditor) {
      gotoRegisterAuditorPage();
    } else if (item == AppStringConstants.logOut) {
      Future.delayed((const Duration(milliseconds: 300)), () => logoutDialog(context: _state.context));
    }
    _state.agencyDashboardController.update();
  }

  void dashboardItemOnTap(String text) {
    if (text == AppStringConstants.yourAuditors) {
      gotoAgencyYourAuditorPage(agencyId: 323);
    } else if (text == AppStringConstants.pendingWithAuditor) {
      gotoPendingWithAuditorPage();
    } else if (text == AppStringConstants.cancelled) {
      gotoAgencyCancelledAuditPage();
    } else if (text == AppStringConstants.approved) {
      gotoAgencyApprovedPage();
    } else if (text == AppStringConstants.nonCompliance) {
      gotoAgencyNonCompliancePage();
    } else if (text == AppStringConstants.applicationReceived) {
      gotApplicationReceivedPage();
    }
  }
}
