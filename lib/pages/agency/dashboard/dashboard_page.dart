import 'package:ams/app_imports.dart';

class AgencyDashboardPage extends StatefulWidget {
  const AgencyDashboardPage({super.key});

  @override
  State<AgencyDashboardPage> createState() => AgencyDashboardPageState();
}

class AgencyDashboardPageState extends State<AgencyDashboardPage> {
  AgencyDashboardPageHelper? _agencyDashboardPageHelper;
  late AgencyDashboardController agencyDashboardController;

  @override
  Widget build(BuildContext context) {
    _agencyDashboardPageHelper = _agencyDashboardPageHelper ?? (_agencyDashboardPageHelper = AgencyDashboardPageHelper(this));
    return GetBuilder(
      init: AgencyDashboardController(),
      builder: (AgencyDashboardController controller) {
        agencyDashboardController = controller;
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: getAppStatusBar(),
          child: Stack(
            children: [
              Scaffold(
                key: _agencyDashboardPageHelper!.agencyDrawerKey,
                drawer: _drawerView(),
                appBar: _appBarView(),
                body: _bodyView(),
              ),
              if (ApiStatus.loading == _agencyDashboardPageHelper!.apiStatus) const AppLoader(), //AppLoader(),
            ],
          ),
        );
      },
    );
  }

  Drawer _drawerView() {
    return Drawer(
      backgroundColor: AppColorConstants.colorWhite,
      child: ListView(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                height: 180,
                width: double.infinity,
                color: AppColorConstants.colorAppPrimary,
                child: AppImageAsset(
                  image: AppAssetsConstants.agencyDrawerProfile,
                ),
              ),
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10).copyWith(bottom: 5),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColorConstants.colorWhite,
                    ),
                    child: ClipRRect(
                      child: AppImageAsset(
                        image: AppAssetsConstants.icProfile,
                        height: 45,
                        width: 30,
                      ),
                    ),
                  ),
                  const AppText(
                    'Welcome user',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColorConstants.colorWhite,
                  ),
                ],
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(22.0),
            child: Column(
              children: _agencyDashboardPageHelper!.drawerItem.map((item) {
                return _drawerTile(item['title'], item['icon']);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _drawerTile(String name, String image) {
    return InkWell(
      onTap: () => _agencyDashboardPageHelper!.drawerOnTap(name),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5),
        padding: EdgeInsets.only(
          top: 11,
          bottom: 11,
          left: name == AppStringConstants.logOut ? 15 : 10,
        ),
        width: double.infinity,
        height: 45,
        decoration: BoxDecoration(
          color: _agencyDashboardPageHelper!.selectedDrawerItem == name
              ? AppColorConstants.colorAppPrimary
              : AppColorConstants.colorWhite,
          borderRadius: BorderRadius.circular(100),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            AppImageAsset(
              image: image,
              height: 23,
              width: 23,
              color: _agencyDashboardPageHelper!.selectedDrawerItem == name
                  ? AppColorConstants.colorWhite
                  : name == AppStringConstants.logOut
                      ? AppColorConstants.colorRedAccent
                      : AppColorConstants.colorDarkGrey,
            ),
            const SizedBox(width: 15),
            AppText(
              name.tr,
              color: _agencyDashboardPageHelper!.selectedDrawerItem == name
                  ? AppColorConstants.colorWhite
                  : name == AppStringConstants.logOut
                      ? AppColorConstants.colorRedAccent
                      : AppColorConstants.colorDarkGrey,
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _bodyView() {
    return Column(
      children: [
        _dataView(),
      ],
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(
      title: AppStringConstants.dashboard.tr,
      titleSize: 18,
      isBack: false,
      leading: Padding(
        padding: const EdgeInsets.only(left: 0, bottom: 15, top: 15, right: 0),
        child: InkWell(
          onTap: () => _agencyDashboardPageHelper!.agencyDrawerKey.currentState?.openDrawer(),
          child: AppImageAsset(
            image: AppAssetsConstants.icMenu,
            height: 10,
            width: 10,
          ),
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: AppImageAsset(
            image: AppAssetsConstants.fssaiImage,
            width: 110,
          ),
        ),
      ],
    );
  }

  Widget _dataView() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DashboardCard(
                  text: AppStringConstants.yourAuditors,
                  value: _agencyDashboardPageHelper!.agencyDashboardModel.auditors.toString(),
                  image: AppAssetsConstants.icPeople,
                  color: AppColorConstants.colorLightGreen,
                  onTap: () => _agencyDashboardPageHelper!.dashboardItemOnTap(AppStringConstants.yourAuditors),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Expanded(
                child: DashboardCard(
                  text: AppStringConstants.pendingWithAuditor,
                  value: _agencyDashboardPageHelper!.agencyDashboardModel.pendingWithAuditor.toString(),
                  image: AppAssetsConstants.icPeople,
                  color: AppColorConstants.colorLightGreen,
                  onTap: () => _agencyDashboardPageHelper!.dashboardItemOnTap(AppStringConstants.pendingWithAuditor),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Expanded(
                child: DashboardCard(
                  text: AppStringConstants.approved,
                  value: _agencyDashboardPageHelper!.agencyDashboardModel.approved.toString(),
                  image: AppAssetsConstants.icApproved,
                  color: AppColorConstants.colorLightGreen,
                  onTap: () => _agencyDashboardPageHelper!.dashboardItemOnTap(AppStringConstants.approved),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Expanded(
                child: DashboardCard(
                  text: AppStringConstants.cancelled,
                  value: _agencyDashboardPageHelper!.agencyDashboardModel.cancelled.toString(),
                  image: AppAssetsConstants.icRejected,
                  color: AppColorConstants.colorLightRed,
                  onTap: () => _agencyDashboardPageHelper!.dashboardItemOnTap(AppStringConstants.cancelled),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Expanded(
                child: DashboardCard(
                  text: AppStringConstants.nonCompliance,
                  value: _agencyDashboardPageHelper!.agencyDashboardModel.noncompliance.toString(),
                  image: AppAssetsConstants.icRejected,
                  color: AppColorConstants.colorLightRed,
                  onTap: () => _agencyDashboardPageHelper!.dashboardItemOnTap(AppStringConstants.nonCompliance),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Expanded(
                child: DashboardCard(
                  text: AppStringConstants.applicationReceived,
                  value: _agencyDashboardPageHelper!.agencyDashboardModel.applicationReceived.toString(),
                  image: AppAssetsConstants.icApplicationReceived,
                  color: AppColorConstants.colorBlue,
                  onTap: () => _agencyDashboardPageHelper!.dashboardItemOnTap(AppStringConstants.applicationReceived),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
