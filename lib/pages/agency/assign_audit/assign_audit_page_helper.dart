import 'package:ams/app_imports.dart';

class AssignAuditPageHelper {
  final AssignAuditPageState _state;
  String? auditorsName;
  String? yesOrNo;
  TextEditingController licenceController = TextEditingController();
  TextEditingController fboController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController mobileController = TextEditingController();
  TextEditingController contactPersonController = TextEditingController();
  TextEditingController stateController = TextEditingController();
  TextEditingController districtController = TextEditingController();
  TextEditingController kobController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  String licenseErrorText='';
  List<String> demo = ['1', '2', '3', '4', '5', '6'];
  List<String> yerNo = ['Yes', 'No'];
  String auditorErrorText = '';
  String yerOrNoErrorText = '';
  ApiStatus apiStatus=ApiStatus.initial;
  AssignAuditPageHelper(this._state);

  void auditorOnChange(dynamic value) {
    auditorsName = value;
    _state.assignAuditController.update();
  }

  void yesOrNoOnChange(dynamic value) {
    yesOrNo = value;
    _state.assignAuditController.update();
  }
  void verifyLicenceOnTap(){
    if(licenceController.text.isEmpty){
      licenseErrorText='Please Enter License Number';
    }else{
      licenseErrorText='';
    }
    _state.assignAuditController.update();
  }
  void assignAuditOnTap(){
    if(auditorsName==null){
      auditorErrorText='Please Select Auditor';
    }else{
      auditorErrorText='';
    }
    _state.assignAuditController.update();

  }
}
