import 'package:ams/app_imports.dart';

class AssignAuditPage extends StatefulWidget {
  const AssignAuditPage({super.key});

  @override
  State<AssignAuditPage> createState() => AssignAuditPageState();
}

class AssignAuditPageState extends State<AssignAuditPage> {
  AssignAuditPageHelper? _assignAuditPageHelper;
  late AssignAuditController assignAuditController;
  @override
  Widget build(BuildContext context) {
    _assignAuditPageHelper = _assignAuditPageHelper ?? (AssignAuditPageHelper(this));
    return GetBuilder(
      init: AssignAuditController(),
      builder: (AssignAuditController controller) {
        assignAuditController = controller;
        return AppScaffold(
          appBar: _appBarView(),
          body: _bodyView(),
          apiStatus: _assignAuditPageHelper!.apiStatus,
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.assignAudit.tr);
  }

  Widget _bodyView() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: AppColorConstants.colorAppPrimary),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: 50,
                  decoration: const BoxDecoration(
                    color: AppColorConstants.colorAppPrimary,
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(8), topRight: Radius.circular(8)),
                  ),
                  child: Center(child: AppText(AppStringConstants.verifyLicense.tr,color: Colors.white,fontSize:18,fontWeight: FontWeight.w700,),),
                ),
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _commonTextField(AppStringConstants.enterLicenseNumber, _assignAuditPageHelper!.licenceController,errorText: _assignAuditPageHelper!.licenseErrorText),
                      AppButton(
                        onTap: ()=>_assignAuditPageHelper!.verifyLicenceOnTap(),
                        title: AppStringConstants.verifyLicense.tr,
                        height: 40,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: AppColorConstants.colorAppPrimary),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: 50,
                  decoration: const BoxDecoration(
                    color: AppColorConstants.colorAppPrimary,
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(8), topRight: Radius.circular(8)),
                  ),
                  child: Center(child: AppText(AppStringConstants.uploadInspectionReport.tr,color: Colors.white,fontSize:18,fontWeight: FontWeight.w700,),),

                ),
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _commonTextField(AppStringConstants.enterLicenseNumber, _assignAuditPageHelper!.licenceController),
                      _commonTextField(AppStringConstants.fbo, _assignAuditPageHelper!.fboController),
                      _commonTextField(AppStringConstants.email, _assignAuditPageHelper!.emailController),
                      _commonTextField(AppStringConstants.mobile, _assignAuditPageHelper!.mobileController),
                      _commonTextField(AppStringConstants.contactPerson, _assignAuditPageHelper!.contactPersonController),
                      _commonTextField(AppStringConstants.state, _assignAuditPageHelper!.stateController),
                      _commonTextField(AppStringConstants.district, _assignAuditPageHelper!.districtController),
                      _commonTextField(AppStringConstants.kob, _assignAuditPageHelper!.kobController),
                      _commonTextField(AppStringConstants.address, _assignAuditPageHelper!.addressController),
                      AppText(
                        AppStringConstants.auditorsName.tr,
                        fontWeight: FontWeight.w500,
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      AppDropdownButton(
                        errorText: _assignAuditPageHelper!.auditorErrorText,
                        hint: AppStringConstants.notSelected.tr,
                        value: _assignAuditPageHelper!.auditorsName,
                        onChanged: _assignAuditPageHelper!.auditorOnChange,
                        buttonHeight: 45,
                        dropdownMenuHeight: 130,
                        itemBuilder: _assignAuditPageHelper!.demo
                            .map(
                              (item) => DropdownMenuItem(
                                value: item,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 10),
                                  child: AppText(
                                    item,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                        selectedMenuItemBuilder: (context, child) {
                          return Padding(
                            padding: const EdgeInsets.all(10),
                            child: AppText(_assignAuditPageHelper!.auditorsName.toString()),
                          );
                        },
                        selectedItemBuilder: (context) {
                          return _assignAuditPageHelper!.demo
                              .map(
                                (e) => Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 5),
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: AppText(
                                      e,
                                      maxLines: 1,
                                      fontSize: 16,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              )
                              .toList();
                        },
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                      AppText(
                        AppStringConstants.stateUtSponsoredAudit.tr,
                        fontWeight: FontWeight.w500,
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      AppDropdownButton(
                        errorText: _assignAuditPageHelper!.yerOrNoErrorText,
                        buttonHeight: 45,
                        dropdownMenuHeight: 100,
                        itemPadding: const EdgeInsets.all(10),
                        hint: AppStringConstants.notSelected.tr,
                        value: _assignAuditPageHelper!.yesOrNo,
                        onChanged: _assignAuditPageHelper!.yesOrNoOnChange,
                        itemBuilder: _assignAuditPageHelper!.yerNo
                            .map(
                              (item) => DropdownMenuItem(
                                value: item,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 10),
                                  child: AppText(
                                    item,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                        selectedMenuItemBuilder: (context, child) {
                          return Padding(
                            padding: const EdgeInsets.all(10),
                            child: AppText(_assignAuditPageHelper!.yesOrNo.toString()),
                          );
                        },
                        selectedItemBuilder: (context) {
                          return _assignAuditPageHelper!.yerNo
                              .map(
                                (e) => Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 5),
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: AppText(
                                      e,
                                      maxLines: 1,
                                      fontSize: 16,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              )
                              .toList();
                        },
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                      AppButton(
                        onTap: () =>_assignAuditPageHelper!.assignAuditOnTap(),
                        title: AppStringConstants.assignAudit.tr,
                        height: 40,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _commonTextField(String hintText, TextEditingController textEditingController,{String? errorText}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          hintText.tr,
          fontWeight: FontWeight.w500,
        ),
        const SizedBox(
          height: 5,
        ),
        AppTextFormField(controller: textEditingController, hintText: hintText.tr,errorText: errorText??'',),
        const SizedBox(
          height: 12,
        ),
      ],
    );
  }
}
