import 'package:ams/app_imports.dart';

class AgencyApprovedPage extends StatefulWidget {
  const AgencyApprovedPage({super.key});

  @override
  State<AgencyApprovedPage> createState() => AgencyApprovedPageState();
}

class AgencyApprovedPageState extends State<AgencyApprovedPage> {
  AgencyApprovedPageHelper? _agencyApprovedPageHelper;
  late AgencyApprovedController agencyApprovedController;
  @override
  Widget build(BuildContext context) {
    _agencyApprovedPageHelper = _agencyApprovedPageHelper ?? (AgencyApprovedPageHelper(this));
    return GetBuilder(
      init: AgencyApprovedController(),
      builder: (AgencyApprovedController controller) {
        agencyApprovedController = controller;
        return AppScaffold(
          apiStatus: _agencyApprovedPageHelper!.apiStatus,
          appBar: _appBarView(),
          body: _bodyView(),
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.approvedAuditReports.tr);
  }

  Widget _bodyView() {
    return tableCard(
      isShowDropdown: true,
      isAscending: _agencyApprovedPageHelper!.isAscending,
      dropdownValue: _agencyApprovedPageHelper!.selectedValue,
      dropdownList: _agencyApprovedPageHelper!.dropdownList,
      isShowSearch: true,
      isDateRangeShow: true,
      isMainListEmpty: _agencyApprovedPageHelper!.isEmpty,
      submitDateOnTap: _agencyApprovedPageHelper!.submitOnTap,
      fromDateController: _agencyApprovedPageHelper!.fromDateController,
      fromDateOnTap: _agencyApprovedPageHelper!.fromDateOnTap,
      toDateController: _agencyApprovedPageHelper!.toDateController,
      toDateOnTap: _agencyApprovedPageHelper!.toDateOnTap,
      searchController: _agencyApprovedPageHelper!.searchController,
      sortOnTap: _agencyApprovedPageHelper!.sortOnTap,
      searchOnChanged: _agencyApprovedPageHelper!.searchOnChange,
      dropDownOnChanged: _agencyApprovedPageHelper!.dropDownOnChange,
      clearSearchOnTap: _agencyApprovedPageHelper!.clearValue,
      children: [
        for (var data in _agencyApprovedPageHelper!.searchList) _tableView(data.toJson()),
      ],
    );
  }

  Widget _tableView(Map<String, dynamic> item) {
    return Padding(
      padding: const EdgeInsets.all(12.0).copyWith(left: 0, right: 0),
      child: Table(
        border: TableBorder.all(
          color: AppColorConstants.colorMediumGrey,
          borderRadius: BorderRadius.circular(5),
        ),
        children: [
          for (var entry in item.entries)
            TableRow(
              children: [
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    alignment: Alignment.centerLeft,
                    color: AppColorConstants.colorLightPurple,
                    child: AppText(
                      entry.key.toString().tr,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
                entry.key == AppStringConstants.report
                    ? TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppButton(
                                textSize: 10,
                                height: 25,
                                width:80,
                                borderRadius: BorderRadius.circular(5),
                                onTap: () => gotoInspectionReportPage(),
                                title: AppStringConstants.view.tr,
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              AppButton(
                                textSize: 10,
                                height: 25,
                                width: 110,
                                borderRadius: BorderRadius.circular(5),
                                backColor: AppColorConstants.colorYellow,
                                onTap: () {},
                                title: AppStringConstants.auditReport.tr,
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              AppButton(
                                textSize: 10,
                                height: 25,
                                width: 110,
                                borderRadius: BorderRadius.circular(5),
                                backColor: AppColorConstants.colorLightGreen,
                                onTap: () {},
                                title: AppStringConstants.followUpReport.tr,
                              ),
                            ],
                          ),
                        ),
                      )
                    : ((entry.key == AppStringConstants.submissionDate) ||
                            (entry.key == AppStringConstants.auditStartDate) ||
                            (entry.key == AppStringConstants.auditEndDate))
                        ? TableCell(
                            verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: AppText(
                               entry.value == null ? DateHelper.getDDMMYYYYFormatDate(DateTime.now()) : DateHelper.getDDMMYYYYFormatDate(entry.value),
                                fontSize: 12,
                                color: AppColorConstants.colorBlack,
                              ),
                            ),
                          )
                        : TableCell(
                            verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: AppText(
                                entry.value.toString(),
                                fontSize: 12,
                                color: AppColorConstants.colorBlack,
                              ),
                            ),
                          ),
              ],
            ),
        ],
      ),
    );
  }
}
