import 'package:ams/app_imports.dart';

class AgencyApprovedPageHelper {
  final AgencyApprovedPageState _state;
  DateTime? fromDate;
  DateTime? toDate;
  List<ApprovedAuditDetails> approvedAuditList = [];
  List<ApprovedAuditDetails> searchList = [];
  String? selectedValue;
  bool isEmpty=false;
  TextEditingController searchController = TextEditingController();
  TextEditingController fromDateController = TextEditingController();
  TextEditingController toDateController = TextEditingController();
  bool isAscending = true;
  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.reportNo,
    AppStringConstants.fboName,
    AppStringConstants.licenseNo,
    AppStringConstants.districtState,
    AppStringConstants.agency,
    AppStringConstants.auditor,
    AppStringConstants.kob,
    AppStringConstants.auditStartDate,
    AppStringConstants.auditEndDate,
    AppStringConstants.submissionDate,
  ];

  ApiStatus apiStatus = ApiStatus.initial;

  List<AgencyApprovedAuditsModel> data = [];

  AgencyApprovedPageHelper(this._state) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () => getApplicationData(),
    );
  }

  Future<void> getApplicationData() async {
    apiStatus = ApiStatus.loading;
    _state.agencyApprovedController.update();
    setDefaultValue();
    await _state.agencyApprovedController.getAgencyApprovedAuditsData().then((value) {
      approvedAuditList = [];
      int srNo = 1;
      for (var model in value) {
        approvedAuditList.add(
          ApprovedAuditDetails(
            sNo: srNo++,
            reportNo: model.reportno,
            fboName: model.fboname,
            licenseNo: model.licenseno,
            districtState: model.district,
            agency: model.agencyname,
            auditor: model.auditorname,
            kob: model.kob,
            auditStartDate: DateFormat('dd/MM/yyyy').parse(model.auditstartdate),
            auditEndDate: DateFormat('dd/MM/yyyy').parse(model.auditenddate),
          ),
        );
      }
      if(approvedAuditList.isEmpty){
        isEmpty=true;
      }
      apiStatus = ApiStatus.success;
      searchList = approvedAuditList;
      _state.agencyApprovedController.update();
    });
  }

  void setDefaultValue() {
    selectedValue = AppStringConstants.srNo;
    searchController.clear();
    isAscending = true;
  }

  void searchOnChange(String text) {
    searchList = approvedAuditList.where((element) {
      return ((element.sNo.toString().toLowerCase().contains(text)) ||
          (element.submissionDate.toString().toLowerCase().contains(text)) ||
          (element.reportNo.toString().toLowerCase().contains(text)) ||
          (element.fboName.toString().toLowerCase().contains(text)) ||
          (element.licenseNo.toString().toLowerCase().contains(text)) ||
          (element.districtState.toString().toLowerCase().contains(text)) ||
          (element.agency.toString().toLowerCase().contains(text)) ||
          (element.auditor.toString().toLowerCase().contains(text)) ||
          (element.kob.toString().toLowerCase().contains(text)) ||
          (element.auditStartDate.toString().toLowerCase().contains(text)) ||
          (element.auditEndDate.toString().toLowerCase().contains(text)));
    }).toList();
    _state.agencyApprovedController.update();
  }

  void clearValue() {
    FocusScope.of(_state.context).unfocus();
    searchController.clear();
    searchList = approvedAuditList;
    _state.agencyApprovedController.update();
  }

  void dropDownOnChange(dynamic value) {
    selectedValue = value;
    sortOnchange();
    _state.agencyApprovedController.update();
  }

  void sortOnchange() {
    if (isAscending) {
      if (selectedValue == AppStringConstants.auditStartDate ||
          selectedValue == AppStringConstants.auditEndDate ||
          selectedValue == AppStringConstants.submissionDate) {
        searchList.sort(
          (a, b) => (a.toJson()[selectedValue] ?? DateTime.now()).compareTo(b.toJson()[selectedValue] ?? DateTime.now()),
        );
      } else {
        searchList.sort((a, b) => a.toJson()[selectedValue].compareTo(b.toJson()[selectedValue]));
      }
    } else {
      if (selectedValue == AppStringConstants.auditStartDate ||
          selectedValue == AppStringConstants.auditEndDate ||
          selectedValue == AppStringConstants.submissionDate) {
        searchList.sort(
          (a, b) => (b.toJson()[selectedValue] ?? DateTime.now()).compareTo(a.toJson()[selectedValue] ?? DateTime.now()),
        );
      } else {
        searchList.sort((a, b) => b.toJson()[selectedValue].compareTo(a.toJson()[selectedValue]));
      }
    }
    _state.agencyApprovedController.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortOnchange();
    _state.agencyApprovedController.update();
  }

  Future fromDateOnTap() async {
    fromDate = await appDatePicker(_state.context);
    fromDateController.text = DateHelper.getDDMMMMYYYYDateFormatDate(fromDate ?? DateTime.now());
    _state.agencyApprovedController.update();
  }

  Future toDateOnTap() async {
    toDate = await appDatePicker(_state.context);
    toDateController.text = DateHelper.getDDMMMMYYYYDateFormatDate(toDate ?? DateTime.now());
    _state.agencyApprovedController.update();
  }

  void submitOnTap() {
    int diff = toDate!.difference(fromDate!).inDays;
    if (diff < 0) {}
  }
}
