import 'package:ams/app_imports.dart';

class PendingWithAuditorPageHelper {
  final PendingWithAuditorPageState _state;
  TextEditingController searchController = TextEditingController();
  String? selectedValue;
  bool isAscending = false;
  final List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.idNo,
    AppStringConstants.agencyName,
    AppStringConstants.auditorName,
    AppStringConstants.licenseNo,
    AppStringConstants.contactPerson,
    AppStringConstants.fboName,
    AppStringConstants.address,
    AppStringConstants.kob,
    AppStringConstants.dateOfRequest,
  ];
  List<PendingWithAuditorDetails> pendingWithAuditorDetailsList = [];
  ApiStatus apiStatus = ApiStatus.initial;
  bool isEmpty = false;
  List<AgencyPendingWithAuditorModel> data = [];
  List<PendingWithAuditorDetails> searchList = [];
  PendingWithAuditorPageHelper(this._state) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () => getApplicationData(),
    );
  }

  Future<void> getApplicationData() async {
    apiStatus = ApiStatus.loading;
    _state.pendingWithAuditorController.update();
    setDefaultValue();
    await _state.pendingWithAuditorController.getAgencyPendingWithAuditorData().then((value) {
      pendingWithAuditorDetailsList=[];
      int srNo = 1;
      for (var model in value) {
        pendingWithAuditorDetailsList.add(
          PendingWithAuditorDetails(
            address: model.address,
            srNo: srNo++,
            idNo: model.inspectionreportid.toString(),
            agencyName: model.agencyname,
            auditorName: model.auditorname,
            licenseNo: model.licenseno,
            contactPerson: model.contactperson,
            fboName: model.fboname,
            kob: model.kob,
            dateOfRequest: DateFormat('dd/MM/yyyy').parse(model.dateofrequest),
          ),
        );
      }
      logs('pending with audior length ${pendingWithAuditorDetailsList.length}');
      searchList = pendingWithAuditorDetailsList;
      if (searchList.isEmpty) {
        isEmpty = true;
      }
      apiStatus = ApiStatus.success;
      _state.pendingWithAuditorController.update();
    });
  }

  void setDefaultValue() {
    selectedValue = AppStringConstants.srNo;
    searchController.clear();
    isAscending = true;
  }

  void searchOnChange(String text) {
    searchList = pendingWithAuditorDetailsList.where((element) {
      return ((element.srNo.toString().toLowerCase().contains(text)) ||
          (element.idNo.toString().toLowerCase().contains(text)) ||
          (element.agencyName.toString().toLowerCase().contains(text)) ||
          (element.auditorName.toString().toLowerCase().contains(text)) ||
          (element.licenseNo.toString().toLowerCase().contains(text)) ||
          (element.contactPerson.toString().toLowerCase().contains(text)) ||
          (element.address.toString().toLowerCase().contains(text)) ||
          (element.kob.toString().toLowerCase().contains(text)) ||
          (element.dateOfRequest.toString().toLowerCase().contains(text)) ||
          (element.fboName.toString().toLowerCase().contains(text)));
    }).toList();

    _state.pendingWithAuditorController.update();
  }

  void clearValue() {
    FocusScope.of(_state.context).unfocus();
    searchController.clear();
    searchList = pendingWithAuditorDetailsList;
    _state.pendingWithAuditorController.update();
  }

  void dropDownOnChange(dynamic value) {
    selectedValue = value;
    sortOnchange();
    _state.pendingWithAuditorController.update();
  }

  void sortOnchange() {
    if (isAscending) {
      if (selectedValue == AppStringConstants.dateOfRequest) {
        pendingWithAuditorDetailsList
            .sort((a, b) => (a.dateOfRequest ?? DateTime.now()).compareTo(b.dateOfRequest ?? DateTime.now()));
      } else {
        pendingWithAuditorDetailsList.sort((a, b) => a.toJson()[selectedValue].compareTo(b.toJson()[selectedValue]));
      }
    } else {
      if (selectedValue == AppStringConstants.dateOfRequest) {
        pendingWithAuditorDetailsList
            .sort((a, b) => (b.dateOfRequest ?? DateTime.now()).compareTo(a.dateOfRequest ?? DateTime.now()));
      } else {
        pendingWithAuditorDetailsList.sort((a, b) => b.toJson()[selectedValue].compareTo(a.toJson()[selectedValue]));
      }
    }
    _state.pendingWithAuditorController.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortOnchange();
    _state.pendingWithAuditorController.update();
  }
}
