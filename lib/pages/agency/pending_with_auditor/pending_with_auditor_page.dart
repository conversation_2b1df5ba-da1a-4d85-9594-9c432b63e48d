import 'package:ams/app_imports.dart';

class PendingWithAuditorPage extends StatefulWidget {
  const PendingWithAuditorPage({super.key});

  @override
  State<PendingWithAuditorPage> createState() => PendingWithAuditorPageState();
}

class PendingWithAuditorPageState extends State<PendingWithAuditorPage> {
  PendingWithAuditorPageHelper? _pendingWithAuditorPageHelper;
  late PendingWithAuditorController pendingWithAuditorController;

  @override
  Widget build(BuildContext context) {
    _pendingWithAuditorPageHelper = _pendingWithAuditorPageHelper ?? (PendingWithAuditorPageHelper(this));
    return GetBuilder(
      init: PendingWithAuditorController(),
      builder: (PendingWithAuditorController controller) {
        pendingWithAuditorController = controller;
        return Stack(
          children: [
            AppScaffold(
              apiStatus: _pendingWithAuditorPageHelper!.apiStatus,
              appBar: _appBarView(),
              body: _bodyView(),
            ),
          ],
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.pendingWithAuditor.tr);
  }

  Widget _bodyView() {
    return tableCard(
      isShowDropdown: true,
      isMainListEmpty: _pendingWithAuditorPageHelper!.isEmpty,
      dropdownValue: _pendingWithAuditorPageHelper!.selectedValue,
      dropdownList: _pendingWithAuditorPageHelper!.dropdownList,
      isShowSearch: true,
      isAscending: _pendingWithAuditorPageHelper!.isAscending,
      searchController: _pendingWithAuditorPageHelper!.searchController,
      sortOnTap: _pendingWithAuditorPageHelper!.sortOnTap,
      searchOnChanged: _pendingWithAuditorPageHelper!.searchOnChange,
      dropDownOnChanged: _pendingWithAuditorPageHelper!.dropDownOnChange,
      clearSearchOnTap: _pendingWithAuditorPageHelper!.clearValue,
      children: [
        for (var data in _pendingWithAuditorPageHelper!.searchList) _tableView(data.toJson()),
      ],
    );
  }

  Widget _tableView(Map<String, dynamic> item) {
    return Padding(
      padding: const EdgeInsets.all(12.0).copyWith(left: 0, right: 0),
      child: Table(
        border: TableBorder.all(
          color: AppColorConstants.colorMediumGrey,
          borderRadius: BorderRadius.circular(5),
        ),
        children: [
          for (var entry in item.entries)
            TableRow(
              children: [
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    alignment: Alignment.centerLeft,
                    color: AppColorConstants.colorLightPurple,
                    child: AppText(
                      entry.key.toString().tr,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
                entry.key == AppStringConstants.report
                    ? TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Padding(
                          padding: const EdgeInsets.all(12).copyWith(left: 30, right: 30),
                          child: AppButton(
                            onTap: () => entry.value == AppStringConstants.view?gotoInspectionReportPage():cancelOnTapShowDialog(),
                            borderRadius: BorderRadius.circular(5),
                            backColor: entry.value == AppStringConstants.view
                                ? AppColorConstants.colorAppPrimary
                                : AppColorConstants.colorRed,
                            title: entry.value.toString().tr,
                            textSize: 14,
                            height: 35,
                          ),
                        ),
                      )
                    : (entry.key == AppStringConstants.auditorName || entry.key == AppStringConstants.address)
                        ? TableCell(
                            verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                            child: Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  AppText(
                                    entry.value.toString(),
                                    fontSize: 12,
                                    color: AppColorConstants.colorBlack,
                                  ),
                                  Row(
                                    children: [
                                      AppImageAsset(image: AppAssetsConstants.icPhone),
                                      Expanded(
                                        child: AppText(
                                          item[AppStringConstants.mobile] ?? '',
                                          fontSize: 12,
                                          color: AppColorConstants.colorBlack,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          )
                        : entry.key == AppStringConstants.dateOfRequest
                            ? TableCell(
                                verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: AppText(
                                    DateHelper.getDDMMYYYYFormatDate(entry.value),
                                    fontSize: 12,
                                    color: AppColorConstants.colorBlack,
                                  ),
                                ),
                              )
                            : TableCell(
                                verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: AppText(
                                    entry.value.toString(),
                                    fontSize: 12,
                                    color: AppColorConstants.colorBlack,
                                  ),
                                ),
                              ),
              ],
            ),
        ],
      ),
    );
  }

  Future cancelOnTapShowDialog() {
    return appDialog(
      context: context,
      title: AppStringConstants.infoTrainFssaiGovSays.tr,
      buttonText: AppStringConstants.ok,
      buttonTap: () {},
      subTitle: AppStringConstants.auditRequestCancellationMessage.tr,
    );
  }
}
