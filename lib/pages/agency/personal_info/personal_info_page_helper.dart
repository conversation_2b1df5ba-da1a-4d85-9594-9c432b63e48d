import 'package:ams/app_imports.dart';

class AgencyPersonalInfoPageHelper {
  final AgencyPersonalInfoPageState _pageState;
  ApiStatus apiStatus = ApiStatus.initial;
  AgencyPersonalInfoModel? agencyPersonalInfoModel;
  String kobString = '';
  AgencyPersonalInfoPageHelper(this._pageState) {
    Future.delayed((const Duration(milliseconds: 200)), () => getPersonalInfo());
  }

  Future<void> getPersonalInfo() async {
    apiStatus = ApiStatus.loading;
    _pageState.personalInfoController.update();
    _pageState.personalInfoController.getPersonalInfo(165).then((value) {
      agencyPersonalInfoModel = value;
      int srNo = 1;
      for (var element in value.kob) {
        kobString = kobString + (' ${srNo++}:- ${element.kobName}');
      }
      apiStatus = ApiStatus.success;
      _pageState.personalInfoController.update();
    });
  }

  void documentOnTap(String onTapText) {
    if (onTapText == AppStringConstants.certificateOfAccereditation) {
      launchDocumentUrl(agencyPersonalInfoModel?.docDetails[0].filename ?? '');
    } else if (onTapText == AppStringConstants.confidentialityAgreement) {
      launchDocumentUrl(agencyPersonalInfoModel?.docDetails[1].filename ?? '');
    } else if (onTapText == AppStringConstants.copyOfLastAssessmentReport) {
      launchDocumentUrl(agencyPersonalInfoModel?.docDetails[2].filename ?? '');
    } else if (onTapText == AppStringConstants.leadAuditorCourseCertificate) {
      launchDocumentUrl(agencyPersonalInfoModel?.docDetails[0].filename ?? '');
    } else if (onTapText == AppStringConstants.legalEntryStatus) {
      launchDocumentUrl(agencyPersonalInfoModel?.docDetails[0].filename ?? '');
    }
  }
}
