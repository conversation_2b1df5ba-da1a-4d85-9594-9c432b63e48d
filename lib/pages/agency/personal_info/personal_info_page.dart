import 'package:ams/app_imports.dart';

class AgencyPersonalInfoPage extends StatefulWidget {
  const AgencyPersonalInfoPage({super.key});

  @override
  State<AgencyPersonalInfoPage> createState() => AgencyPersonalInfoPageState();
}

class AgencyPersonalInfoPageState extends State<AgencyPersonalInfoPage> {
  AgencyPersonalInfoPageHelper? _pageHelper;
  late PersonalInfoController personalInfoController;

  @override
  Widget build(BuildContext context) {
    _pageHelper = _pageHelper ?? (AgencyPersonalInfoPageHelper(this));
    return GetBuilder(
      init: PersonalInfoController(),
      builder: (PersonalInfoController controller) {
        personalInfoController = controller;
        return AppScaffold(
          apiStatus: _pageHelper!.apiStatus,
          appBar: CommonAppBar(title: AppStringConstants.agencyDetails.tr),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 11, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColorConstants.colorAppPrimary),
      ),
      child: ListView(
        padding: const EdgeInsets.all(8),
        children: [
          _personalInfoView(),
          const SizedBox(height: 12),
          _auditAgencyInfoView(),
          const SizedBox(height: 12),
          _docAttachedInfoView(),
        ],
      ),
    );
  }

  Widget _personalInfoView() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorSkyBlue),
            borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 22, vertical: 10),
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 30),
              _commonRichText('${AppStringConstants.name.tr} : ', _pageHelper!.agencyPersonalInfoModel?.contactPersonName ?? ''),
              _commonRichText('${AppStringConstants.email.tr} : ', _pageHelper!.agencyPersonalInfoModel?.email ?? ''),
              _commonRichText('${AppStringConstants.mobile.tr} : ', _pageHelper!.agencyPersonalInfoModel?.mobile ?? ''),
              _commonRichText('${AppStringConstants.address.tr} : ', _pageHelper!.agencyPersonalInfoModel?.address ?? ''),
            ],
          ),
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            AppImageAsset(image: AppAssetsConstants.imgPersonalInfo),
            AppText(
              AppStringConstants.personalInfo.tr,
              fontSize: 18,
              color: AppColorConstants.colorWhite,
            ),
          ],
        ),
      ],
    );
  }

  Widget _auditAgencyInfoView() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorLightGreen),
            borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 22, vertical: 10),
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 30),
              _commonRichText('${AppStringConstants.agencyName.tr} : ', _pageHelper!.agencyPersonalInfoModel?.agencyName ?? ''),
              _commonRichText(
                '${AppStringConstants.certificateNumber.tr} : ',
                _pageHelper!.agencyPersonalInfoModel?.certificateNumber ?? '',
              ),
              _commonRichText(
                '${AppStringConstants.validUpto.tr} : ',
                DateHelper.getDDMMMMYYYYDateFormatDate(_pageHelper!.agencyPersonalInfoModel?.validUpto ?? DateTime.now()),
              ),
              _commonRichText(
                '${AppStringConstants.numberOfAuditor.tr} : ',
                _pageHelper!.agencyPersonalInfoModel?.noOfAuditors.toString() ?? '',
              ),
              _commonRichText(
                '${AppStringConstants.legalEnterStatus.tr} : ',
                _pageHelper!.agencyPersonalInfoModel?.legalEntryStatus ?? '',
              ),
              _commonRichText(
                '${AppStringConstants.recognitionNumber.tr} : ',
                _pageHelper!.agencyPersonalInfoModel?.recognitionNumber ?? '',
              ),
            ],
          ),
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            AppImageAsset(image: AppAssetsConstants.imgAgencyInfo),
            AppText(
              AppStringConstants.agencyInfo.tr,
              fontSize: 18,
              color: AppColorConstants.colorWhite,
            ),
          ],
        ),
      ],
    );
  }

  Widget _docAttachedInfoView() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppText(
            AppStringConstants.documentAttached.tr,
            fontWeight: FontWeight.w500,
          ),
          const SizedBox(
            height: 8,
          ),
          const SizedBox(
            height: 15,
          ),
          _normalDocumentAttached(
            AppStringConstants.kindOfBusiness,
            _pageHelper!.kobString,
          ),
          const SizedBox(
            height: 15,
          ),
          _normalDocumentAttached(AppStringConstants.scopeOfAuditing, _pageHelper!.agencyPersonalInfoModel?.auditingScope ?? ''),
          const SizedBox(
            height: 15,
          ),
          _normalDocumentAttached(
            AppStringConstants.geographicalAreas,
            _pageHelper!.agencyPersonalInfoModel?.geographicalArea ?? '',
          ),
        ],
      ),
    );
  }

  Widget _commonRichText(String firstText, String secondText) {
    return Text.rich(
      TextSpan(
        text: firstText,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
        children: [
          TextSpan(
            text: secondText,
            style: const TextStyle(fontWeight: FontWeight.w400),
          ),
        ],
      ),
    );
  }

  Widget _commonDocumentAttached(String text, VoidCallback onTap) {
    return Row(
      children: [
        Expanded(
          child: AppText(
            text.tr,
            color: AppColorConstants.colorRedAccent,
            fontSize: 12,
          ),
        ),
        const SizedBox(
          width: 10,
        ),
        InkWell(onTap: onTap, child: AppImageAsset(image: AppAssetsConstants.icDownloadArrow)),
      ],
    );
  }

  Widget _normalDocumentAttached(String text, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          text.tr,
          fontWeight: FontWeight.w500,
        ),
        AppText(
          value,
          color: AppColorConstants.colorRedAccent,
          fontSize: 12,
        ),
      ],
    );
  }
}
