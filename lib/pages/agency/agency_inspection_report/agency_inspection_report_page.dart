import 'package:ams/app_imports.dart';

class AgencyInspectionReportPage extends StatefulWidget {
  const AgencyInspectionReportPage({super.key});

  @override
  State<AgencyInspectionReportPage> createState() => AgencyInspectionReportPageState();
}

class AgencyInspectionReportPageState extends State<AgencyInspectionReportPage> {
  AgencyInspectionReportPageHelper? _agencyInspectionReportPageHelper;
  late AgencyInspectionReportController agencyInspectionReportController;
  @override
  Widget build(BuildContext context) {
    _agencyInspectionReportPageHelper = _agencyInspectionReportPageHelper ?? (AgencyInspectionReportPageHelper(this));
    return GetBuilder(
      init: AgencyInspectionReportController(),
      builder: (AgencyInspectionReportController controller) {
        agencyInspectionReportController = controller;
        return AppScaffold(
          appBar: _appBarView(),
          body: _bodyView(),
          apiStatus: _agencyInspectionReportPageHelper!.apiStatus,
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.viewDetail.tr);
  }

  Widget _bodyView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      child: ListView(
        shrinkWrap: true,
        children: [
          const SizedBox(
            height: 12,
          ),
          _thirdPartyAuditView(),
          const SizedBox(
            height: 12,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Text.rich(
              TextSpan(
                text: '${AppStringConstants.note.tr} : ',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColorConstants.colorRedAccent,
                  fontSize: 13,
                ),
                children: [
                  TextSpan(
                    text: AppStringConstants.requiredQuestionNote.tr,
                    style: const TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
            ),
          ),
          _inspectionReportView(),
          const SizedBox(
            height: 12,
          ),
          _pointsAndRatingView(),
          const SizedBox(
            height: 12,
          ),
          _bottomView(),
        ],
      ),
    );
  }

  Widget _thirdPartyAuditView() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppColorConstants.colorVividOrange),
      ),
      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColorConstants.colorVividOrange,
              border: Border.all(color: AppColorConstants.colorVividOrange),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(3),
                topLeft: Radius.circular(3),
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 13),
            child: AppText(
              AppStringConstants.fssaiThirdPartyAudits.tr,
              color: AppColorConstants.colorWhite,
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                  alignment: Alignment.centerLeft,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(5), topRight: Radius.circular(5)),
                    border: Border(
                      left: BorderSide(color: AppColorConstants.colorLightGrey300),
                      right: BorderSide(color: AppColorConstants.colorLightGrey300),
                      top: BorderSide(color: AppColorConstants.colorLightGrey300),
                    ),
                    color: AppColorConstants.colorPeach,
                  ),
                  child: const AppText('Test Agency Name', fontWeight: FontWeight.w600, fontSize: 16),
                ),
                Table(
                  border: TableBorder.all(
                    color: AppColorConstants.colorLightGrey300,
                    borderRadius: const BorderRadius.only(bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5)),
                  ),
                  children: [
                    for (var entry in _agencyInspectionReportPageHelper!.thirdPartyAuditModel.toJson().entries)
                      TableRow(
                        children: [
                          TableCell(
                            verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                              alignment: Alignment.centerLeft,
                              color: AppColorConstants.colorPeach,
                              child: AppText(
                                entry.key.tr,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                          TableCell(
                            verticalAlignment: TableCellVerticalAlignment.middle,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                              child: (entry.key == AppStringConstants.filesUploaded)
                                  ? _uploadedFileView()
                                  : AppText(
                                      (entry.key == AppStringConstants.auditStartDate ||
                                              entry.key == AppStringConstants.auditEndDate)
                                          ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                          : entry.value,
                                      fontSize: 12,
                                      color: AppColorConstants.colorBlack,
                                    ),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _uploadedFileView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppButton(
          onTap: (_agencyInspectionReportPageHelper!.thirdPartyAuditModel.auditorAgreementFile?.isNotEmpty ?? false)
              ? () => launchDocumentUrl(_agencyInspectionReportPageHelper!.thirdPartyAuditModel.auditorAgreementFile!)
              : () {},
          title: AppStringConstants.auditorAgreementFile.tr,
          imageIcon: (_agencyInspectionReportPageHelper!.thirdPartyAuditModel.auditorAgreementFile?.isNotEmpty ?? false)
              ? AppAssetsConstants.icDownload
              : AppAssetsConstants.icCancel,
          iconSize: 10,
          iconColor: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.circular(5),
          backColor: AppColorConstants.colorYellow,
          textSize: 10,
          height: 25,
          border: Border.all(color: AppColorConstants.colorYellow),
        ),
        const SizedBox(height: 5),
        AppButton(
          onTap: (_agencyInspectionReportPageHelper!.thirdPartyAuditModel.confidentialityFile?.isNotEmpty ?? false)
              ? () => launchDocumentUrl(_agencyInspectionReportPageHelper!.thirdPartyAuditModel.confidentialityFile!)
              : () {},
          title: AppStringConstants.confidentialityFile.tr,
          imageIcon: (_agencyInspectionReportPageHelper!.thirdPartyAuditModel.confidentialityFile?.isNotEmpty ?? false)
              ? AppAssetsConstants.icDownload
              : AppAssetsConstants.icCancel,
          iconColor: AppColorConstants.colorWhite,
          iconSize: 10,
          borderRadius: BorderRadius.circular(5),
          backColor: AppColorConstants.colorYellow,
          textSize: 10,
          height: 25,
          border: Border.all(color: AppColorConstants.colorYellow),
        ),
        const SizedBox(height: 5),
        AppButton(
          onTap: (_agencyInspectionReportPageHelper!.thirdPartyAuditModel.otherFile?.isNotEmpty ?? false)
              ? () => launchDocumentUrl(_agencyInspectionReportPageHelper!.thirdPartyAuditModel.otherFile!)
              : () {},
          title: AppStringConstants.other.tr,
          imageIcon: (_agencyInspectionReportPageHelper!.thirdPartyAuditModel.otherFile?.isNotEmpty ?? false)
              ? AppAssetsConstants.icDownload
              : AppAssetsConstants.icCancel,
          iconSize: 10,
          iconColor: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.circular(5),
          backColor: AppColorConstants.colorYellow,
          textSize: 10,
          height: 25,
          width: 80,
          border: Border.all(color: AppColorConstants.colorYellow),
        ),
      ],
    );
  }

  Widget _inspectionReportView() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppColorConstants.colorVividOrange),
      ),
      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColorConstants.colorVividOrange,
              border: Border.all(color: AppColorConstants.colorVividOrange),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(3),
                topLeft: Radius.circular(3),
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 13),
            child: const AppText(
              '${AppStringConstants.inspectionChecklistFor} GENERAL MANUFACTURING',
              color: AppColorConstants.colorWhite,
              fontSize: 18,
              textAlign: TextAlign.center,
              fontWeight: FontWeight.w700,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                for (var element in _agencyInspectionReportPageHelper!.inspectionReportList)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    child: Table(
                      border: TableBorder.all(
                        color: AppColorConstants.colorLightGrey300,
                      ),
                      children: [
                        for (var entry in element.toJson().entries)
                          TableRow(
                            children: [
                              TableCell(
                                verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                  alignment: Alignment.centerLeft,
                                  color: AppColorConstants.colorPeach,
                                  child: AppText(
                                    entry.key.tr,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              TableCell(
                                verticalAlignment: TableCellVerticalAlignment.middle,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                  child: (entry.key == AppStringConstants.fileUploaded)
                                      ? (entry.value != null && entry.value.toString().isNotEmpty)
                                          ? Align(
                                              alignment: Alignment.centerLeft,
                                              child: InkWell(
                                                onTap: () => launchDocumentUrl(entry.value.toString()),
                                                child: Padding(
                                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                                  child: AppImageAsset(
                                                    image: AppAssetsConstants.icDownload,
                                                    height: 20,
                                                    width: 20,
                                                  ),
                                                ),
                                              ),
                                            )
                                          : const SizedBox()
                                      : AppText(
                                          '${(entry.key == AppStringConstants.comments && entry.value.toString().isEmpty) ? '-' : (entry.key == AppStringConstants.srNo && element.isMandatory) ? '${entry.value}*' : entry.value}',
                                          fontSize: 12,
                                          color: (entry.key == AppStringConstants.question && element.isMandatory)
                                              ? AppColorConstants.colorRedAccent
                                              : AppColorConstants.colorBlack,
                                        ),
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _pointsAndRatingView() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppColorConstants.colorVividOrange),
      ),
      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColorConstants.colorVividOrange,
              border: Border.all(color: AppColorConstants.colorVividOrange),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(3),
                topLeft: Radius.circular(3),
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 13),
            child:  AppText(
              AppStringConstants.pointsAndCompliance.tr,
              color: AppColorConstants.colorWhite,
              fontSize: 18,
              textAlign: TextAlign.center,
              fontWeight: FontWeight.w700,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Table(
              border: TableBorder.all(
                color: AppColorConstants.colorLightGrey300,
              ),
              children: [
                pointCommonRow(AppStringConstants.totalPoints.tr, '1'),
                pointCommonRow(AppStringConstants.pointsScored.tr, '1'),
                pointCommonRow(AppStringConstants.rating.tr, '1'),
                pointCommonRow(AppStringConstants.compliance.tr, 'Needs Improvement'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  TableRow pointCommonRow(String key, String value) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorPeach,
            child: AppText(
              key.tr,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: AppText(
              value.toString(),
              fontSize: 12,
              fontWeight: FontWeight.w700,
              color: AppColorConstants.colorBlack,
            ),
          ),
        ),
      ],
    );
  }

  Widget _bottomView() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _commonRadioButton(AppStringConstants.approved, _agencyInspectionReportPageHelper!.selectedButton)),
            Expanded(child: _commonRadioButton(AppStringConstants.sendBack, _agencyInspectionReportPageHelper!.selectedButton)),
            Expanded(child: _commonRadioButton(AppStringConstants.reject, _agencyInspectionReportPageHelper!.selectedButton)),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        Column(
          children: [
            AppButton(
              onTap: () => launchDocumentUrl(''),
              title: AppStringConstants.previewReport.tr,
              textSize: 15,
            ),
            const SizedBox(
              height: 5,
            ),
            AppButton(
              onTap: () => _agencyInspectionReportPageHelper!.processButtonOnTap(),
              title: _agencyInspectionReportPageHelper!.selectedButton.tr,
              textSize: 15,
              backColor: _agencyInspectionReportPageHelper!.selectedButton==AppStringConstants.approved?AppColorConstants.colorLightGreen:_agencyInspectionReportPageHelper!.selectedButton==AppStringConstants.reject?AppColorConstants.colorRed:AppColorConstants.colorSkyBlue,
            ),
            const SizedBox(
              height: 5,
            ),
            AppButton(
              backColor: AppColorConstants.colorVividOrange,
              onTap: () => gotoBack(),
              title: AppStringConstants.sendBack.tr,
              textSize: 15,
            ),
            const SizedBox(height: 20,),
          ],
        ),
      ],
    );
  }

  Widget _commonRadioButton(String text, String selectedValue) {
    return Row(
      children: [
        Radio(
          value: text,
          groupValue: selectedValue,
          onChanged: (value) => _agencyInspectionReportPageHelper!.radioButtonOnTap(value),
        ),
        Expanded(
          child: AppText(
            text.tr,
            fontSize: 12,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
