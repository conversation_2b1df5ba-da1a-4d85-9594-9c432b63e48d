import 'package:ams/app_imports.dart';

class AgencyInspectionReportPageHelper {
  final AgencyInspectionReportPageState _state;
  ApiStatus apiStatus=ApiStatus.initial;
  String selectedButton = AppStringConstants.approved;
  ThirdPartyAuditModel thirdPartyAuditModel = ThirdPartyAuditModel(
    companyName:
        'BIOGLAZE BIOTECH PRIVATE LIMITED\nKind of Business: Relabeller - Food or Health Supplements and Nutraceuticals etc.',
    reportNo: '75/48',
    auditStartDate: DateTime(2024, 7, 1),
    auditEndDate: DateTime(2024, 7, 7),
    auditorName: 'Test auditor',
    auditorContactNumber: '**********',
    address: '2255/4-7, 2ND FLOOR, MARI WALA TOWN, MANI MAJRAChandigarhChandigarh Chandigarh Chandigarh',
    email: '<EMAIL>',
    mobileNumber: '9317******',
    fboFssaiLicenseNo: '13023999000032',
    majorNonConformity: '-',
    minorNonConformity: '-',
    comments: '-',
    fboRepresentativeName: 'AJAY BHARDWAJ',
    filesUploaded: '',
    auditorAgreementFile: 'https://infotrain.fssai.gov.in/AMS/uploadauditdata/49milkauditoragreement.pdf',
    confidentialityFile: 'https://infotrain.fssai.gov.in/AMS/uploadauditdata/49milkauditoragreement.pdf',
  );

  List<InspectionItem> inspectionReportList = [
    InspectionItem(
      srNo: 1,
      question: 'Food establishment has an updated FSSAI license and is displayed at a prominent location',
      maxPoints: '2',
      pointScored: '1',
      fileUrl: 'https://infotrain.fssai.gov.in/AMS/uploadauditdata/42milkQ1.pdf',
      comments: '',
    ),
    InspectionItem(
      srNo: 2,
      question:
          'The design of food premises provides adequate working space; permit maintenance & cleaning to prevent the entry of dirt, dust & pests.',
      maxPoints: '2',
      pointScored: 'NA',
      fileUrl: '',
      comments: 'Test comment',
    ),
    InspectionItem(
      srNo: 3,
      question: 'The internal structure & fittings are made of non-toxic and impermeable material.',
      maxPoints: '2',
      pointScored: 'NA',
      fileUrl: '',
      comments: '',
    ),
    InspectionItem(
      srNo: 4,
      question: 'Walls, ceilings & doors are free from flaking paint or plaster, condensation & shedding particles.',
      maxPoints: '2',
      pointScored: 'NA',
      fileUrl: '',
      comments: 't',
    ),
    InspectionItem(
      srNo: 5,
      question:
          'Potable water (meeting standards of IS:10500) is used as product ingredient or in contact with food or food contact surface. Tested for quality semi-annually. Check for records.',
      maxPoints: '4',
      pointScored: '4',
      fileUrl: 'https://infotrain.fssai.gov.in/AMS/uploadauditdata/42milkQ13.pdf',
      comments: '',
      isMandatory: true,
    ),
    InspectionItem(
      srNo: 6,
      question:
          'Raw Milk Reception Dock (RMRD) facility is sufficiently raised with sides & top to prevent contamination while unloading of raw material',
      maxPoints: '2',
      pointScored: '2',
      fileUrl: '',
      comments: '',
    ),
    InspectionItem(
      srNo: 7,
      question: 'Separate processing facilities available for heat treated milk & milk products to avoid cross contamination',
      maxPoints: '2',
      pointScored: 'NA',
      fileUrl: '',
      comments: '',
      isMandatory: true,
    ),
  ];
  AgencyInspectionReportPageHelper(this._state);

  void radioButtonOnTap(dynamic value) {
    selectedButton = value;
    _state.agencyInspectionReportController.update();
  }

  void processButtonOnTap() {
    if (selectedButton == AppStringConstants.reject) {
    } else if (selectedButton == AppStringConstants.sendBack) {
    } else {
    }
  }
}
