import 'package:ams/app_imports.dart';

class ApplicationReceivedPage extends StatefulWidget {
  const ApplicationReceivedPage({super.key});

  @override
  State<ApplicationReceivedPage> createState() => ApplicationReceivedPageState();
}

class ApplicationReceivedPageState extends State<ApplicationReceivedPage> {
  ApplicationApprovedPageHelper? _applicationApprovedPageHelper;
  late AgencyApplicationReceivedController agencyApprovedController;
  @override
  Widget build(BuildContext context) {
    _applicationApprovedPageHelper = _applicationApprovedPageHelper ?? (ApplicationApprovedPageHelper(this));
    return GetBuilder(
      init: AgencyApplicationReceivedController(),
      builder: (AgencyApplicationReceivedController controller) {
        agencyApprovedController = controller;
        return AppScaffold(
          apiStatus: _applicationApprovedPageHelper!.apiStatus,
          appBar: _appBarView(),
          body: _bodyView(),
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.applicationReceived.tr);
  }

  Widget _bodyView() {
    return tableCard(
      isShowDropdown: true,
      dropdownValue: _applicationApprovedPageHelper!.selectedValue,
      dropdownList: _applicationApprovedPageHelper!.dropdownList,
      isShowSearch: true,
      isMainListEmpty: _applicationApprovedPageHelper!.isEmpty,
      isAscending: _applicationApprovedPageHelper!.isAscending,
      searchController: _applicationApprovedPageHelper!.searchController,
      sortOnTap: _applicationApprovedPageHelper!.sortOnTap,
      searchOnChanged: _applicationApprovedPageHelper!.searchOnChange,
      dropDownOnChanged: _applicationApprovedPageHelper!.dropDownOnChange,
      clearSearchOnTap: _applicationApprovedPageHelper!.clearValue,
      children: [
        for (var data in _applicationApprovedPageHelper!.searchList) _tableView(data.toJson()),
      ],
    );
  }

  Widget _tableView(Map<String, dynamic> item) {
    return Padding(
      padding: const EdgeInsets.all(12.0).copyWith(left: 0, right: 0),
      child: Table(
        border: TableBorder.all(
          color: AppColorConstants.colorMediumGrey,
          borderRadius: BorderRadius.circular(5),
        ),
        children: [
          for (var entry in item.entries)
            TableRow(
              children: [
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    alignment: Alignment.centerLeft,
                    color: AppColorConstants.colorLightPurple,
                    child: AppText(
                      entry.key.toString().tr,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
                entry.key == AppStringConstants.report
                    ? TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0).copyWith(left: 35, right: 35),
                          child: AppButton(
                            backColor: AppColorConstants.colorAppPrimary,
                            height: 35,
                            borderRadius: BorderRadius.circular(5),
                            textSize: 14,
                            title: AppStringConstants.view,
                            onTap: () => gotoAgencyInspectionReportPage(),
                          ),
                        ),
                      )
                    : entry.key == AppStringConstants.date
                        ? TableCell(
                            verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: AppText(
                                DateHelper.getDDMMYYYYFormatDate(entry.value),
                                fontSize: 12,
                                color: AppColorConstants.colorBlack,
                              ),
                            ),
                          )
                        : (entry.key == AppStringConstants.auditorName || entry.key == AppStringConstants.address)
                            ? TableCell(
                                verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      AppText(
                                        entry.value.toString(),
                                        fontSize: 12,
                                        color: AppColorConstants.colorBlack,
                                      ),
                                      Row(
                                        children: [
                                          AppImageAsset(image: AppAssetsConstants.icPhone),
                                          Expanded(
                                            child: AppText(
                                              item[AppStringConstants.mobile] ?? '',
                                              fontSize: 12,
                                              color: AppColorConstants.colorBlack,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            : TableCell(
                                verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: AppText(
                                    entry.value.toString(),
                                    fontSize: 12,
                                    color: AppColorConstants.colorBlack,
                                  ),
                                ),
                              ),
              ],
            ),
        ],
      ),
    );
  }
}
