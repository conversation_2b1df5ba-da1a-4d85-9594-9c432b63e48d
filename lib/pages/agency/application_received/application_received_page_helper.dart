import 'package:ams/app_imports.dart';

class ApplicationApprovedPageHelper {
  final ApplicationReceivedPageState _state;
  List<ApplicationReceivedDetails> applicationReceivedList = [];
  List<ApplicationReceivedDetails> searchList = [];
  String? selectedValue;
  TextEditingController searchController = TextEditingController();
  bool isAscending = true;
  bool isEmpty=false;
  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.reportNo,
    AppStringConstants.auditorsName,
    AppStringConstants.fboName,
    AppStringConstants.licenseNo,
    AppStringConstants.contactPerson,
    AppStringConstants.address,
    AppStringConstants.kob,
    AppStringConstants.date,
  ];

  ApiStatus apiStatus = ApiStatus.initial;

  List<AgencyApplicationReceivedModel> data = [];

  ApplicationApprovedPageHelper(this._state) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () => getApplicationData(),
    );
  }

  Future<void> getApplicationData() async {
    apiStatus = ApiStatus.loading;
    _state.agencyApprovedController.update();
    setDefaultValue();
    await _state.agencyApprovedController.getAgencyApplicationReceivedData().then((value) {
      applicationReceivedList = [];
      int srNo = 1;
      for (var model in value) {
        applicationReceivedList.add(
          ApplicationReceivedDetails(
            srNo: srNo++,
            reportNo: model.reportno,
            auditorName: model.auditorname,
            licenseNo: model.licenseno,
            contactPerson: model.contactperson,
            fboName: model.fboname,
            address: model.address,
            kob: model.kob,
            date: DateFormat('dd/MM/yyyy').parse(model.dateofrequest),
          ),
        );
      }
      if(applicationReceivedList.isEmpty){
        isEmpty=true;
      }
      apiStatus = ApiStatus.success;
      searchList = applicationReceivedList;
      _state.agencyApprovedController.update();
    });
  }

  void setDefaultValue() {
    selectedValue = AppStringConstants.srNo;
    searchController.clear();
    isAscending = true;
  }

  void searchOnChange(String text) {
    searchList = applicationReceivedList.where((element) {
      return ((element.srNo.toString().toLowerCase().contains(text)) ||
          (element.contactPerson.toString().toLowerCase().contains(text)) ||
          (element.reportNo.toString().toLowerCase().contains(text)) ||
          (element.fboName.toString().toLowerCase().contains(text)) ||
          (element.licenseNo.toString().toLowerCase().contains(text)) ||
          (element.auditorName.toString().toLowerCase().contains(text)) ||
          (element.address.toString().toLowerCase().contains(text)) ||
          (element.date.toString().toLowerCase().contains(text)) ||
          (element.kob.toString().toLowerCase().contains(text)));
    }).toList();
    _state.agencyApprovedController.update();
  }

  void clearValue() {
    FocusScope.of(_state.context).unfocus();
    searchController.clear();
    searchList = applicationReceivedList;
    _state.agencyApprovedController.update();
  }

  void dropDownOnChange(dynamic value) {
    selectedValue = value;
    sortOnchange();
    _state.agencyApprovedController.update();
  }

  void sortOnchange() {
    if (isAscending) {
      if (selectedValue == AppStringConstants.date) {
        searchList.sort(
          (a, b) => (a.date ?? DateTime.now()).compareTo(b.date ?? DateTime.now()),
        );
      } else {
        searchList.sort((a, b) => a.toJson()[selectedValue].compareTo(b.toJson()[selectedValue]));
      }
    } else {
      if (selectedValue == AppStringConstants.date) {
        searchList.sort(
          (a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()),
        );
      } else {
        searchList.sort((a, b) => b.toJson()[selectedValue].compareTo(a.toJson()[selectedValue]));
      }
    }

    _state.agencyApprovedController.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortOnchange();
    _state.agencyApprovedController.update();
  }
}
