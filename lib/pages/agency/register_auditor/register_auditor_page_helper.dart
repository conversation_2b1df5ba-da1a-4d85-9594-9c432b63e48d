import 'package:ams/app_imports.dart';

class RegisterAuditorPageHelper {
  final RegisterAuditorPageState _state;
  TextEditingController auditorNameController = TextEditingController();
  TextEditingController fatherNameController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  TextEditingController pinCodeController = TextEditingController();
  TextEditingController mobileNumberController = TextEditingController();
  TextEditingController emailAddressController = TextEditingController();
  TextEditingController degreeController = TextEditingController();
  TextEditingController subjectController = TextEditingController();
  TextEditingController collegeUniController = TextEditingController();
  TextEditingController gradeAndController = TextEditingController();
  TextEditingController organizationController = TextEditingController();
  TextEditingController designationController = TextEditingController();
  ApiStatus apiStatus=ApiStatus.initial;
  StateModel? selectedStateModel;
  DistrictModel? selectedDistrictModel;
  File? leadAuditorCertificateFile;
  File? educationQualificationFile;
  File? auditLogFile;
  File? sectorSpecificKnowledgeFile;
  String auditorErrorText = '';
  String fatherErrorText = '';
  String addressErrorText = '';
  String stateErrorText = '';
  String districtErrorText = '';
  String pinCodeErrorText = '';
  String mobileErrorText = '';
  String degreeErrorText = '';
  String subjectErrorText = '';
  String collegeErrorText = '';
  String passingYearErrorText = '';
  String percentageErrorText = '';
  String organizationErrorText = '';
  String designationErrorText = '';
  String fromErrorText = '';
  String experienceInMonthErrorText = '';
  String toErrorText = '';
  String leadAuditorCertificateErrorText = '';
  String educationQualificationErrorText = '';
  String auditLogErrorText = '';
  String sectorSpecificKnowledgeErrorText = '';
  String emailErrorText = '';

  List<ExperienceModelController> experienceList = [
    ExperienceModelController(
      orgNameController: TextEditingController(),
      designationController: TextEditingController(),
      toController: TextEditingController(),
      experienceMonthController: TextEditingController(),
      fromController: TextEditingController(),
    ),
  ];
  List<QualificationModel> qualificationList = [
    QualificationModel(
      degreeController: TextEditingController(),
      subjectController: TextEditingController(),
      collegeController: TextEditingController(),
      passingYearController: TextEditingController(),
      percentageController: TextEditingController(),
    ),
  ];
  List<StateModel> stateList = [];
  List<DistrictModel> districtList = [];
  RegisterAuditorPageHelper(this._state){
    Future.delayed((const Duration(milliseconds: 200)),()=>getStateList());
  }

  void stateOnchange(dynamic value) {
    selectedStateModel=value;
    selectedDistrictModel=null;
    getDistrictList();
    _state.registerAuditorController.update();
  }
  Future<void> getStateList()async {
    apiStatus=ApiStatus.loading;
    _state.registerAuditorController.update();
    _state.registerAuditorController.getStateList().then((value){
      stateList=value;
      apiStatus=ApiStatus.success;
      _state.registerAuditorController.update();

    });
  }
  Future<void> getDistrictList()async{
    apiStatus=ApiStatus.loading;
    _state.registerAuditorController.update();
    _state.registerAuditorController.getDistrictList(selectedStateModel?.stateId??0).then((value){
      districtList=value;
      apiStatus=ApiStatus.success;
      _state.registerAuditorController.update();
    });
  }
  void districtOnchange(dynamic value) {
    selectedDistrictModel=value;
    _state.registerAuditorController.update();
  }

  void addMoreExperienceDetails() {
    experienceList.add(
      ExperienceModelController(
        orgNameController: TextEditingController(),
        designationController: TextEditingController(),
        fromController: TextEditingController(),
        toController: TextEditingController(),
        experienceMonthController: TextEditingController(),
      ),
    );

    _state.registerAuditorController.update();
  }

  void addMoreQualificationDetails() {
    qualificationList.add(
      QualificationModel(
        degreeController: TextEditingController(),
        subjectController: TextEditingController(),
        collegeController: TextEditingController(),
        passingYearController: TextEditingController(),
        percentageController: TextEditingController(),
      ),
    );
    _state.registerAuditorController.update();
  }

  void removeExperienceDetails(int index) {
    experienceList.removeAt(index);
    _state.registerAuditorController.update();
  }

  void removeQualificationDetails(int index) {
    qualificationList.removeAt(index);
    _state.registerAuditorController.update();
  }

  void documentOnTap(File? file, String text) async {
    if (text == AppStringConstants.leadAuditorCertificate) {
      leadAuditorCertificateFile =
          await FilePickerHelper.instance.pickFiles(_state.context, allowedExtensions: ['pdf'], fileSizeLimit: 2);
    } else if (text == AppStringConstants.educationQualification) {
      educationQualificationFile =
          await FilePickerHelper.instance.pickFiles(_state.context, allowedExtensions: ['pdf'], fileSizeLimit: 2);
    } else if (text == AppStringConstants.auditLog) {
      auditLogFile = await FilePickerHelper.instance.pickFiles(_state.context, allowedExtensions: ['pdf'], fileSizeLimit: 2);
    } else {
      sectorSpecificKnowledgeFile =
          await FilePickerHelper.instance.pickFiles(_state.context, allowedExtensions: ['pdf'], fileSizeLimit: 2);
    }
    _state.registerAuditorController.update();
  }

  Future openDatePicker(TextEditingController controller) async {
    DateTime? selectedDate = await appDatePicker(_state.context);
    controller.text = DateHelper.getDDMMYYYYFormatDate(selectedDate ?? DateTime.now());
  }

  void submitOnTap() {
    if (ValidationHelper.instance.validateEmptyController(auditorNameController)) {
      auditorErrorText = AppStringConstants.pleaseEnterAuditorName;
    } else {
      auditorErrorText = '';
    }
    if (ValidationHelper.instance.validateEmptyController(fatherNameController)) {
      fatherErrorText = AppStringConstants.pleaseEnterFatherName;
    } else {
      fatherErrorText = '';
    }
    if (ValidationHelper.instance.validateEmptyController(addressController)) {
      addressErrorText = AppStringConstants.pleaseEnterAddress;
    } else {
      addressErrorText = '';
    }
    if (ValidationHelper.instance.validateEmptyController(pinCodeController)) {
      pinCodeErrorText = AppStringConstants.pleaseEnterPinCodeNumber;
    } else {
      pinCodeErrorText = '';
    }
    if (ValidationHelper.instance.validateEmptyController(mobileNumberController)) {
      mobileErrorText = AppStringConstants.pleaseEnterMobileNumber;
    } else {
      mobileErrorText = '';
    }
    if (ValidationHelper.instance.validateEmptyController(emailAddressController)) {
      emailErrorText = AppStringConstants.pleaseEnterEmail;
    } else {
      emailErrorText = '';
    }
    for (var item in experienceList) {
      if (item.experienceMonthController.text.trim().isEmpty) {
        item.experienceMonthError = AppStringConstants.pleaseEnterExperienceInMonth;
      } else {
        item.experienceMonthError = '';
      }
      if (item.toController.text.trim().isEmpty) {
        item.toErrorText = AppStringConstants.pleaseSelectToDate;
      } else {
        item.toErrorText = '';
      }
      if (item.fromController.text.trim().isEmpty) {
        item.fromErrorText = AppStringConstants.pleaseSelectFromDate;
      } else {
        item.fromErrorText = '';
      }
      if (item.designationController.text.trim().isEmpty) {
        item.designationErrorText = AppStringConstants.pleaseEnterDesignation;
      } else {
        item.designationErrorText = '';
      }
      if (item.orgNameController.text.trim().isEmpty) {
        item.orgNameError = AppStringConstants.pleaseEnterOrganizationName;
      } else {
        item.orgNameError = '';
      }
      for (var item in qualificationList) {
        if (item.degreeController.text.trim().isEmpty) {
          item.degreeError = AppStringConstants.pleaseEnterGraduation;
        } else {
          item.degreeError = '';
        }
        if (item.subjectController.text.trim().isEmpty) {
          item.subjectErrorText = AppStringConstants.pleaseEnterStream;
        } else {
          item.subjectErrorText = '';
        }
        if (item.collegeController.text.trim().isEmpty) {
          item.collegeErrorText = AppStringConstants.pleaseEnterCollegeOrUniversityName;
        } else {
          item.collegeErrorText = '';
        }
        if (item.passingYearController.text.trim().isEmpty) {
          item.passingYearErrorText = AppStringConstants.pleaseEnterPassingYear;
        } else {
          item.passingYearErrorText = '';
        }
        if (item.percentageController.text.trim().isEmpty) {
          item.percentageError = AppStringConstants.pleaseEnterPercentageOrGrade;
        } else {
          item.percentageError = '';
        }
      }
      if (selectedStateModel == null) {
        stateErrorText = AppStringConstants.pleaseSelectState;
      } else {
        stateErrorText = '';
      }
      if (selectedDistrictModel == null) {
        districtErrorText = AppStringConstants.pleaseSelectDistrict;
      } else {
        districtErrorText = '';
      }
      if (leadAuditorCertificateFile == null) {
        leadAuditorCertificateErrorText = AppStringConstants.pleaseChooseFile;
      } else {
        leadAuditorCertificateErrorText = '';
      }
      if (educationQualificationFile == null) {
        educationQualificationErrorText = AppStringConstants.pleaseChooseFile;
      } else {
        educationQualificationErrorText = '';
      }
      if (auditLogFile == null) {
        auditLogErrorText = AppStringConstants.pleaseChooseFile;
      } else {
        auditLogErrorText = '';
      }
      if (sectorSpecificKnowledgeFile == null) {
        sectorSpecificKnowledgeErrorText = AppStringConstants.pleaseChooseFile;
      } else {
        sectorSpecificKnowledgeErrorText = '';
      }
      _state.registerAuditorController.update();
    }
  }
}
