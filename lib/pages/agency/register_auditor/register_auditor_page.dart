import 'package:ams/app_imports.dart';

class RegisterAuditorPage extends StatefulWidget {
  const RegisterAuditorPage({super.key});

  @override
  State<RegisterAuditorPage> createState() => RegisterAuditorPageState();
}

class RegisterAuditorPageState extends State<RegisterAuditorPage> {
  RegisterAuditorPageHelper? _registerAuditorPageHelper;
  late RegisterAuditorController registerAuditorController;
  @override
  Widget build(BuildContext context) {
    _registerAuditorPageHelper = _registerAuditorPageHelper ?? (RegisterAuditorPageHelper(this));
    return GetBuilder(
      init: RegisterAuditorController(),
      builder: (RegisterAuditorController controller) {
        registerAuditorController = controller;
        return Stack(
          children: [
            AppScaffold(
              appBar: _appBarView(),
              body: _bodyView(),
              apiStatus: _registerAuditorPageHelper!.apiStatus,
            ),
          ],
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.registerAuditor.tr);
  }

  Widget _bodyView() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColorConstants.colorAppPrimary),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: AppColorConstants.colorPeach,
              ),
              child: Column(
                children: [
                  RichText(
                    text: TextSpan(
                      text: '${AppStringConstants.note.tr}: ',
                      style: const TextStyle(
                        color: Colors.redAccent,
                        fontWeight: FontWeight.w500,
                      ),
                      children: const [
                        TextSpan(
                          text: AppStringConstants.registerAuditorNoteString,
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: AppColorConstants.colorBlack,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            _commonIconTextField(
              hintText: AppStringConstants.nameOfAuditor,
              controller: _registerAuditorPageHelper!.auditorNameController,
              preFixIcon: AppAssetsConstants.icPerson,
              errorText: _registerAuditorPageHelper!.auditorErrorText,
            ),
            _commonIconTextField(
              hintText: AppStringConstants.fatherNameOfAuditor,
              controller: _registerAuditorPageHelper!.fatherNameController,
              preFixIcon: AppAssetsConstants.icPerson,
              errorText: _registerAuditorPageHelper!.fatherErrorText,
            ),
            _commonIconTextField(
              hintText: AppStringConstants.address,
              controller: _registerAuditorPageHelper!.addressController,
              preFixIcon: AppAssetsConstants.icPerson,
              errorText: _registerAuditorPageHelper!.addressErrorText,
            ),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppText(
                          AppStringConstants.state.tr,
                          fontWeight: FontWeight.w500,
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        AppDropdownButton(
                          selectedMenuItemBuilder: (context, child) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                              child: AppText(
                                _registerAuditorPageHelper!.selectedStateModel?.stateName?? '',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                                fontWeight: FontWeight.w400,
                                fontSize: 16,
                                color: AppColorConstants.colorBlack,
                              ),
                            );
                          },
                          buttonHeight: 40,
                          dropdownMenuHeight: 130,
                          itemPadding: const EdgeInsets.all(10),
                          hint: AppStringConstants.notSelected.tr,
                          value: _registerAuditorPageHelper!.selectedStateModel,
                          errorText: _registerAuditorPageHelper!.stateErrorText.tr,
                          onChanged: _registerAuditorPageHelper!.stateOnchange,
                          itemBuilder: _registerAuditorPageHelper!.stateList
                              .map(
                                (item) => DropdownMenuItem(
                                  value: item,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 10),
                                    child: AppText(
                                      item.stateName??'',
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                      fontWeight: FontWeight.w400,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                          selectedItemBuilder: (context) {
                            return _registerAuditorPageHelper!.stateList
                                .map(
                                  (e) => Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 5),
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: AppText(
                                        e.stateName??'',
                                        maxLines: 1,
                                        fontSize: 16,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ),
                                )
                                .toList();
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppText(
                          AppStringConstants.district.tr,
                          fontWeight: FontWeight.w500,
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        AppDropdownButton(
                          buttonHeight: 40,
                          dropdownMenuHeight: 130,
                          itemPadding: const EdgeInsets.all(10),
                          hint: AppStringConstants.notSelected.tr,
                          errorText: _registerAuditorPageHelper!.districtErrorText.tr,
                          value: _registerAuditorPageHelper!.selectedDistrictModel,
                          onChanged: _registerAuditorPageHelper!.districtOnchange,
                          selectedMenuItemBuilder: (context, child) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                              child: AppText(
                                  _registerAuditorPageHelper!.selectedDistrictModel?.districtName?? '',
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 16,
                                  color: AppColorConstants.colorBlack,
                              ),
                            );
                          },
                          itemBuilder: _registerAuditorPageHelper!.districtList
                              .map(
                                (item) => DropdownMenuItem(
                                  value: item,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 10),
                                    child: AppText(
                                      item.districtName??'',
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                      fontWeight: FontWeight.w400,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                          selectedItemBuilder: (context) {
                            return _registerAuditorPageHelper!.districtList
                                .map(
                                  (e) => Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 5),
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: AppText(
                                        e.districtName??'',
                                        maxLines: 1,
                                        fontSize: 16,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ),
                                )
                                .toList();
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            _commonIconTextField(
              hintText: AppStringConstants.pincode,
              controller: _registerAuditorPageHelper!.pinCodeController,
              preFixIcon: AppAssetsConstants.icMail,
              errorText: _registerAuditorPageHelper!.pinCodeErrorText,
            ),
            _commonIconTextField(
              hintText: AppStringConstants.mobile,
              controller: _registerAuditorPageHelper!.mobileNumberController,
              preFixIcon: AppAssetsConstants.icPerson,
              errorText: _registerAuditorPageHelper!.mobileErrorText,
            ),
            _commonIconTextField(
              hintText: AppStringConstants.email,
              controller: _registerAuditorPageHelper!.emailAddressController,
              preFixIcon: AppAssetsConstants.icMail,
              errorText: _registerAuditorPageHelper!.emailErrorText,
            ),
            const SizedBox(
              height: 12,
            ),
            Container(
              height: 1,
              width: double.infinity,
              color: AppColorConstants.colorAppPrimary,
            ),
            const SizedBox(
              height: 12,
            ),
            AppText(
              AppStringConstants.qualificationDetail.tr,
              fontWeight: FontWeight.w700,
              fontSize: 16,
            ),
            const SizedBox(
              height: 12,
            ),
            _qualificationDetailsView(),
            Container(
              height: 1,
              width: double.infinity,
              color: AppColorConstants.colorAppPrimary,
            ),
            const SizedBox(
              height: 12,
            ),
            AppText(
              AppStringConstants.experienceDetail.tr,
              fontWeight: FontWeight.w700,
              fontSize: 16,
            ),
            const SizedBox(
              height: 12,
            ),
            _experienceDetailsView(),
            Container(
              height: 1,
              width: double.infinity,
              color: AppColorConstants.colorAppPrimary,
            ),
            const SizedBox(
              height: 12,
            ),
            AppText(
              AppStringConstants.documentAttached.tr,
              fontWeight: FontWeight.w700,
              fontSize: 16,
            ),
            const SizedBox(
              height: 12,
            ),
            _openDocumentView(AppStringConstants.leadAuditorCertificate, _registerAuditorPageHelper!.leadAuditorCertificateFile,
                _registerAuditorPageHelper!.leadAuditorCertificateErrorText,),
            _openDocumentView(AppStringConstants.educationQualification, _registerAuditorPageHelper!.educationQualificationFile,
                _registerAuditorPageHelper!.educationQualificationErrorText,),
            _openDocumentView(AppStringConstants.auditLog, _registerAuditorPageHelper!.auditLogFile,
                _registerAuditorPageHelper!.auditLogErrorText,),
            _openDocumentView(AppStringConstants.sectorSpecificKnowledge, _registerAuditorPageHelper!.sectorSpecificKnowledgeFile,
                _registerAuditorPageHelper!.sectorSpecificKnowledgeErrorText,),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: AppButton(onTap: () => _registerAuditorPageHelper!.submitOnTap(), title: AppStringConstants.submit.tr),
            ),
          ],
        ),
      ),
    );
  }

  Widget _commonIconTextField({
    required String hintText,
    required TextEditingController controller,
    required String preFixIcon,
    required String errorText,
    bool readOnly = false,
  }) {
    return Padding(
      padding: const EdgeInsets.all(12.0).copyWith(bottom: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppText(
            hintText.tr,
            fontWeight: FontWeight.w500,
          ),
          const SizedBox(
            height: 5,
          ),
          AppTextFormField(
            readOnly: readOnly,
            controller: controller,
            hintText: hintText.tr,
            errorText: errorText.tr,
            onTap: (readOnly) ? () => _registerAuditorPageHelper!.openDatePicker(controller) : () {},
            prefixIcon: Padding(
              padding: const EdgeInsets.all(16.0),
              child: AppImageAsset(
                image: preFixIcon,
                width: 13,
                height: 15,
                color: AppColorConstants.colorGrey,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _commonTextField(String hintText, TextEditingController controller, String errorText) {
    return Padding(
      padding: const EdgeInsets.all(12.0).copyWith(bottom: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppText(
            hintText.tr,
            fontWeight: FontWeight.w500,
          ),
          const SizedBox(
            height: 5,
          ),
          AppTextFormField(
            controller: controller,
            errorText: errorText.tr,
            hintText: hintText.tr,
          ),
        ],
      ),
    );
  }

  Widget _qualificationDetailsView() {
    return ListView.builder(
      itemCount: _registerAuditorPageHelper!.qualificationList.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return Column(
          children: [
            _commonTextField(
              AppStringConstants.degree,
              _registerAuditorPageHelper!.qualificationList[index].degreeController,
              _registerAuditorPageHelper!.qualificationList[index].degreeError,
            ),
            _commonTextField(
              AppStringConstants.subjectStream,
              _registerAuditorPageHelper!.qualificationList[index].subjectController,
              _registerAuditorPageHelper!.qualificationList[index].subjectErrorText,
            ),
            _commonTextField(
              AppStringConstants.collegeUniversity,
              _registerAuditorPageHelper!.qualificationList[index].collegeController,
              _registerAuditorPageHelper!.qualificationList[index].collegeErrorText,
            ),
            _commonIconTextField(
              readOnly: true,
              hintText: AppStringConstants.passingYear,
              controller: _registerAuditorPageHelper!.qualificationList[index].passingYearController,
              errorText: _registerAuditorPageHelper!.qualificationList[index].passingYearErrorText,
              preFixIcon: AppAssetsConstants.icCalender,
            ),
            _commonTextField(
              AppStringConstants.percentageGrade,
              _registerAuditorPageHelper!.qualificationList[index].percentageController,
              _registerAuditorPageHelper!.qualificationList[index].percentageError,
            ),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  Expanded(
                    child: _registerAuditorPageHelper!.qualificationList.length == 1
                        ? const SizedBox()
                        : AppButton(
                            height: 40,
                            title: AppStringConstants.remove.tr,
                            onTap: () => _registerAuditorPageHelper!.removeQualificationDetails(index),
                          ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: AppButton(
                      height: 40,
                      title: AppStringConstants.addMore.tr,
                      onTap: () => _registerAuditorPageHelper!.addMoreQualificationDetails(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _experienceDetailsView() {
    return ListView.builder(
      itemCount: _registerAuditorPageHelper!.experienceList.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return Column(
          children: [
            _commonTextField(
              AppStringConstants.organizationName,
              _registerAuditorPageHelper!.experienceList[index].orgNameController,
              _registerAuditorPageHelper!.experienceList[index].orgNameError,
            ),
            _commonTextField(
              AppStringConstants.designation,
              _registerAuditorPageHelper!.experienceList[index].designationController,
              _registerAuditorPageHelper!.experienceList[index].designationErrorText,
            ),
            Row(
              children: [
                Expanded(
                  child: _commonIconTextField(
                    hintText: AppStringConstants.from,
                    controller: _registerAuditorPageHelper!.experienceList[index].fromController,
                    errorText: _registerAuditorPageHelper!.experienceList[index].fromErrorText,
                    preFixIcon: AppAssetsConstants.icCalender,
                    readOnly: true,
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Expanded(
                  child: _commonIconTextField(
                    hintText: AppStringConstants.to,
                    controller: _registerAuditorPageHelper!.experienceList[index].toController,
                    errorText: _registerAuditorPageHelper!.experienceList[index].toErrorText,
                    preFixIcon: AppAssetsConstants.icCalender,
                    readOnly: true,
                  ),
                ),
              ],
            ),
            _commonTextField(
              AppStringConstants.experienceInMonth,
              _registerAuditorPageHelper!.experienceList[index].experienceMonthController,
              _registerAuditorPageHelper!.experienceList[index].experienceMonthError,
            ),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  Expanded(
                    child: _registerAuditorPageHelper!.experienceList.length == 1
                        ? const SizedBox()
                        : AppButton(
                            height: 40,
                            title: AppStringConstants.remove.tr,
                            onTap: () => _registerAuditorPageHelper!.removeExperienceDetails(index),
                          ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: AppButton(
                      height: 40,
                      title: AppStringConstants.addMore.tr,
                      onTap: () => _registerAuditorPageHelper!.addMoreExperienceDetails(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _openDocumentView(String text, File? file, String errorText) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppText(text.tr),
          const SizedBox(
            height: 5,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: AppColorConstants.colorGrey),
            ),
            child: Row(
              children: [
                InkWell(
                  onTap: () => _registerAuditorPageHelper!.documentOnTap(file, text),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
                    decoration: BoxDecoration(
                      color: AppColorConstants.colorGrey,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: AppColorConstants.colorLightGrey),
                    ),
                    child: AppText(
                      AppStringConstants.chooseFile.tr,
                      fontSize: 10,
                    ),
                  ),
                ),
                const SizedBox(
                  width: 5,
                ),
                (file != null)
                    ? AppText(file.path.split('/').last)
                    : AppText(
                        AppStringConstants.noFileChoosen.tr,
                        color: AppColorConstants.colorGrey,
                        fontSize: 12,
                      ),
              ],
            ),
          ),
          if (errorText.isNotEmpty) AppText(errorText.tr, color: AppColorConstants.colorRed, fontSize: 12),
        ],
      ),
    );
  }
}
