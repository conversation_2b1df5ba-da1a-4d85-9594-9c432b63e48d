import 'package:ams/app_imports.dart';


class AgencyNonCompliancePage extends StatefulWidget {
  const AgencyNonCompliancePage({super.key});

  @override
  State<AgencyNonCompliancePage> createState() => AgencyNonCompliancePageState();
}

class AgencyNonCompliancePageState extends State<AgencyNonCompliancePage> {
  AgencyNonCompliancePageHelper? _pageHelper;
  late AgencyNonComplianceController controller;

  @override
  Widget build(BuildContext context) {
    _pageHelper = _pageHelper ?? (AgencyNonCompliancePageHelper(this));
    return GetBuilder<AgencyNonComplianceController>(
      init: AgencyNonComplianceController(),
      builder: (AgencyNonComplianceController nonComplianceController) {
        controller=nonComplianceController;
        return AppScaffold(
          appBar: CommonAppBar(title: AppStringConstants.nonCompliance.tr),
          apiStatus: _pageHelper!.apiStatus,
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return tableCard(
      dropDownOnChanged: (value) => _pageHelper!.sortTypeOnChanged(value as String),
      dropdownList: _pageHelper!.dropdownList,
      dropdownValue: _pageHelper!.sortTypeValue.isEmpty ? null : _pageHelper!.sortTypeValue,
      searchController: _pageHelper!.searchController,
      isShowDropdown: true,
      isShowSearch: true,
      isMainListEmpty: _pageHelper!.isEmpty,
      isAscending: _pageHelper!.isAscending,
      sortOnTap: () => _pageHelper!.sortOnTap(),
      children: [
        const SizedBox(height: 10),
        for (var element in _pageHelper!.auditViewList)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Table(
              border: TableBorder.all(
                color: AppColorConstants.colorMediumGrey,
                borderRadius: BorderRadius.circular(5),
              ),
              children: [
                for (var entry in element.toJson().entries)
                  TableRow(
                    children: [
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.centerLeft,
                          color: AppColorConstants.colorLightGrey,
                          child: AppText(
                            entry.key.tr,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                     TableCell(
                              verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                child: (entry.key == AppStringConstants.auditorName || entry.key == AppStringConstants.address)
                                    ? Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          AppText(
                                            entry.value.toString(),
                                            fontSize: 12,
                                            color: AppColorConstants.colorBlack,
                                          ),
                                          Row(
                                            children: [
                                              AppImageAsset(image: AppAssetsConstants.icPhone),
                                              Expanded(
                                                child: AppText(
                                                  element.mobileNo ?? '',
                                                  fontSize: 12,
                                                  color: AppColorConstants.colorBlack,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : AppText(
                                        (entry.key == AppStringConstants.dateOfRequest)
                                            ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                            : '${entry.value ?? ''}',
                                        fontSize: 12,
                                        color: AppColorConstants.colorBlack,
                                      ),
                              ),
                            ),
                    ],
                  ),
              ],
            ),
          ),
      ],
    );
  }
}
