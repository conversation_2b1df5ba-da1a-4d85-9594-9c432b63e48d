import 'package:ams/app_imports.dart';

class AgencyNonCompliancePageHelper {
  final AgencyNonCompliancePageState _pageState;

  List<AgencyNonComplianceAuditViewModel> auditViewList = [];
  ApiStatus apiStatus=ApiStatus.initial;
  bool isEmpty=false;
  List<AgencyNonComplianceAuditViewModel> tmpList = [];
  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.idNo,
    AppStringConstants.agencyName,
    AppStringConstants.auditorName,
    AppStringConstants.licenseNo,
    AppStringConstants.contactPerson,
    AppStringConstants.fboName,
    AppStringConstants.address,
    AppStringConstants.kob,
    AppStringConstants.dateOfRequest,
  ];

  String sortTypeValue = AppStringConstants.srNo;
  bool isAscending = true;

  TextEditingController searchController = TextEditingController();

  AgencyNonCompliancePageHelper(this._pageState) {
   Future.delayed(const Duration(milliseconds: 200),()=>getNonComplianceData());
  }
  Future<void> getNonComplianceData() async {
    apiStatus=ApiStatus.loading;
    _pageState.controller.update();
    _pageState.controller.getNonComplianceData(416).then((value) {
      int srNo=1;
      for(var model in value){
        AgencyNonComplianceAuditViewModel agencyNonComplianceAuditViewModel=AgencyNonComplianceAuditViewModel(
          srNo:srNo++,
          idNo: model.inspectionrequestid.toString(),
          auditorName: model.auditorname,
          address: model.address,
          agencyName: model.agencyname,
          licenseNo: model.licenseno,
          contactPerson: model.contactperson,
          kob: model.kob,
          dateOfRequest: DateFormat('dd/MM/yyyy').parse(model.dateofrequest),

        );
        auditViewList.add(agencyNonComplianceAuditViewModel);
      }
      if(auditViewList.isEmpty){
        isEmpty=true;
      }
      tmpList=auditViewList;
      apiStatus=ApiStatus.success;
      _pageState.controller.update();
    });
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      if (sortTypeValue == AppStringConstants.dateOfRequest) {
        auditViewList.sort(
          (a, b) => a.dateOfRequest!.compareTo(b.dateOfRequest ?? DateTime.now()),
        );
      } else {
        auditViewList.sort(
          (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
        );
      }
    } else {
      if (sortTypeValue == AppStringConstants.dateOfRequest) {
        auditViewList.sort(
          (a, b) => b.dateOfRequest!.compareTo(a.dateOfRequest ?? DateTime.now()),
        );
      } else {
        auditViewList.sort(
          (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
        );
      }
    }
    _pageState.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }
}
