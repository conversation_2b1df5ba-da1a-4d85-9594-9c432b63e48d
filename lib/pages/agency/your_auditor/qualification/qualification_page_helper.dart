import 'package:ams/app_imports.dart';

class QualificationPageHelper {
  QualificationPageState state;
  List<QualificationDetails> qualificationList = [];
  ApiStatus apiStatus = ApiStatus.initial;

  QualificationPageHelper(this.state){
    Future.delayed((const Duration(milliseconds: 200)),()=>getApplicationData());
  }

  Future<void> getApplicationData() async {
    apiStatus = ApiStatus.loading;
    state.yourAuditorsController.update();
    await state.yourAuditorsController.getYourAuditorQualificationData(auditorId: 279).then((value) {
      qualificationList=[];
      int srNo = 1;
      for (var model in value) {
        qualificationList.add(
          QualificationDetails(
            srNo: srNo,
            passingYear: model.passingyear?.toString()??'',
            percentageGrade: model.percentage.toString(),
            qualificationCertificate: model.qulname,
            subject: model.subject,
            university: model.universityboard,
          ),
        );
        srNo++;
      }
      apiStatus = ApiStatus.success;
      state.yourAuditorsController.update();
    });
  }
}
