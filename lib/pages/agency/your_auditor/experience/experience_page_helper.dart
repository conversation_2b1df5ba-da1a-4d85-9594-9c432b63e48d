import 'package:ams/app_imports.dart';

class ExperiencePageHelper {
  final ExperiencePageState _state;
  List<ExperienceDetails> experienceDetailsList = [];
  ApiStatus apiStatus = ApiStatus.initial;
  ExperiencePageHelper(this._state){
    Future.delayed((const Duration(milliseconds: 200)),()=>getExperienceData());
  }

  Future<void> getExperienceData() async {
    apiStatus = ApiStatus.loading;
    _state.yourAuditorsController.update();
    _state.yourAuditorsController.getExperienceList(auditorId: 279).then((value) {
      int srNo = 1;
      for (var model in value) {
        experienceDetailsList.add(
          ExperienceDetails(
            srNo: srNo,
            organizationName: model.orgName??'',
            designation: model.designation??'',
            from: DateHelper.getDDMMMMYYYYDateFormatDate(model.fromExp ?? DateTime.now()),
            to: DateHelper.getDDMMMMYYYYDateFormatDate(model.fromExp ?? DateTime.now()),
            experienceInMonth: model.expMonth?.toString()?? '0',
          ),
        );
        srNo++;
      }
      apiStatus=ApiStatus.success;
      _state.yourAuditorsController.update();
    });
  }
}
