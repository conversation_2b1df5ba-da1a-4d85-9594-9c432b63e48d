import 'package:ams/app_imports.dart';

class ExperiencePage extends StatefulWidget {
  final Map<String,dynamic> auditor;
  const ExperiencePage({required this.auditor, super.key});

  @override
  State<ExperiencePage> createState() => ExperiencePageState();
}

class ExperiencePageState extends State<ExperiencePage> {
  ExperiencePageHelper? _experiencePageHelper;
  late YourAuditorsController yourAuditorsController;
  @override
  Widget build(BuildContext context) {
    _experiencePageHelper = _experiencePageHelper ?? (_experiencePageHelper = ExperiencePageHelper(this));
    return GetBuilder(
      init: YourAuditorsController(),
      builder: (YourAuditorsController controller) {
        yourAuditorsController = controller;
        return AppScaffold(
          appBar: _appBarView(),
          body: _bodyView(),
          apiStatus:_experiencePageHelper!.apiStatus,
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.experienceDetailsOf.tr);
  }

  Widget _bodyView() {
    if (_experiencePageHelper!.experienceDetailsList.isEmpty) {
      return const NoDataFoundView();
    }
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 5),
      itemCount: _experiencePageHelper!.experienceDetailsList.length,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.all(12.0),
          child: Table(
            border: TableBorder.all(
              color: AppColorConstants.colorMediumGrey,
              borderRadius: BorderRadius.circular(5),
            ),
            children: [
              for (var entry in _experiencePageHelper!.experienceDetailsList[index].toJson().entries)
                TableRow(
                  children: [
                    TableCell(
                      verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        alignment: Alignment.centerLeft,
                        color: AppColorConstants.colorLightPurple,
                        child: AppText(
                          entry.key.toString().tr,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    TableCell(
                      verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: AppText(
                          entry.value.toString(),
                          fontSize: 12,
                          color: AppColorConstants.colorBlack,
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }
}
