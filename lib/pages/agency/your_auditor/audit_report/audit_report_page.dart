import 'package:ams/app_imports.dart';

class AuditReportPage extends StatefulWidget {
  const AuditReportPage({super.key});

  @override
  State<AuditReportPage> createState() => AuditReportPageState();
}

class AuditReportPageState extends State<AuditReportPage> {
  AuditReportPageHelper? _auditReportPageHelper;
  late YourAuditorsController yourAuditorsController;
  @override
  Widget build(BuildContext context) {
    _auditReportPageHelper = _auditReportPageHelper ?? (AuditReportPageHelper(this));
    return GetBuilder(
      init: YourAuditorsController(),
      builder: (YourAuditorsController controller) {
        yourAuditorsController = controller;
        return Stack(
          children: [
            AppScaffold(
              apiStatus: _auditReportPageHelper!.apiStatus,
              appBar: _appBarView(),
              body: _bodyView(),
            ),
          ],
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.auditReportSubmitted);
  }

  Widget _bodyView() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _searchFiledView(),
          const SizedBox(
            height: 10,
          ),
          _tableView(),
        ],
      ),
    );
  }

  Widget _searchFiledView() {
    return AppTextFormField(
      border: const OutlineInputBorder(
        borderSide: BorderSide(color: AppColorConstants.colorAppPrimary, width: 0.5),
      ),
      hintColor: AppColorConstants.colorBlack,
      hintTextSize: 12,
      suffixIcon: (_auditReportPageHelper!.searchController.text.isNotEmpty)
          ? InkWell(
              onTap: () => _auditReportPageHelper!.clearValue(),
              child: Padding(
                padding: const EdgeInsets.only(top: 8, left: 10, bottom: 8),
                child: AppImageAsset(image: AppAssetsConstants.icCancel),
              ),
            )
          : null,
      textFieldFontSize: 12,
      contentPadding: EdgeInsets.zero,
      controller: _auditReportPageHelper!.searchController,
      hintText: AppStringConstants.search.tr,
      textFieldHeight: 35,
      onChanged: _auditReportPageHelper!.searchOnChange,
      textFieldColor: AppColorConstants.colorLightPurple,
      prefixIcon: Padding(
        padding: const EdgeInsets.symmetric(vertical: 5),
        child: AppImageAsset(
          image: AppAssetsConstants.icSearch,
        ),
      ),
    );
  }

  Widget _tableView() {
    if (_auditReportPageHelper!.searchList.isEmpty) {
      return const Expanded(child: NoDataFoundView());
    }
    return Expanded(
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 5),
        itemCount: _auditReportPageHelper!.searchList.length,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.all(12.0).copyWith(left: 0, right: 0),
            child: Table(
              border: TableBorder.all(
                color: AppColorConstants.colorMediumGrey,
                borderRadius: BorderRadius.circular(5),
              ),
              children: [
                for (var entry in _auditReportPageHelper!.searchList[index].toJson().entries)
                  TableRow(
                    children: [
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.centerLeft,
                          color: AppColorConstants.colorLightPurple,
                          child: AppText(
                            entry.key.toString().tr,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      entry.key == AppStringConstants.auditReport
                          ? TableCell(
                              verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                              child: InkWell(
                                onTap: () => launchDocumentUrl(''),
                                child: Container(
                                  padding: const EdgeInsets.all(10),
                                  alignment: Alignment.topLeft,
                                  child: AppImageAsset(
                                    image: AppAssetsConstants.icDownloadArrow,
                                    height: 20,
                                    width: 13,
                                  ),
                                ),
                              ),
                            )
                          : TableCell(
                              verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                              child: Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: AppText(
                                  entry.value.toString(),
                                  fontSize: 12,
                                  color: AppColorConstants.colorBlack,
                                ),
                              ),
                            ),
                    ],
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
