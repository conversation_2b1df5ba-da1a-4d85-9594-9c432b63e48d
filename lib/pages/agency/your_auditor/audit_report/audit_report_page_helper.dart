import 'package:ams/app_imports.dart';

class AuditReportPageHelper {
  final AuditReportPageState _state;
  TextEditingController searchController = TextEditingController();
  List<AuditDetailsReport> auditReportDetails = [
    AuditDetailsReport(
      srNo: 1,
      licenseNo: '1223',
      fboName: 'tr',
      state: 'gujrat',
      agency: 'demo',
      auditEndDate: 'f26-05-2024',
      auditReport: '04-06-2024',
      auditStartDate: 'hello',
    ),
    AuditDetailsReport(
      srNo: 2,
      licenseNo: '1223',
      fboName: 'tr',
      state: 'gujrat',
      agency: 'demo',
      auditEndDate: 'f26-05-2024',
      auditReport: '04-06-2024',
      auditStartDate: 'hello',
    ),
    AuditDetailsReport(
      srNo: 3,
      licenseNo: '1223',
      fboName: 'tr',
      state: 'gujrat',
      agency: 'newdemo',
      auditEndDate: 'f26-05-2024',
      auditReport: '04-06-2024',
      auditStartDate: 'hello',
    ),
  ];
  List<AuditDetailsReport> searchList = [];
  ApiStatus apiStatus=ApiStatus.initial;
  AuditReportPageHelper(this._state) {
    searchList = auditReportDetails;
  }

  void searchOnChange(String value) {
    searchList = auditReportDetails.where((element) {
      return ((element.srNo.toString().toLowerCase().contains(value)) ||
          (element.state.toString().toLowerCase().contains(value)) ||
          (element.licenseNo.toString().toLowerCase().contains(value)) ||
          (element.fboName.toString().toLowerCase().contains(value)) ||
          (element.district.toString().toLowerCase().contains(value)) ||
          (element.agency.toString().toLowerCase().contains(value)) ||
          (element.auditStartDate.toString().toLowerCase().contains(value)) ||
          (element.auditEndDate.toString().toLowerCase().contains(value)) ||
          (element.auditSubmissionDate.toString().toLowerCase().contains(value)));
    }).toList();
    _state.yourAuditorsController.update();
  }

  void clearValue() {
    FocusScope.of(_state.context).unfocus();
    searchController.clear();
    searchList = auditReportDetails;
    _state.yourAuditorsController.update();
  }
}
