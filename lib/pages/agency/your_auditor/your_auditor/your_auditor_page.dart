import 'package:ams/app_imports.dart';

class YourAuditorPage extends StatefulWidget {
  final int agencyId;
  const YourAuditorPage({required this.agencyId, super.key});

  @override
  State<YourAuditorPage> createState() => YourAuditorPageState();
}

class YourAuditorPageState extends State<YourAuditorPage> {
  late YourAuditorsController yourAuditorsController;
  YourAuditorPageHelper? _yourAuditorPageHelper;
  @override
  Widget build(BuildContext context) {
    _yourAuditorPageHelper = _yourAuditorPageHelper ?? (YourAuditorPageHelper(this));
    return GetBuilder(
      init: YourAuditorsController(),
      builder: (YourAuditorsController controller) {
        yourAuditorsController = controller;
        return AppScaffold(
          apiStatus: _yourAuditorPageHelper!.apiStatus,
          appBar: _appBarView(),
          body: Column(
            children: [
              Expanded(
                child: tableCard(
                  isMainListEmpty: _yourAuditorPageHelper!.isEmpty,
                  children: [
                    for (var item in _yourAuditorPageHelper!.searchList) _tableView(item.toJson()),
                  ],
                  sortOnTap: _yourAuditorPageHelper!.sortOnTap,
                  searchController: _yourAuditorPageHelper!.searchController,
                  dropdownValue: _yourAuditorPageHelper!.selectedValue,
                  dropDownOnChanged: _yourAuditorPageHelper!.dropDownOnChange,
                  dropdownList: _yourAuditorPageHelper!.dropdownList,
                  isShowDropdown: true,
                  isAscending: _yourAuditorPageHelper!.isAscending,
                  searchOnChanged: _yourAuditorPageHelper!.searchOnChange,
                  isShowSearch: true,
                  clearSearchOnTap: _yourAuditorPageHelper!.clearValue,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.auditors.tr);
  }

  Widget _tableView(Map<String, dynamic> item) {
    return Padding(
      padding: const EdgeInsets.all(12.0).copyWith(left: 0, right: 0),
      child: Table(
        columnWidths: const {0: FixedColumnWidth(140)},
        border: TableBorder.all(
          color: AppColorConstants.colorMediumGrey,
          borderRadius: BorderRadius.circular(5),
        ),
        children: [
          _commonTextRow(key: AppStringConstants.srNo, value: item[AppStringConstants.srNo].toString()),
          _auditorName(AppStringConstants.auditorName, item[AppStringConstants.auditorName], 'pending'),
          _commonTextRow(key: AppStringConstants.emailAndMobile, value: item[AppStringConstants.emailAndMobile]),
          _commonTextRow(key: AppStringConstants.agencyName, value: item[AppStringConstants.agencyName]),
          _commonTextRow(
            key: AppStringConstants.auditsConducted,
            value: item[AppStringConstants.auditsConducted],
            onTap: gotoAuditReportPage,
          ),
          _commonIconRow(
            AppStringConstants.experience,
            AppAssetsConstants.icEye,
            () => gotoExperiencePage(item: item),
            12,
          ),
          _commonIconRow(
              AppStringConstants.qualification, AppAssetsConstants.icForward, () => gotoQualificationPage(item: item), 17,),
          _commonIconRow(AppStringConstants.removeAuditor, AppAssetsConstants.icDelete, _deleteAppDialog, 19),
          _documentAttached(item),
        ],
      ),
    );
  }

  TableRow _commonTextRow({required String key, required String value, VoidCallback? onTap}) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: InkWell(
            onTap: onTap == null ? () {} : () => onTap(),
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: AppText(
                value.toString().tr,
                fontSize: 12,
                color: onTap == null ? AppColorConstants.colorBlack : AppColorConstants.colorSkyBlue,
              ),
            ),
          ),
        ),
      ],
    );
  }

  TableRow _auditorName(String key, String value, String value2) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              children: [
                AppText(
                  value.toString(),
                  fontSize: 12,
                  color: AppColorConstants.colorBlack,
                ),
                const SizedBox(
                  width: 5,
                ),
                AppText(
                  '[${value2.toString()}]',
                  fontSize: 12,
                  color: AppColorConstants.colorRedAccent,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  TableRow _commonIconRow(String key, String image, VoidCallback onTap, double height) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: InkWell(
            onTap: () => onTap(),
            child: Container(
              color: AppColorConstants.colorWhite,
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.all(10),
              child: AppImageAsset(
                image: image,
                height: height,
              ),
            ),
          ),
        ),
      ],
    );
  }

  TableRow _documentAttached(Map<String, dynamic> item) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            child: AppText(
              AppStringConstants.documentAttached.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () =>
                      launchDocumentUrl(item[AppStringConstants.leadAuditorCertificateEducationalQualificationCertificate]),
                  child: AppText(
                    AppStringConstants.leadAuditorCertificate.tr,
                    fontSize: 10,
                    color: AppColorConstants.colorSkyBlue,
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                InkWell(
                  onTap: () => launchDocumentUrl(item[AppStringConstants.educationalQualificationCertificate]),
                  child: AppText(
                    AppStringConstants.educationalQualificationCertificate.tr,
                    fontSize: 10,
                    color: AppColorConstants.colorSkyBlue,
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                InkWell(
                  onTap: () => launchDocumentUrl(item[AppStringConstants.auditorLog]),
                  child: AppText(
                    AppStringConstants.auditorLog.tr,
                    fontSize: 10,
                    color: AppColorConstants.colorSkyBlue,
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                InkWell(
                  onTap: () => launchDocumentUrl(item[AppStringConstants.sectorSpecificKnowledge]),
                  child: AppText(
                    AppStringConstants.sectorSpecificKnowledge.tr,
                    fontSize: 10,
                    color: AppColorConstants.colorSkyBlue,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future _deleteAppDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: AppColorConstants.colorWhite,
          title: AppText(
            AppStringConstants.verifyRejectAgency.tr,
            color: AppColorConstants.colorSkyBlue,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText(AppStringConstants.enterRemarks.tr, fontWeight: FontWeight.w700),
              const SizedBox(
                height: 10,
              ),
              AppTextFormField(
                controller: _yourAuditorPageHelper!.remarkController,
                hintText: AppStringConstants.enterRemarks.tr,
              ),
              const SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Expanded(
                    child: AppButton(
                      height: 35,
                      title: AppStringConstants.submit.tr,
                      onTap: () {},
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: AppButton(
                      height: 35,
                      title: AppStringConstants.close.tr,
                      onTap: () => gotoBack(),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
