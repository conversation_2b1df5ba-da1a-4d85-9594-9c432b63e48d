import 'package:ams/app_imports.dart';

class YourAuditorPageHelper {
  final YourAuditorPageState _state;
  ApiStatus apiStatus = ApiStatus.initial;
  TextEditingController searchController = TextEditingController();
  TextEditingController remarkController = TextEditingController();
  String? selectedValue;
  bool isEmpty=false;
  bool isAscending = true;
  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.auditorName,
    AppStringConstants.emailAndMobile,
    AppStringConstants.agencyName,
    AppStringConstants.auditsConducted,
    AppStringConstants.experience,
    AppStringConstants.qualification,
  ];
  List<YourAuditorDetails> yourAuditorDetailsList = [];
  List<YourAuditorDetails> searchList = [];
  List<AgencyYourAuditorsModel> data = [];

  YourAuditorPageHelper(this._state) {
   Future.delayed(const Duration(milliseconds: 10),()=> getApplicationData());
  }

  Future<void> getApplicationData() async {
    apiStatus = ApiStatus.loading;
    _state.yourAuditorsController.update();
    setDefaultValue();
    int srNo=1;
     await _state.yourAuditorsController.getAgencyPendingWithAuditorData(agencyId: _state.widget.agencyId).then((value){
       yourAuditorDetailsList=[];
      for(var model in value){
        yourAuditorDetailsList.add(
          YourAuditorDetails(
            srNo: srNo,
            auditsConducted: model.noofaudits.toString(),
            experience: model.expertise,
            auditorName: model.auditorname,
            agencyName: model.agencyname,
            emailAndMobile: '${model.email}\n${model.mobile}',
            qualification: model.qualificationfile,
            leadAuditorCertificate: model.leadauditorfile,
            sectorSpecificFile: model.sectorspecificfile,
            auditorLogFile: model.auditorlogfile,
            qualificationFile: model.qualificationfile,
          ),
        );
        srNo++;
      }
      if(yourAuditorDetailsList.isEmpty){
        isEmpty=true;
      }
      searchList=yourAuditorDetailsList;
      apiStatus=ApiStatus.success;
      _state.yourAuditorsController.update();
    });

  }

  void setDefaultValue() {
    selectedValue = AppStringConstants.srNo;
    searchController.clear();
    isAscending = true;
  }

  void searchOnChange(String text) {
    searchList = yourAuditorDetailsList.where((element) {
      return ((element.removeAuditor.toString().toLowerCase().contains(text)) ||
          (element.qualification.toString().toLowerCase().contains(text)) ||
          (element.experience.toString().toLowerCase().contains(text)) ||
          (element.srNo.toString().toLowerCase().contains(text)) ||
          (element.agencyName.toString().toLowerCase().contains(text)) ||
          (element.emailAndMobile.toString().toLowerCase().contains(text)) ||
          (element.auditorName.toString().toLowerCase().contains(text)) ||
          (element.auditsConducted.toString().toLowerCase().contains(text)));
    }).toList();
    _state.yourAuditorsController.update();
  }

  void clearValue() {
    FocusScope.of(_state.context).unfocus();
    searchController.clear();
    searchList = yourAuditorDetailsList;
    _state.yourAuditorsController.update();
  }

  void dropDownOnChange(dynamic value) {
    selectedValue = value;
    sortOnchange();
    _state.yourAuditorsController.update();
  }

  void sortOnchange() {
    if (isAscending) {
      yourAuditorDetailsList.sort((a, b) => a.toJson()[selectedValue].compareTo(b.toJson()[selectedValue]));
    } else {
      yourAuditorDetailsList.sort((a, b) => b.toJson()[selectedValue].compareTo(a.toJson()[selectedValue]));
    }
    _state.yourAuditorsController.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortOnchange();
    _state.yourAuditorsController.update();
  }
}
