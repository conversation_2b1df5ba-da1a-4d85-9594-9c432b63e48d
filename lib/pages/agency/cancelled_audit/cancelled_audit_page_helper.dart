import 'package:ams/app_imports.dart';

class AgencyCancelledAuditPageHelper {
  final AgencyCancelledAuditPageState _pageState;

  List<AgencyCancelAuditViewModel> searchList = [];
  List<AgencyCancelAuditViewModel> cancelledAuditViewList = [];
  bool isEmpty=false;
  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.idNo,
    AppStringConstants.agencyName,
    AppStringConstants.auditorName,
    AppStringConstants.licenseNo,
    AppStringConstants.contactPerson,
    AppStringConstants.fboName,
    AppStringConstants.address,
    AppStringConstants.kob,
    AppStringConstants.dateOfRequest,
  ];

  String sortTypeValue = AppStringConstants.srNo;
  bool isAscending = true;

  TextEditingController searchController = TextEditingController();

  ApiStatus apiStatus = ApiStatus.initial;
  AgencyCancelledAuditPageHelper(this._pageState) {
    Future.delayed(const Duration(milliseconds: 10), () => getApplicationData());
  }

  Future<void> getApplicationData() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    setDefaultValue();
    await _pageState.controller.getAgencyCancelledAuditsData().then((value) {
      cancelledAuditViewList=[];
      int srNo = 1;
      for (var model in value) {
        cancelledAuditViewList.add(
          AgencyCancelAuditViewModel(
            contactPerson: model.contactperson,
            fboName: model.fboname,
            address: model.address,
            kob: model.kob,
            mobileNo: model.reportno,
            dateOfRequest: DateFormat('dd/MM/yyyy').parse(model.dateofrequest),
            report: model.reportno,
            auditorName: model.auditorname,
            idNo: model.inspectionreportid.toString(),
            licenseNo: model.licenseno,
            srNo: srNo++,
            agencyName: model.agencyname,
          ),
        );
      }
      searchList=cancelledAuditViewList;
      if(searchList.isEmpty){
        isEmpty=true;
      }
      apiStatus = ApiStatus.success;
      _pageState.controller.update();
    });
  }

  void setDefaultValue() {
    sortTypeValue = AppStringConstants.srNo;
    searchController.clear();
    isAscending = true;
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      if (sortTypeValue == AppStringConstants.dateOfRequest) {
        cancelledAuditViewList.sort(
          (a, b) => a.dateOfRequest!.compareTo(b.dateOfRequest ?? DateTime.now()),
        );
      } else {
        cancelledAuditViewList.sort(
          (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
        );
      }
    } else {
      if (sortTypeValue == AppStringConstants.dateOfRequest) {
        cancelledAuditViewList.sort(
          (a, b) => b.dateOfRequest!.compareTo(a.dateOfRequest ?? DateTime.now()),
        );
      } else {
        cancelledAuditViewList.sort(
          (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
        );
      }
    }
    _pageState.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }

  void searchOnChange(String text) {
    searchList = cancelledAuditViewList.where((element) {
      return ((element.srNo.toString().toLowerCase().contains(text)) ||
          (element.idNo.toString().toLowerCase().contains(text)) ||
          (element.agencyName.toString().toLowerCase().contains(text)) ||
          (element.auditorName.toString().toLowerCase().contains(text)) ||
          (element.licenseNo.toString().toLowerCase().contains(text)) ||
          (element.contactPerson.toString().toLowerCase().contains(text)) ||
          (element.address.toString().toLowerCase().contains(text)) ||
          (element.kob.toString().toLowerCase().contains(text)) ||
          (element.dateOfRequest.toString().toLowerCase().contains(text)) ||
          (element.fboName.toString().toLowerCase().contains(text)));
    }).toList();

    _pageState.controller.update();
  }

  void clearValue() {
    FocusScope.of(_pageState.context).unfocus();
    searchController.clear();
    searchList = cancelledAuditViewList;
    _pageState.controller.update();
  }
}
