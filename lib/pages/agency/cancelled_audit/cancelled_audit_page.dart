import 'package:ams/app_imports.dart';

class AgencyCancelledAuditPage extends StatefulWidget {
  const AgencyCancelledAuditPage({super.key});

  @override
  State<AgencyCancelledAuditPage> createState() => AgencyCancelledAuditPageState();
}

class AgencyCancelledAuditPageState extends State<AgencyCancelledAuditPage> {
  late AgencyCancelledAuditController controller;
  AgencyCancelledAuditPageHelper? _pageHelper;
  @override
  Widget build(BuildContext context) {
    _pageHelper = _pageHelper ?? (AgencyCancelledAuditPageHelper(this));
    return GetBuilder<AgencyCancelledAuditController>(
      init: AgencyCancelledAuditController(),
      builder: (AgencyCancelledAuditController cancelledAuditController) {
        controller = cancelledAuditController;
        return AppScaffold(
          apiStatus: _pageHelper!.apiStatus,
          appBar: CommonAppBar(title: AppStringConstants.auditReportCancelled.tr),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return tableCard(
      dropDownOnChanged: (value) => _pageHelper!.sortTypeOnChanged(value as String),
      dropdownList: _pageHelper!.dropdownList,
      dropdownValue: _pageHelper!.sortTypeValue.isEmpty ? null : _pageHelper!.sortTypeValue,
      searchController: _pageHelper!.searchController,
      isShowDropdown: true,
      searchOnChanged: _pageHelper!.searchOnChange,
      clearSearchOnTap: _pageHelper!.clearValue,
      isShowSearch: true,
      isMainListEmpty: _pageHelper!.isEmpty,
      isAscending: _pageHelper!.isAscending,
      sortOnTap: () => _pageHelper!.sortOnTap(),
      children: [
        const SizedBox(height: 10),
        for (var element in _pageHelper!.searchList)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Table(
              border: TableBorder.all(
                color: AppColorConstants.colorMediumGrey,
                borderRadius: BorderRadius.circular(5),
              ),
              children: [
                for (var entry in element.toJson().entries)
                  TableRow(
                    children: [
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.centerLeft,
                          color: AppColorConstants.colorLightGrey,
                          child: AppText(
                            entry.key.tr,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      (entry.key == AppStringConstants.report)
                          ? TableCell(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AppButton(
                                      onTap: () => gotoInspectionReportPage(),
                                      title: AppStringConstants.view.tr,
                                      borderRadius: BorderRadius.circular(5),
                                      textSize: 12,
                                      width: 80,
                                      height: 30,
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : TableCell(
                              verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                child: (entry.key == AppStringConstants.auditorName || entry.key == AppStringConstants.address)
                                    ? Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          AppText(
                                            entry.value.toString(),
                                            fontSize: 12,
                                            color: AppColorConstants.colorBlack,
                                          ),
                                          Row(
                                            children: [
                                              AppImageAsset(image: AppAssetsConstants.icPhone),
                                              Expanded(
                                                child: AppText(
                                                  element.mobileNo ?? '',
                                                  fontSize: 12,
                                                  color: AppColorConstants.colorBlack,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : AppText(
                                        (entry.key == AppStringConstants.dateOfRequest)
                                            ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                            : '${entry.value ?? ''}',
                                        fontSize: 12,
                                        color: AppColorConstants.colorBlack,
                                      ),
                              ),
                            ),
                    ],
                  ),
              ],
            ),
          ),
      ],
    );
  }
}
