import 'package:ams/app_imports.dart';

class SuperAdminDashboardPage extends StatefulWidget {
  const SuperAdminDashboardPage({super.key});

  @override
  State<SuperAdminDashboardPage> createState() => SuperAdminDashboardPageState();
}

class SuperAdminDashboardPageState extends State<SuperAdminDashboardPage> {

  SuperAdminDashboardPageHelper? _helper;
  late SuperAdminDashboardController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? SuperAdminDashboardPageHelper(this);
    return GetBuilder<SuperAdminDashboardController>(
      init: SuperAdminDashboardController(),
      builder: (SuperAdminDashboardController superAdminDashboardController) {
        controller = superAdminDashboardController;
        return AppScaffold(
          apiStatus: _helper!.apiStatus,
          scaffoldKey: _helper!.drawerKey,
          drawer: _drawerView(),
          appBar: _appBarView(),
          body: _bodyView(),
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(
      title: AppStringConstants.dashboard.tr,
      titleSize: 18,
      isBack: false,
      leading: Padding(
        padding: const EdgeInsets.only(left: 0,bottom: 15,top: 15,right: 0),
        child: InkWell(
          onTap: () => _helper!.drawerKey.currentState?.openDrawer(),
          child: AppImageAsset(image: AppAssetsConstants.icMenu,height: 10,width: 10,),
        ),
      ),
      actions: [Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: AppImageAsset(
          image: AppAssetsConstants.fssaiImage,
          width: 110,
        ),
      ),],
    );
  }

  Widget _bodyView() {
    return RefreshIndicator(
      onRefresh: () => _helper!.getSuperAdminDashboardData(),
      child: GridView.builder(
        itemCount: _helper!.dashboardCarItems.length,
        padding: const EdgeInsets.all(10),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight(
          height: 115,
          crossAxisCount: 2,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
        ),
        itemBuilder: (context, index) {
          return DashboardCard(
            text: _helper!.dashboardCarItems[index]['title'].toString().tr,
            image: _helper!.dashboardCarItems[index]['icon'],
            onTap: () => _helper!.dashboardOnTap(index),
            value: _helper!.dashboardCarItems[index]['count'].toString(),
          color: _helper!.dashboardCarItems[index]['color'],);
        },
      ),
    );
  }

  Widget _drawerView() {
    return Drawer(
      backgroundColor: AppColorConstants.colorWhite,
      shape: const OutlineInputBorder(borderRadius: BorderRadius.zero, borderSide: BorderSide.none),
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                height: 200,
                color: AppColorConstants.colorAppPrimary,
              ),
              SizedBox(
                height: 200,
                width: double.infinity,
                child: AppImageAsset(
                  image: AppAssetsConstants.splashBgImage,
                  fit: BoxFit.cover,
                ),
              ),
              Positioned(
                top: 40,
                bottom: 10,
                left: 95,
                child: Center(
                  child: AppImageAsset(
                    image: AppAssetsConstants.splashLogo,
                    height: 60,
                    width: 100,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
          Column(
            children:
                List.generate(_helper!.drawerItems.length, (index) => _drawerTile(index, _helper!.drawerItems.length - 1)),
          ),
        ],
      ),
    );
  }

  Widget _drawerTile(int index, int lastIndex) {
    return InkWell(
      onTap: () => _helper!.changeDrawerIndex(index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
        decoration: BoxDecoration(
          color: _helper!.selectedDrawerIndex == index ? AppColorConstants.colorAppPrimary : null,
          borderRadius: BorderRadius.circular(100),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 35,
              child: AppImageAsset(
                image: _helper!.drawerItems[index]['icon'],
                color: (_helper!.selectedDrawerIndex == index)
                    ? AppColorConstants.colorWhite
                    : (index == lastIndex)
                        ? AppColorConstants.colorRed
                        : AppColorConstants.colorDarkGrey,
              ),
            ),
            const SizedBox(width: 10),
            AppText(
              _helper!.drawerItems[index]['title'].toString().tr,
              color: (_helper!.selectedDrawerIndex == index)
                  ? AppColorConstants.colorWhite
                  : (index == lastIndex)
                      ? AppColorConstants.colorRed
                      : AppColorConstants.colorDarkGrey,
            ),
          ],
        ),
      ),
    );
  }
}
