import 'package:ams/app_imports.dart';

class SuperAdminDashboardPageHelper {
  final SuperAdminDashboardPageState _pageState;

  final GlobalKey<ScaffoldState> drawerKey = GlobalKey<ScaffoldState>();

  final List<Map<String, dynamic>> dashboardCarItems = [
    {
      'icon': AppAssetsConstants.icApplicationReceived,
      'color': AppColorConstants.colorBlue,
      'title': AppStringConstants.applicationReceived,
      'count': 0,
    },
    {
      'icon': AppAssetsConstants.icSteeringWheel,
      'color': AppColorConstants.colorYellow,
      'title': AppStringConstants.auditsDone,
      'count': 0,
    },
    {
      'icon': AppAssetsConstants.icThumbUp,
      'color': AppColorConstants.colorLightGreen,
      'title': AppStringConstants.empaneledAgencies,
      'count': 0,
    },
    {
      'icon': AppAssetsConstants.icRejected,
      'color': AppColorConstants.colorLightRed,
      'title': AppStringConstants.rejectedAgencies,
      'count': 0,
    },
    {
      'icon': AppAssetsConstants.icPeople,
      'color': AppColorConstants.colorLightRed,
      'title': AppStringConstants.enlistedAuditors,
      'count': 0,
    },
  ];

  final List<Map<String, dynamic>> drawerItems = [
    {
      'icon': AppAssetsConstants.icSuperAdminDashboard,
      'title': AppStringConstants.dashboard,
    },
    {
      'icon': AppAssetsConstants.icHome,
      'title': AppStringConstants.applicationReceived,
    },
    {
      'icon': AppAssetsConstants.icThumbUp,
      'title': AppStringConstants.empaneledAgencies,
    },
    {
      'icon': AppAssetsConstants.icPeople,
      'title': AppStringConstants.enlistedAuditors,
    },
    {
      'icon': AppAssetsConstants.icSuperAdminReports,
      'title': AppStringConstants.reports,
    },
    {
      'icon': AppAssetsConstants.icChangePassword,
      'title': AppStringConstants.changePassword,
    },
    {
      'icon': AppAssetsConstants.icLogOut,
      'title': AppStringConstants.logOut,
    },
  ];

  int selectedDrawerIndex = 0;
  String username = '';

  ApiStatus apiStatus = ApiStatus.initial;

  SuperAdminDashboardPageHelper(this._pageState) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () {
        getSuperAdminDashboardData();
      },
    );
  }

  Future<void> getSuperAdminDashboardData() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    SuperAdminDashboardModel superAdminDashboardModel = await _pageState.controller.getSuperAdminDashboardData();
    dashboardCarItems[0]['count'] = superAdminDashboardModel.applicationReceived;
    dashboardCarItems[1]['count'] = superAdminDashboardModel.auditsCompleted;
    dashboardCarItems[2]['count'] = superAdminDashboardModel.agency;
    dashboardCarItems[3]['count'] = superAdminDashboardModel.rejected;
    dashboardCarItems[4]['count'] = superAdminDashboardModel.auditor;
    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }

  void changeDrawerIndex(int index) {
    selectedDrawerIndex = index;
    drawerKey.currentState!.closeDrawer();
    switch (index) {
      case 0:
        gotoSuperAdminDashboardPage();
        break;
      case 1:
        Future.delayed(const Duration(milliseconds: 250), () => gotoSuperAdminApplicationReceivedPage());
        break;
      case 2:
        Future.delayed(const Duration(milliseconds: 250), () => gotoSuperAdminEmpanelledAgenciesPage());
        break;
      case 3:
        Future.delayed(const Duration(milliseconds: 250), () => gotoSuperAdminEnlistedAuditorsPage());
        break;
      case 4:
        Future.delayed(const Duration(milliseconds: 250), () => gotoSuperAdminReportsPage());
        break;
      case 5:
        Future.delayed(const Duration(milliseconds: 250), () => gotoChangePasswordPage());
        break;
      case 6:
        Future.delayed(
          (const Duration(milliseconds: 250)),
          () => logoutDialog(context: _pageState.context),
        );
        break;
    }
    _pageState.controller.update();
  }

  void dashboardOnTap(int index) {
    switch (index) {
      case 0:
        gotoSuperAdminApplicationReceivedPage();
        break;
      case 1:
        gotoSuperAdminAuditsDonePage();
        break;
      case 2:
        gotoSuperAdminEmpanelledAgenciesPage();
        break;
      case 3:
        gotoSuperAdminRejectedAgenciesPage();
        break;
      case 4:
        gotoSuperAdminEnlistedAuditorsPage();
        break;
    }
  }
}
