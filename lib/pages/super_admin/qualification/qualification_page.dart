import 'package:ams/app_imports.dart';

class AuditorQualificationPage extends StatefulWidget {
  const AuditorQualificationPage({super.key});

  @override
  State<AuditorQualificationPage> createState() => AuditorQualificationPageState();
}

class AuditorQualificationPageState extends State<AuditorQualificationPage> {
  AuditorQualificationPageHelper? _helper;
  late SuperAdminApplicationReceivedController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? (_helper = AuditorQualificationPageHelper(this));
    return GetBuilder(
      init: SuperAdminApplicationReceivedController(),
      builder: (SuperAdminApplicationReceivedController superAdminApplicationReceivedController) {
        controller = superAdminApplicationReceivedController;
        return Stack(
          children: [
            AppScaffold(
              appBar: _appBarView(),
              body: _bodyView(),
            ),
          ],
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.qualificationDetailsOf.tr);
  }

  Widget _bodyView() {
    return tableCard( children: [
      ListView.separated(
        separatorBuilder: ((context,index) =>  const SizedBox(height: 10,)),
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _helper!.qualificationList.length,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return Table(
            border: TableBorder.all(
              color: AppColorConstants.colorMediumGrey,
              borderRadius: BorderRadius.circular(5),
            ),
            children: [
              for (var entry in _helper!.qualificationList[index].toJson().entries)
                TableRow(
                  children: [
                    TableCell(
                      verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        alignment: Alignment.centerLeft,
                        color: AppColorConstants.colorLightPurple,
                        child: AppText(
                          entry.key.toString().tr,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    TableCell(
                      verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: AppText(
                          entry.value.toString(),
                          fontSize: 12,
                          color: AppColorConstants.colorBlack,
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          );
        },
      ),
    ],);
  }
}
