import 'package:ams/app_imports.dart';

class SuperAdminEnlistedAuditorsPage extends StatefulWidget {
  const SuperAdminEnlistedAuditorsPage({super.key});

  @override
  State<SuperAdminEnlistedAuditorsPage> createState() => SuperAdminEnlistedAuditorsPageState();
}

class SuperAdminEnlistedAuditorsPageState extends State<SuperAdminEnlistedAuditorsPage> {
  SuperAdminEnlistedAuditorsPageHelper? _helper;
  late SuperAdminEnlistedAuditorsController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? SuperAdminEnlistedAuditorsPageHelper(this);
    return GetBuilder<SuperAdminEnlistedAuditorsController>(
      init: SuperAdminEnlistedAuditorsController(),
      builder: (SuperAdminEnlistedAuditorsController superAdminEnlistedAuditorsController) {
        controller = superAdminEnlistedAuditorsController;
        return AppScaffold(
          apiStatus: _helper!.apiStatus,
          appBar: _appBarView(),
          body: _bodyView(),
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.enlistedAuditors.tr);
  }

  Widget _bodyView() {
    return RefreshIndicator(
      onRefresh: () async=> _helper!.getApplicationData(),
      child: tableCard(
        children: [
          if(_helper!.searchList.isEmpty)
            Container(
              height: MediaQuery.of(context).size.height / 1.5,
              padding: const EdgeInsets.all(8.0),
              child: const NoDataFoundView(),
            ),
          for (var item in _helper!.searchList) _tableView(item.toJson()),
        ],
        sortOnTap: _helper!.sortOnTap,
        searchController: _helper!.searchController,
        dropdownValue: _helper!.sortTypeValue,
        dropDownOnChanged: (value)=>_helper!.sortTypeOnChanged(value),
        dropdownList: _helper!.dropdownList,
        isShowDropdown: true,
        isAscending: _helper!.isAscending,
        searchOnChanged: (value) => _helper!.searchOnChange(value),
        isShowSearch: true,
        clearSearchOnTap: () => _helper!.clearValue(),
        isMainListEmpty: _helper!.auditorDetailsList.isEmpty,
      ),
    );
  }

  Widget _tableView(Map<String, dynamic> item) {
    return Padding(
      padding: const EdgeInsets.all(12.0).copyWith(left: 0, right: 0),
      child: Table(
        columnWidths: const {0:FixedColumnWidth(130)},
        border: TableBorder.all(
          color: AppColorConstants.colorMediumGrey,
          borderRadius: BorderRadius.circular(5),
        ),
        children: [
          _commonTextRow(key: AppStringConstants.srNo, value: item[AppStringConstants.srNo].toString()),
          _auditorName(AppStringConstants.auditorName, item[AppStringConstants.auditorName]),
          _commonTextRow(key: AppStringConstants.emailAndMobile, value: item[AppStringConstants.emailAndMobile]),
          _commonTextRow(key: AppStringConstants.agencyName, value: item[AppStringConstants.agencyName]),
          _commonTextRow(
            key: AppStringConstants.auditsConducted,
            value: item[AppStringConstants.auditsConducted],
            onTap: () => gotoSuperAdminConductAuditReportPage(),
          ),
          _commonIconRow(AppStringConstants.experience, AppAssetsConstants.icEye, gotoSuperAdminExperiencePage),
          _commonIconRow(AppStringConstants.qualification, AppAssetsConstants.icForward, gotoSuperAdminQualificationPage),
          _activateDeactivateRow('${AppStringConstants.deActivate.tr} / ${AppStringConstants.activate.tr}',item),
          _documentAttachedRow(
            AppStringConstants.documentAttached,
            item[AppStringConstants.documentAttached],
          ),
        ],
      ),
    );
  }

  TableRow _commonTextRow({required String key, required String value, VoidCallback? onTap}) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: InkWell(
            onTap: () => onTap!(),
            child: Container(
              padding: const EdgeInsets.all(10),
              child: AppText(
                value.toString().tr,
                fontSize: 12,
                color: onTap == null ? AppColorConstants.colorBlack : AppColorConstants.colorBlue,
              ),
            ),
          ),
        ),
      ],
    );
  }

  TableRow _auditorName(String key, String value) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightYellow100,
            padding: const EdgeInsets.all(10),
            child: AppText(
              value.toString(),
              fontSize: 12,
              color: AppColorConstants.colorBlack,
            ),
          ),
        ),
      ],
    );
  }

  TableRow _commonIconRow(String key, String image, VoidCallback onTap) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: InkWell(
            onTap: () => onTap(),
            child: Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.all(10),
              child: AppImageAsset(
                image: image,
              ),
            ),
          ),
        ),
      ],
    );
  }

  TableRow _documentAttachedRow(String key, List<String> value) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () => launchDocumentUrl('https://infotrain.fssai.gov.in/AMS/uploadauditordata/${value[0]}'),
                  child: _underlineText(AppStringConstants.leadAuditorCertificate.tr),
                ),
                const SizedBox(
                  height: 3,
                ),
                InkWell(
                  onTap: () => launchDocumentUrl('https://infotrain.fssai.gov.in/AMS/uploadauditordata/${value[1]}'),
                  child: _underlineText(AppStringConstants.educationalQualificationCertificate.tr,),
                ),
                const SizedBox(
                  height: 3,
                ),
                InkWell(
                  onTap: () => launchDocumentUrl('https://infotrain.fssai.gov.in/AMS/uploadauditordata/${value[2]}'),
                  child: _underlineText(AppStringConstants.auditorLog.tr,),
                ),
                const SizedBox(
                  height: 3,
                ),
                InkWell(
                  onTap: () => launchDocumentUrl('https://infotrain.fssai.gov.in/AMS/uploadauditordata/${value[3]}'),
                  child: _underlineText(AppStringConstants.sectorSpecificKnowledge.tr,),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  TableRow _activateDeactivateRow(String key,Map<String,dynamic> item) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          child: Container(
            padding: const EdgeInsets.fromLTRB(10, 12, 40, 10),
            child:  AppButton(
              onTap: () => _agencyActivationDialogue(item),
              title: item[AppStringConstants.status] == 'De-Activate'
                  ? AppStringConstants.activate.tr
                  : AppStringConstants.deActivate.tr,
              backColor:  item[AppStringConstants.status] == 'De-Activate'
                  ? AppColorConstants.colorLightGreen
                  : AppColorConstants.colorRed,
              borderRadius: BorderRadius.circular(5),
              textSize: 12,
              titleColor:  AppColorConstants.colorWhite,
              height: 30,
              border: Border.all(
                width: 0.5,
                color:  item[AppStringConstants.status] == 'De-Activate'
                    ? AppColorConstants.colorLightGreen
                    : AppColorConstants.colorRed,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future _agencyActivationDialogue(Map<String,dynamic> item) {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          backgroundColor: AppColorConstants.colorWhite,
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                AppStringConstants.infoTrainFssaiGovSays.tr,
              ),
              const SizedBox(
                height: 5,
              ),
              AppText(
                fontWeight: FontWeight.w400,
                fontSize: 14,
                item[AppStringConstants.status] == 'De-Activate'
                    ? AppStringConstants.agencyActivateString.tr
                    : AppStringConstants.agencyDeActivateString.tr,
              ),
              const SizedBox(
                height: 50,
              ),
              Row(
                children: [
                  Expanded(
                    child: AppButton(
                      height: 35,
                      title: AppStringConstants.ok.tr,
                      onTap: () => _helper!.agencyActivateDeactivateTap(item),
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: AppButton(
                      backColor: AppColorConstants.colorLightPurple,
                      titleColor: AppColorConstants.colorAppPrimary,
                      border: Border.all(color: AppColorConstants.colorAppPrimary, width: 1),
                      height: 35,
                      title: AppStringConstants.cancel.tr,
                      onTap: () => gotoBack(),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
  
  Widget _underlineText(String label){
    return Text(
      label,
      style: const TextStyle(
        fontSize: 12,
        shadows: [Shadow(color: AppColorConstants.colorBlue, offset: Offset(0, -1.5))],
        color: AppColorConstants.colorTransparent,
        decoration: TextDecoration.underline,
        decorationColor: Colors.blue,
        decorationThickness: 1,
      ),
      
    );
  }
}
