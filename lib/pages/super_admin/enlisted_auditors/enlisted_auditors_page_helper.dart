import 'package:ams/app_imports.dart';

class SuperAdminEnlistedAuditorsPageHelper {
  final SuperAdminEnlistedAuditorsPageState _pageState;
  TextEditingController searchController = TextEditingController();
  TextEditingController remarkController = TextEditingController();
  int srNo = 1;
  ApiStatus apiStatus = ApiStatus.initial;
  String sortTypeValue = AppStringConstants.srNo;
  bool isAscending = true;
  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.auditorName,
    AppStringConstants.emailAndMobile,
    AppStringConstants.agencyName,
    AppStringConstants.auditsConducted,
    AppStringConstants.experience,
    AppStringConstants.qualification,
  ];
  List<SuperAdminAuditorDetailsViewModel> auditorDetailsList = [];
  List<SuperAdminEnlistedAuditorsModel> _data = [];
  List<SuperAdminAuditorDetailsViewModel> searchList = [];

  Future<void> getApplicationData() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    setDefaultValue();
    _data = await _pageState.controller.getEnlistedAuditors();
    auditorDetailsList.clear();
    _initializeAuditorList();
    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }

  void setDefaultValue(){
    sortTypeValue = AppStringConstants.srNo;
    searchController.clear();
    isAscending = true;
  }

  void _initializeAuditorList(){
    for (int i = 0; i < (_data.length); i++) {
      SuperAdminEnlistedAuditorsModel model = _data[i];
      auditorDetailsList.add(
        SuperAdminAuditorDetailsViewModel(
          srNo: srNo,
          auditsConducted: model.nooFAudits.toString(),
          experience: model.expertise,
          auditorName: model.auditorName,
          agencyName: model.agencyName,
          emailAndMobile: '${model.mobile}\n${model.email}',
          qualification: model.address,
          auditorStatus: 'Activate',
          documentAttached: [model.leadAuditorFile,model.qualificationFile,model.auditorLogFile,model.sectorsPecificFile],
        ),
      );
      srNo++;
    }
    searchList = auditorDetailsList;
    _pageState.controller.update();
  }

  SuperAdminEnlistedAuditorsPageHelper(this._pageState) {
    Future.delayed(const Duration(milliseconds: 10),()=> getApplicationData());
  }

  void searchOnChange(String text) {
    searchList = auditorDetailsList.where((element) {
      return ((element.auditorStatus.toString().toLowerCase().contains(text)) ||
          (element.qualification.toString().toLowerCase().contains(text)) ||
          (element.experience.toString().toLowerCase().contains(text)) ||
          (element.srNo.toString().toLowerCase().contains(text)) ||
          (element.agencyName.toString().toLowerCase().contains(text)) ||
          (element.emailAndMobile.toString().toLowerCase().contains(text)) ||
          (element.auditorName.toString().toLowerCase().contains(text)) ||
          (element.auditsConducted.toString().toLowerCase().contains(text)));
    }).toList();
    _pageState.controller.update();
  }

  void clearValue() {
    FocusScope.of(_pageState.context).unfocus();
    searchController.clear();
    searchList = auditorDetailsList;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      searchList.sort(
        (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
      );
    } else {
      searchList.sort(
        (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
      );
    }
    _pageState.controller.update();
  }

  void agencyActivateDeactivateTap(Map<String,dynamic> item){
    if(item [AppStringConstants.status] == 'De-Activate'){
      item [AppStringConstants.status] = 'Activate';
    }else if(item [AppStringConstants.status] == 'Activate'){
      item [AppStringConstants.status] = 'De-Activate';
    }
    gotoBack();
    _pageState.controller.update();
  }
}
