import 'package:ams/app_imports.dart';

class SuperAdminEmpanelledAgenciesPage extends StatefulWidget {
  const SuperAdminEmpanelledAgenciesPage({super.key});

  @override
  State<SuperAdminEmpanelledAgenciesPage> createState() => SuperAdminEmpanelledAgenciesPageState();
}

class SuperAdminEmpanelledAgenciesPageState extends State<SuperAdminEmpanelledAgenciesPage> {
  SuperAdminEmpanelledAgenciesPageHelper? _helper;
  late SuperAdminEmpanelledAgenciesController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? SuperAdminEmpanelledAgenciesPageHelper(this);
    return GetBuilder<SuperAdminEmpanelledAgenciesController>(
      init: SuperAdminEmpanelledAgenciesController(),
      builder: (SuperAdminEmpanelledAgenciesController superAdminEmpanelledAgenciesController) {
        controller = superAdminEmpanelledAgenciesController;
        return AppScaffold(
          apiStatus: _helper!.apiStatus,
          appBar: CommonAppBar(
            backOnTap: () => gotoBack(),
            title: AppStringConstants.approvedEmpanelledAgencies.tr,
            titleSize: 20,
            titleWright: FontWeight.w500,
          ),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return RefreshIndicator(
      onRefresh: () => _helper!.getEmpanelledAgencyData(),
      child: tableCard(
        isAscending: _helper!.isAscending,
        dropdownValue: _helper!.sortTypeValue.isEmpty ? null : _helper!.sortTypeValue,
        dropDownOnChanged: (value) => _helper!.sortTypeOnChanged(value as String),
        searchOnChanged: (value) => _helper!.searchOnChange(value),
        clearSearchOnTap: () => _helper!.clearValue(),
        dropdownList: _helper!.dropDownList,
        isShowSearch: true,
        isMainListEmpty: _helper!.agencyList.isEmpty,
        searchController: _helper!.searchController,
        isShowDropdown: true,
        sortOnTap: () => _helper!.sortOnTap(),
        children: [
          if (_helper!.searchList.isEmpty)
            Container(
              height: MediaQuery.of(context).size.height / 1.5,
              padding: const EdgeInsets.all(8.0),
              child: const NoDataFoundView(),
            ),
          const SizedBox(height: 10),
          for (var element in _helper!.searchList) tableView(element),
        ],
      ),
    );
  }

  Widget tableView(SuperAdminEmpanelledAgencyModel model) {
    Map<String, dynamic> agencyDetails = model.toJson();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Table(
        border: TableBorder.all(
          color: AppColorConstants.colorMediumGrey,
          borderRadius: BorderRadius.circular(5),
        ),
        children: [
          for (var entry in agencyDetails.entries)
            TableRow(
              children: [
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    alignment: Alignment.centerLeft,
                    color: AppColorConstants.colorLightPurple,
                    child: AppText(
                      entry.key.tr,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
                (entry.key == AppStringConstants.actions)
                    ? TableCell(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppButton(
                                onTap: () => _agencyActivationDialogue(model),
                                title: model.agencyStatus == AppStringConstants.deActive
                                    ? AppStringConstants.activate.tr
                                    : AppStringConstants.deActivate.tr,
                                backColor: model.agencyStatus == AppStringConstants.deActive
                                    ? AppColorConstants.colorLightGreen
                                    : AppColorConstants.colorRed,
                                borderRadius: BorderRadius.circular(5),
                                textSize: 12,
                                titleColor: AppColorConstants.colorWhite,
                                width: 90,
                                height: 30,
                              ),
                              const SizedBox(height: 8),
                              AppButton(
                                onTap: () => _agencyUpdateDialogue(model),
                                title: AppStringConstants.edit.tr,
                                borderRadius: BorderRadius.circular(5),
                                textSize: 12,
                                backColor: AppColorConstants.colorAppPrimary,
                                titleColor: AppColorConstants.colorWhite,
                                width: 50,
                                height: 30,
                              ),
                            ],
                          ),
                        ),
                      )
                    : (entry.key == AppStringConstants.status)
                        ? TableCell(
                            verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                            child: Container(
                              padding: const EdgeInsets.all(10),
                              child: AppText(
                                entry.value.toString(),
                                fontSize: 12,
                                color: AppColorConstants.colorLightGreen,
                              ),
                            ),
                          )
                        : (entry.key == AppStringConstants.viewDetail)
                            ? TableCell(
                                verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                child: InkWell(
                                  onTap: () => gotoSuperAdminAgencyDetailPage(agencyId: model.agencyId),
                                  child: Container(
                                    alignment: Alignment.centerLeft,
                                    padding: const EdgeInsets.symmetric(horizontal: 10),
                                    child: AppImageAsset(
                                      image: AppAssetsConstants.icEye,
                                      height: 12,
                                    ),
                                  ),
                                ),
                              )
                            : (entry.key == AppStringConstants.auditors)
                                ? TableCell(
                                    verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                    child: InkWell(
                                      onTap: () => gotoSuperAdminViewAuditorsPage(model.agencyId),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 10),
                                        alignment: Alignment.centerLeft,
                                        child: AppText(
                                          entry.value.toString(),
                                          fontSize: 12,
                                          color: AppColorConstants.colorBlue,
                                        ),
                                      ),
                                    ),
                                  )
                                : (entry.key == AppStringConstants.audits)
                                    ? TableCell(
                                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                        child: InkWell(
                                          onTap: () => gotoSuperAdminAuditConductByAuditorPage(audits: []),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 10),
                                            alignment: Alignment.centerLeft,
                                            child: AppText(
                                              entry.value.toString(),
                                              fontSize: 12,
                                              color: AppColorConstants.colorBlue,
                                            ),
                                          ),
                                        ),
                                      )
                                    : TableCell(
                                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                        child: Container(
                                          padding: const EdgeInsets.all(10),
                                          child: AppText(
                                            (entry.key == AppStringConstants.date)
                                                ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                                : entry.value.toString(),
                                            fontSize: 12,
                                            color: AppColorConstants.colorBlack,
                                          ),
                                        ),
                                      ),
              ],
            ),
        ],
      ),
    );
  }

  Future _agencyActivationDialogue(SuperAdminEmpanelledAgencyModel model) {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          backgroundColor: AppColorConstants.colorWhite,
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                AppStringConstants.infoTrainFssaiGovSays.tr,
              ),
              const SizedBox(
                height: 5,
              ),
              AppText(
                fontWeight: FontWeight.w400,
                fontSize: 14,
                model.agencyStatus == AppStringConstants.deActive
                    ? AppStringConstants.agencyActivateString.tr
                    : AppStringConstants.agencyDeActivateString.tr,
              ),
              const SizedBox(
                height: 50,
              ),
              Row(
                children: [
                  Expanded(
                    child: AppButton(
                      height: 35,
                      title: AppStringConstants.ok.tr,
                      onTap: () => _helper!.activateDeactivateAgency(model.agencyId!,model.agencyStatus!),
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: AppButton(
                      backColor: AppColorConstants.colorLightPurple,
                      titleColor: AppColorConstants.colorAppPrimary,
                      border: Border.all(color: AppColorConstants.colorAppPrimary, width: 1),
                      height: 35,
                      title: AppStringConstants.cancel.tr,
                      onTap: () => gotoBack(),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Future _agencyUpdateDialogue(SuperAdminEmpanelledAgencyModel model) {
    _helper!.initializedField(model);
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.all(10),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          backgroundColor: AppColorConstants.colorWhite,
          title: Center(
            child: AppText(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              AppStringConstants.updateAgencyDetails.tr.toUpperCase(),
              color: AppColorConstants.colorAppPrimary,
            ),
          ),
          content: StatefulBuilder(
            builder: (context, setState) {
              return SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText(
                      '${AppStringConstants.agencyName.tr} :',
                      color: AppColorConstants.colorAppPrimary,
                    ),
                    const SizedBox(
                      height: 3,
                    ),
                    AppTextFormField(
                      textFieldFontSize: 12,
                      controller: _helper!.agencyNameController,
                      hintText: AppStringConstants.agencyName.tr,
                      textFieldHeight: 30,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    AppText(
                      '${AppStringConstants.state.tr} :',
                      color: AppColorConstants.colorAppPrimary,
                    ),
                    const SizedBox(
                      height: 3,
                    ),
                    AppDropdownButton(
                      buttonHeight: 35,
                      hint: AppStringConstants.selectState.tr,
                      value: _helper!.selectedState,
                      onChanged: (value) => setState(() => _helper!.changeStateDropDownValue(dropdownValue: value as StateModel)),
                      itemBuilder: _helper!.stateList.map(
                        (item) {
                          return DropdownMenuItem(
                            value: item,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10),
                              width: double.infinity,
                              alignment: Alignment.centerLeft,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: (item == _helper!.stateList.last)
                                      ? BorderSide.none
                                      : const BorderSide(color: AppColorConstants.colorGrey, width: 0.5),
                                ),
                              ),
                              child: AppText(
                                item.stateName ?? '',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                                fontWeight: FontWeight.w400,
                                color: AppColorConstants.colorBlack.withOpacity(0.5),
                              ),
                            ),
                          );
                        },
                      ).toList(),
                      selectedItemBuilder: (context) {
                        return _helper!.stateList
                            .map(
                              (e) => Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: AppText(
                                e.stateName ?? '',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        )
                            .toList();
                      },
                      selectedMenuItemBuilder: (context, child) {
                        return Container(
                          height: 40,
                          width: double.infinity,
                          color: AppColorConstants.colorAppPrimary,
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: AppText(
                              _helper!.selectedState?.stateName ?? '',
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              fontWeight: FontWeight.w400,
                              color: AppColorConstants.colorWhite,
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    AppText(
                      '${AppStringConstants.address.tr} :',
                      color: AppColorConstants.colorAppPrimary,
                    ),
                    const SizedBox(
                      height: 3,
                    ),
                    AppTextFormField(
                      textFieldFontSize: 12,
                      maxLines: 5,
                      controller: _helper!.addressController,
                      hintText: AppStringConstants.address.tr,
                      textFieldHeight: 70,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    AppText(
                      '${AppStringConstants.mobileNo.tr} :',
                      color: AppColorConstants.colorAppPrimary,
                    ),
                    const SizedBox(
                      height: 3,
                    ),
                    AppTextFormField(
                      textFieldFontSize: 12,
                      controller: _helper!.mobileController,
                      hintText: AppStringConstants.mobileNo.tr,
                      textFieldHeight: 30,
                      readOnly: true,
                      textFieldColor: AppColorConstants.colorLightGrey100,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      maxLength: 10,
                      keyboardType: TextInputType.phone,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    AppText(
                      '${AppStringConstants.scopeOfAudition.tr} :',
                      color: AppColorConstants.colorAppPrimary,
                    ),
                    const SizedBox(
                      height: 3,
                    ),
                    AppTextFormField(
                      textFieldFontSize: 12,
                      maxLines: 5,
                      controller: _helper!.scopeController,
                      hintText: AppStringConstants.scopeOfAudition.tr,
                      textFieldHeight: 70,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    AppText(
                      '${AppStringConstants.geographicalArea.tr} :',
                      color: AppColorConstants.colorAppPrimary,
                    ),
                    const SizedBox(
                      height: 3,
                    ),
                    AppTextFormField(
                      textFieldFontSize: 12,
                      maxLines: 5,
                      controller: _helper!.areaController,
                      hintText: AppStringConstants.geographicalArea.tr,
                      textFieldHeight: 70,
                    ),
                  ],
                ),
              );
            },
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: AppButton(
                    height: 35,
                    title: AppStringConstants.update.tr,
                    onTap: () => _helper!.updateEmpanelledAgency(model),
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: AppButton(
                    height: 35,
                    title: AppStringConstants.close.tr,
                    onTap: () => gotoBack(),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
