import 'package:ams/app_imports.dart';

class SuperAdminEmpanelledAgenciesPageHelper {
  final SuperAdminEmpanelledAgenciesPageState _pageState;

  String sortTypeValue = AppStringConstants.srNo;
  bool isAscending = false;
  StateModel? selectedState;

  final TextEditingController agencyNameController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController scopeController = TextEditingController();
  final TextEditingController areaController = TextEditingController();
  final TextEditingController searchController = TextEditingController();

  List<String> dropDownList = [
    AppStringConstants.srNo,
    AppStringConstants.agencyName,
    AppStringConstants.state,
    AppStringConstants.address,
    AppStringConstants.mobile,
    AppStringConstants.status,
    AppStringConstants.remarks,
    AppStringConstants.auditors,
    AppStringConstants.audits,
    AppStringConstants.agencyStatus,
    AppStringConstants.auditingScope,
    AppStringConstants.geographicalArea,
  ];

  List<StateModel> stateList = [];

  List<SuperAdminEmpanelledAgencyModel> agencyList = [];

  List<SuperAdminEmpanelledAgencyModel> searchList = [];

  ApiStatus apiStatus = ApiStatus.initial;

  List<SuperAdminEmpanelledAgenciesModel> empanelledAgencyList = [];

  SuperAdminEmpanelledAgenciesPageHelper(this._pageState) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () async {
        await getStateListData();
        await getEmpanelledAgencyData();
      },
    );
  }

  Future<void> getEmpanelledAgencyData() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    setDefaultValue();
    empanelledAgencyList = await _pageState.controller.getEmpaneledAgencyData();
    agencyList.clear();
    _initializeData();
    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }

  void _initializeData() {
    for (int i = 0; i < (empanelledAgencyList.length); i++) {
      SuperAdminEmpanelledAgenciesModel model = empanelledAgencyList[i];
      agencyList.add(
        SuperAdminEmpanelledAgencyModel(
          agencyId: model.agencyId,
          srNo: i + 1,
          agencyName: model.agencyName,
          address: model.address,
          remarks: model.remarks,
          mobile: model.mobile,
          auditors: model.nooFAuditors,
          audits: model.nooFAudits,
          state: model.stateName,
          status: AppStringConstants.approvedStatus,
          geoGraphicalArea: model.geographicalArea,
          agencyStatus: model.activeFlag,
          auditingScope: model.auditingScope,
        ),
      );
    }
    searchList = agencyList;
    _pageState.controller.update();
  }

  Future<void> getStateListData() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    stateList = await _pageState.controller.getStateListData();
    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }

  void setDefaultValue() {
    sortTypeValue = AppStringConstants.srNo;
    searchController.clear();
    isAscending = true;
  }

  void clearValue() {
    FocusScope.of(_pageState.context).unfocus();
    searchController.clear();
    searchList = agencyList;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }

  void changeDropDownValue({required String dropdownValue}) {
    sortTypeValue = dropdownValue;
    _pageState.controller.update();
  }

  void changeStateDropDownValue({required StateModel dropdownValue}) {
    selectedState = dropdownValue;
    _pageState.controller.update();
  }

  void changeSortingMethod() {
    isAscending = !isAscending;
    _pageState.controller.update();
  }


  void initializedField(SuperAdminEmpanelledAgencyModel model) {
    agencyNameController.text = model.agencyName ?? '';
    addressController.text = model.address ?? '';
    mobileController.text = model.mobile ?? '';
    scopeController.text = model.auditingScope ?? '';
    areaController.text = model.geoGraphicalArea ?? '';
    if (model.state?.isNotEmpty ?? false) {
      selectedState = stateList.firstWhere((element) => element.stateName == model.state);
    }
    _pageState.controller.update();
  }

  void searchOnChange(String value) {
    searchList = agencyList.where((element) {
      return ((element.srNo.toString().toLowerCase().contains(value)) ||
          (element.state.toString().toLowerCase().contains(value)) ||
          (element.agencyName.toString().toLowerCase().contains(value)) ||
          (element.mobile.toString().toLowerCase().contains(value)) ||
          (element.address.toString().toLowerCase().contains(value)) ||
          (element.auditors.toString().toLowerCase().contains(value)) ||
          (element.remarks.toString().toLowerCase().contains(value)));
    }).toList();
    _pageState.controller.update();
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      searchList.sort(
        (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
      );
    } else {
      searchList.sort(
        (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
      );
    }
    _pageState.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }

  Future<void> updateEmpanelledAgency(SuperAdminEmpanelledAgencyModel model) async {
    gotoBack();
    if (model.agencyId != null) {
      apiStatus = ApiStatus.loading;
      _pageState.controller.update();
      bool isSuccess = await _pageState.controller.updateEmpanelledAgency(
        agencyId: model.agencyId,
        agencyName: agencyNameController.text,
        mobileNumber: mobileController.text,
        geographicalArea: areaController.text,
        scopeOfAuditing: scopeController.text,
      );
      if (isSuccess) {
        AppStringConstants.detailsUpdated.tr.showSuccessToast();
       await getEmpanelledAgencyData();
      }
      apiStatus = ApiStatus.success;
      _pageState.controller.update();
    }
  }

  Future<void> activateDeactivateAgency(int agencyId,String agencyStatus) async {
    gotoBack();
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    String status = (agencyStatus == 'Active')
        ? 'Deactive'
        : (agencyStatus == 'Deactive')
        ? 'Active'
        : agencyStatus;
    bool isSuccess = await _pageState.controller
        .activateDeactivateAgency(agencyId: agencyId, status: status);
    if(isSuccess){
      await getEmpanelledAgencyData();
    }
    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }
}
