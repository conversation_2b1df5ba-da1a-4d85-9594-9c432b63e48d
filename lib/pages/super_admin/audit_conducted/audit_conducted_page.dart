import 'package:ams/app_imports.dart';

class ConductAuditReportPage extends StatefulWidget {
  const ConductAuditReportPage({super.key});

  @override
  State<ConductAuditReportPage> createState() => ConductAuditReportPageState();
}

class ConductAuditReportPageState extends State<ConductAuditReportPage> {
  ConductAuditReportPageHelper? _helper;
  late SuperAdminApplicationReceivedController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? (ConductAuditReportPageHelper(this));
    return GetBuilder(
      init: SuperAdminApplicationReceivedController(),
      builder: (SuperAdminApplicationReceivedController superAdminApplicationReceivedController) {
        controller = superAdminApplicationReceivedController;
        return Stack(
          children: [
            AppScaffold(
              appBar: _appBarView(),
              body: _bodyView(),
            ),
          ],
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.auditsConducted.tr);
  }

  Widget _bodyView() {
    return tableCard(
      isShowSearch: true,
      isShowDropdown: true,
      isAscending: _helper!.isAscending,
      sortOnTap: () => _helper!.sortOnTap(),
      clearSearchOnTap: ()=>_helper!.clearValue(),
      dropDownOnChanged: (value)=>_helper!.sortTypeOnChanged(value),
      dropdownList: _helper!.dropdownList,
      dropdownValue: _helper!.sortTypeValue,
      searchController: _helper!.searchController,
      searchOnChanged: _helper!.searchOnChange,
      children: [
        _tableView(),
      ],
    );
  }

  Widget _tableView() {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _helper!.searchList.length,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5.0,vertical: 10),
          child: Table(
            border: TableBorder.all(
              color: AppColorConstants.colorMediumGrey,
              borderRadius: BorderRadius.circular(5),
            ),
            children: [
              for (var entry in _helper!.searchList[index].toJson().entries)
                TableRow(
                  children: [
                    TableCell(
                      verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        alignment: Alignment.centerLeft,
                        color: AppColorConstants.colorLightPurple,
                        child: AppText(
                          entry.key.toString().tr,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    entry.key == AppStringConstants.auditReport
                        ? TableCell(
                            verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                            child: InkWell(
                              onTap: () => launchDocumentUrl(''),
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                alignment: Alignment.topLeft,
                                child: AppImageAsset(
                                  image: AppAssetsConstants.icDownloadArrow,
                                ),
                              ),
                            ),
                          )
                        : TableCell(
                            verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: AppText(
                                entry.value.toString(),
                                fontSize: 12,
                                color: AppColorConstants.colorBlack,
                              ),
                            ),
                          ),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }
}
