import 'package:ams/app_imports.dart';


class ConductAuditReportPageHelper {
  final ConductAuditReportPageState _state;
  String sortTypeValue = AppStringConstants.srNo;
  bool isAscending = false;
  TextEditingController searchController = TextEditingController();
  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.licenseNo,
    AppStringConstants.fboName,
    AppStringConstants.state,
    AppStringConstants.district,
    AppStringConstants.agency,
    AppStringConstants.auditEndDate,
    AppStringConstants.auditStartDate,
  ];
  List<ConductAuditDetails> conductedAuditsDetails = [
    ConductAuditDetails(
      srNo: 1,
      licenseNo: '1223',
      fboName: 'tr',
      state: 'gujrat',
      agency: 'demo',
      auditEndDate: 'f26-05-2024',
      auditReport: '04-06-2024',
      auditStartDate: 'hello',
      district: 'surat',
    ),
    ConductAuditDetails(
      srNo: 2,
      licenseNo: '1223',
      fboName: 'tr',
      state: 'gujrat',
      agency: 'demo',
      auditEndDate: 'f26-05-2024',
      auditReport: '04-06-2024',
      auditStartDate: 'hello',
      district: 'surat',
    ),
    ConductAuditDetails(
      srNo: 3,
      licenseNo: '1223',
      fboName: 'tr',
      state: 'gujrat',
      agency: 'newdemo',
      auditEndDate: 'f26-05-2024',
      auditReport: '04-06-2024',
      auditStartDate: 'ghelo',
      district: 'surat',
    ),
  ];
  List<ConductAuditDetails> searchList = [];
  ConductAuditReportPageHelper(this._state) {
    searchList = conductedAuditsDetails;
  }

  void searchOnChange(String value) {
    searchList = conductedAuditsDetails.where((element) {
      return ((element.srNo.toString().toLowerCase().contains(value)) ||
          (element.state.toString().toLowerCase().contains(value)) ||
          (element.licenseNo.toString().toLowerCase().contains(value)) ||
          (element.fboName.toString().toLowerCase().contains(value)) ||
          (element.district.toString().toLowerCase().contains(value)) ||
          (element.agency.toString().toLowerCase().contains(value)) ||
          (element.auditStartDate.toString().toLowerCase().contains(value)) ||
          (element.auditEndDate.toString().toLowerCase().contains(value)));
    }).toList();
    _state.controller.update();
  }

  void clearValue() {
    FocusScope.of(_state.context).unfocus();
    searchController.clear();
    searchList = conductedAuditsDetails;
    _state.controller.update();
  }


  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _state.controller.update();
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      if (sortTypeValue == AppStringConstants.auditStartDate) {
        conductedAuditsDetails.sort(
              (a, b) => a.auditStartDate!.compareTo(b.auditStartDate ?? DateTime.now().toString()),
        );
      } else if (sortTypeValue == AppStringConstants.auditEndDate){
        conductedAuditsDetails.sort(
              (a, b) => a.auditStartDate!.compareTo(b.auditStartDate ?? DateTime.now().toString()),
        );
      }else {
        conductedAuditsDetails.sort(
              (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
        );
      }
    } else {
      if (sortTypeValue == AppStringConstants.auditStartDate) {
        conductedAuditsDetails.sort(
              (a, b) => b.auditStartDate!.compareTo(a.auditStartDate ?? DateTime.now().toString()),
        );
      } else if (sortTypeValue == AppStringConstants.auditEndDate){
        conductedAuditsDetails.sort(
              (a, b) => b.auditStartDate!.compareTo(a.auditStartDate ?? DateTime.now().toString()),
        );
      } else {
        conductedAuditsDetails.sort(
              (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
        );
      }
    }
    _state.controller.update();
  }
}
