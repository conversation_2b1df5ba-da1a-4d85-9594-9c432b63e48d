import 'package:ams/app_imports.dart';

class SuperAdminRejectedAgenciesPageHelper {
  final SuperAdminRejectedAgenciesPageState _pageState;

  String sortTypeValue = AppStringConstants.srNo;

  TextEditingController searchController = TextEditingController();
  TextEditingController remarkController = TextEditingController();

  List<String> dropDownList = [
    AppStringConstants.srNo,
    AppStringConstants.agencyName,
    AppStringConstants.state,
    AppStringConstants.address,
    AppStringConstants.mobile,
    AppStringConstants.status,
    AppStringConstants.remarks,
    AppStringConstants.auditors,
    AppStringConstants.audits,
  ];

  List<String> verificationDropDownList = [
    AppStringConstants.approve,
    AppStringConstants.reject,
  ];
  List<SuperAdminApplicationReceivedViewModel> searchList = [];
  List<SuperAdminApplicationReceivedViewModel> tableData = [];

  List<SuperAdminApplicationReceivedModel> data = [];

  dynamic approveRejectValue;
  bool isAscending = true;

  ApiStatus apiStatus = ApiStatus.initial;

  SuperAdminRejectedAgenciesPageHelper(this._pageState) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () => getRejectAgencyData(),
    );
  }

  Future<void> getRejectAgencyData() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    setDefaultValue();
    data = await _pageState.controller.getRejectedAgencyData();
    tableData.clear();
    _initializeData();

    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }

  void setDefaultValue() {
    sortTypeValue = AppStringConstants.srNo;
    searchController.clear();
    isAscending = true;
  }

  void clearValue() {
    FocusScope.of(_pageState.context).unfocus();
    searchController.clear();
    searchList = tableData;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }

  void _initializeData() {
    for (int i = 0; i < (data.length); i++) {
      SuperAdminApplicationReceivedModel model = data[i];
      tableData.add(
        SuperAdminApplicationReceivedViewModel(
          agencyId: model.agencyId,
          srNo: i + 1,
          agencyName: model.agencyName,
          address: model.address,
          remarks: model.remarks,
          mobile: model.mobile,
          auditors: model.nooFAuditors,
          audits: model.nooFAudits,
          state: model.stateName,
          status: AppStringConstants.rejectedStatus,
        ),
      );
    }
    searchList = tableData;
    _pageState.controller.update();
  }

  void changeVerificationDropDownValue({required String dropdownValue}) {
    approveRejectValue = dropdownValue;
    _pageState.controller.update();
  }

  void searchOnChange(String value) {
    searchList = tableData.where((element) {
      return ((element.srNo.toString().toLowerCase().contains(value)) ||
          (element.state.toString().toLowerCase().contains(value)) ||
          (element.agencyName.toString().toLowerCase().contains(value)) ||
          (element.mobile.toString().toLowerCase().contains(value)) ||
          (element.address.toString().toLowerCase().contains(value)) ||
          (element.auditors.toString().toLowerCase().contains(value)) ||
          (element.remarks.toString().toLowerCase().contains(value)));
    }).toList();
    _pageState.controller.update();
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      searchList.sort(
        (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
      );
    } else {
      searchList.sort(
        (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
      );
    }
    _pageState.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }
}
