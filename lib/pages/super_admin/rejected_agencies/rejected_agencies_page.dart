import 'package:ams/app_imports.dart';

class SuperAdminRejectedAgenciesPage extends StatefulWidget {
  const SuperAdminRejectedAgenciesPage({super.key});

  @override
  State<SuperAdminRejectedAgenciesPage> createState() => SuperAdminRejectedAgenciesPageState();
}

class SuperAdminRejectedAgenciesPageState extends State<SuperAdminRejectedAgenciesPage> {
  SuperAdminRejectedAgenciesPageHelper? _helper;
  late SuperAdminRejectedAgenciesController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? SuperAdminRejectedAgenciesPageHelper(this);
    return GetBuilder(
      init: SuperAdminRejectedAgenciesController(),
      builder: (SuperAdminRejectedAgenciesController superAdminRejectedAgenciesController) {
        controller = superAdminRejectedAgenciesController;
        return AppScaffold(
          apiStatus: _helper!.apiStatus,
          appBar: _appBarView(),
          body: _bodyView(),
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(
      title: AppStringConstants.rejectedAgencies.tr,
      titleSize: 18,
    );
  }

  Widget _bodyView() {

    return RefreshIndicator(
      onRefresh: () => _helper!.getRejectAgencyData(),
      child: tableCard(
        sortOnTap: () => _helper!.sortOnTap(),
        dropDownOnChanged: (value) => _helper!.sortTypeOnChanged(value),
        dropdownList: _helper!.dropDownList,
        dropdownValue: _helper!.sortTypeValue.isNotEmpty ? _helper!.sortTypeValue : null,
        searchController: _helper!.searchController,
        isShowDropdown: true,
        isAscending: _helper!.isAscending,
        clearSearchOnTap: () => _helper!.clearValue(),
        isShowSearch: true,
        children: [
          if (_helper!.searchList.isEmpty)
            Container(
              height: MediaQuery.of(context).size.height / 1.5,
              padding: const EdgeInsets.all(8.0),
              child: const NoDataFoundView(),
            ),
          for (var entry in _helper!.searchList) _applicationTable(entry),
        ],
      ),
    );
  }

  Widget _applicationTable(SuperAdminApplicationReceivedViewModel model) {
    Map<String, dynamic> agencyDetails = model.toJson();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
      child: Table(
        border: TableBorder.all(
          color: AppColorConstants.colorMediumGrey,
          borderRadius: BorderRadius.circular(5),
        ),
        children: [
          for (var entry in agencyDetails.entries)
            TableRow(
              children: [
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    alignment: Alignment.centerLeft,
                    color: AppColorConstants.colorLightPurple,
                    child: AppText(
                      entry.key.toString().tr,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                  child: InkWell(
                    onTap: () {
                      if (entry.key == AppStringConstants.auditors) {
                        gotoSuperAdminViewAuditorsPage(model.agencyId ?? 0);
                      } else if (entry.key == AppStringConstants.audits) {
                        gotoSuperAdminAuditConductByAuditorPage(audits: []);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      color: AppColorConstants.colorWhite,
                      alignment: Alignment.centerLeft,
                      child: entry.key == AppStringConstants.viewDetails
                          ? InkWell(
                              onTap: () => gotoSuperAdminAgencyDetailPage(agencyId: model.agencyId),
                              child: AppImageAsset(
                                image: AppAssetsConstants.icEye,
                                height: 12,
                              ),
                            )
                          : entry.key == AppStringConstants.verifyReject
                              ? InkWell(
                                  onTap: () => _agencyVerificationDialogue(),
                                  child: AppImageAsset(
                                    image: AppAssetsConstants.icEdit,
                                    height: 18,
                                  ),
                                )
                              : entry.key == AppStringConstants.status
                                  ? AppText(
                                      entry.value.toString(),
                                      fontSize: 12,
                                      color: entry.value == AppStringConstants.rejectedStatus
                                          ? AppColorConstants.colorRed
                                          : AppColorConstants.colorLightGreen,
                                    )
                                  : AppText(
                                      entry.value.toString(),
                                      fontSize: 12,
                                      color: (entry.key == AppStringConstants.auditors || entry.key == AppStringConstants.audits)
                                          ? AppColorConstants.colorBlue
                                          : AppColorConstants.colorBlack,
                                    ),
                    ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  void _agencyVerificationDialogue() async {
    return appDialog(
      titleFontSize: 16,
      context: context,
      title: AppStringConstants.approveRejectAgency.tr,
      bodyWidget: StatefulBuilder(
        builder: (context, setState) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText(
                '${AppStringConstants.remarks.tr}:',
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              const SizedBox(
                height: 5,
              ),
              AppTextFormField(
                textFieldFontSize: 14,
                controller: _helper!.remarkController,
                hintText: '',
                maxLines: 5,
                textFieldHeight: 80,
              ),
              const SizedBox(
                height: 10,
              ),
              AppText(
                '${AppStringConstants.status.tr}:',
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              const SizedBox(height: 5),
              AppDropdownButton(
                buttonHeight: 35,
                dropdownItems: _helper!.verificationDropDownList,
                hint: AppStringConstants.notSelected.tr,
                value: _helper!.approveRejectValue,
                onChanged: (value) => setState(
                  () => _helper!.changeVerificationDropDownValue(dropdownValue: value),
                ),
              ),
            ],
          );
        },
      ),
      buttonText: AppStringConstants.submit.tr,
      buttonTap: () {},
    );
  }
}
