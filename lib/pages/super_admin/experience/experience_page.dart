import 'package:ams/app_imports.dart';

class AuditorExperiencePage extends StatefulWidget {
  const AuditorExperiencePage({super.key});

  @override
  State<AuditorExperiencePage> createState() => AuditorExperiencePageState();
}

class AuditorExperiencePageState extends State<AuditorExperiencePage> {
  AuditorExperiencePageHelper? _helper;
  late SuperAdminApplicationReceivedController controller;
  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? (_helper = AuditorExperiencePageHelper(this));
    return GetBuilder(
      init: SuperAdminApplicationReceivedController(),
      builder: (SuperAdminApplicationReceivedController superAdminApplicationReceivedController) {
        controller = superAdminApplicationReceivedController;
        return Stack(
          children: [
            AppScaffold(
              appBar: _appBarView(),
              body: _bodyView(),
            ),
          ],
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.experienceDetailsOf.tr);
  }

  Widget _bodyView() {
    return tableCard(
      children: [
        ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          separatorBuilder: (context,index)=> const SizedBox(height: 10,),
          padding: const EdgeInsets.symmetric(vertical: 5),
          itemCount: _helper!.experienceDetailsList.length,
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return Table(
              border: TableBorder.all(
                color: AppColorConstants.colorMediumGrey,
                borderRadius: BorderRadius.circular(5),
              ),
              children: [
                for (var entry in _helper!.experienceDetailsList[index].toJson().entries)
                  TableRow(
                    children: [
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.centerLeft,
                          color: AppColorConstants.colorLightPurple,
                          child: AppText(
                            entry.key.toString().tr,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: AppText(
                            entry.value.toString(),
                            fontSize: 12,
                            color: AppColorConstants.colorBlack,
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            );
          },
        ),
      ],
    );
  }
}
