import 'package:ams/app_imports.dart';

class AuditorExperiencePageHelper {
  final AuditorExperiencePageState _state;
  List<AuditorExperienceDetails> experienceDetailsList = [
    AuditorExperienceDetails(
      srNo: 1,
      organizationName: 'Test',
      designation: 'developer',
      from: '18-02-2024',
      to: '1-01-2025',
      experienceInMonth: '5',
    ),
    AuditorExperienceDetails(
      srNo: 1,
      organizationName: 'Test',
      designation: 'developer',
      from: '18-02-2024',
      to: '1-01-2025',
      experienceInMonth: '5',
    ),
    AuditorExperienceDetails(
      srNo: 1,
      organizationName: 'Test',
      designation: 'developer',
      from: '18-02-2024',
      to: '1-01-2025',
      experienceInMonth: '5',
    ),
  ];
  AuditorExperiencePageHelper(this._state);
}
