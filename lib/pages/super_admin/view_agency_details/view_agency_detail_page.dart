import 'package:ams/app_imports.dart';

class SuperAdminViewAgencyDetailPage extends StatefulWidget {
  final int? agencyId;
  const SuperAdminViewAgencyDetailPage({required this.agencyId, super.key});

  @override
  State<SuperAdminViewAgencyDetailPage> createState() => SuperAdminViewAgencyDetailPageState();
}

class SuperAdminViewAgencyDetailPageState extends State<SuperAdminViewAgencyDetailPage> {
  SuperAdminViewAgencyDetailPageHelper? _helper;
  late SuperAdminApplicationReceivedController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? SuperAdminViewAgencyDetailPageHelper(this);
    return GetBuilder<SuperAdminApplicationReceivedController>(
      init: SuperAdminApplicationReceivedController(),
      builder: (SuperAdminApplicationReceivedController superAdminApplicationReceivedController) {
        controller = superAdminApplicationReceivedController;
        return AppScaffold(
          apiStatus: _helper!.apiStatus,
          appBar: CommonAppBar(
            backOnTap: () => gotoBack(),
            title: AppStringConstants.agencyDetails.tr,
            titleSize: 22,
            titleWright: FontWeight.w500,
          ),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return tableCard(
      children: [
        _personalInfoView(),
        const SizedBox(
          height: 10,
        ),
        _agencyInfoView(),
        const SizedBox(
          height: 5,
        ),
        _documentAttachedView(),
        const SizedBox(
          height: 15,
        ),
        _kindOfBusinessAndAreaView(),
      ],
    );
  }

  Widget _personalInfoView() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorSkyBlue),
            borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 18,vertical: 10),
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 30),
              _commonRichText('${AppStringConstants.name.tr} : ', _helper!.agencyData.contactPersonName,false),
              _commonRichText('${AppStringConstants.email.tr} : ', _helper!.agencyData.email,false),
              _commonRichText('${AppStringConstants.mobile.tr} : ', _helper!.agencyData.phone,false),
              _commonRichText('${AppStringConstants.address.tr} : ', _helper!.agencyData.address,false),
            ],
          ),
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            AppImageAsset(image: AppAssetsConstants.imgPersonalInfo),
            AppText(
              AppStringConstants.personalInfo.tr,
              fontSize: 18,
              color: AppColorConstants.colorWhite,
            ),
          ],
        ),
      ],
    );
  }

  Widget _agencyInfoView() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorLightGreen),
            borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 18,vertical: 10),
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 30),
              _commonRichText('${AppStringConstants.agencyName.tr} : ', _helper!.agencyData.agencyName,false),
              _commonRichText('${AppStringConstants.certificateNumber.tr} : ', _helper!.agencyData.certificateNumber,false),
              _commonRichText(
                '${AppStringConstants.validUpTo.tr} : ',
                (_helper!.agencyData.validUpTo == null)
                    ? ''
                    : DateHelper.getDDMMYYYYFormatDate(_helper!.agencyData.validUpTo ?? DateTime.now()),
                false,
              ),
              _commonRichText('${AppStringConstants.numberOfAuditor.tr} : ', _helper!.agencyData.nooFAuditors.toString(),true),
              _commonRichText('${AppStringConstants.legalEntryStatus.tr} : ', _helper!.agencyData.legalEntryStatus,true),
              _commonRichText('${AppStringConstants.recognitionNumber.tr} : ', _helper!.agencyData.recognitionNumber ,true),
            ],
          ),
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            AppImageAsset(image: AppAssetsConstants.imgAgencyInfo),
            AppText(
              AppStringConstants.agencyInfo.tr,
              fontSize: 18,
              color: AppColorConstants.colorWhite,
            ),
          ],
        ),
      ],
    );
  }

  Widget _commonRichText(String firstText, String secondText ,bool isRedFont) {
    return Text.rich(
      TextSpan(
        text: firstText,
        style: const TextStyle(
          fontFamily: AppAssetsConstants.defaultFont,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        children: [
          TextSpan(
            text: secondText,
            style: TextStyle(
              fontFamily: AppAssetsConstants.defaultFont,
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: isRedFont ? AppColorConstants.colorRed : AppColorConstants.colorBlack,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _documentAttachedView(){
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppText(AppStringConstants.documentAttached.tr,fontWeight: FontWeight.w600,fontSize: 16,),
        const SizedBox(height: 5,),
        for(int i = 0; i< _helper!.agencyData.docDetails.length ; i++)
        _commonDownloadRow(label: _helper!.agencyData.docDetails[i].filename, onDownloadTap: () {  }),
      ],
    );
  }

  Widget _commonDownloadRow({required String label,required Function()? onDownloadTap}){
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4,horizontal: 4),
      child: Text.rich(
            TextSpan(
              text: '$label*',
              style: const TextStyle(
                fontFamily: AppAssetsConstants.defaultFont,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppColorConstants.colorRed,
              ),
              children: [
                WidgetSpan(child: InkWell(onTap: onDownloadTap,child: Padding(
                  padding: const EdgeInsets.only(left: 5.0),
                  child: AppImageAsset(image: AppAssetsConstants.icDownload,height: 10,color: AppColorConstants.colorRed,),
                ),),),
              ],
            ),
      ),
    );
  }

  Widget _kindOfBusinessAndAreaView(){
   int  kobNo = 0;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStringConstants.kindOfBusiness.tr,
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
        const SizedBox(height: 5,),
        ..._helper!.agencyData.kob.map((kobItem) {
          kobNo++;
          return AppText(
            '$kobNo :- ${kobItem.kobName.toString()}',
            color: AppColorConstants.colorRed,
            fontWeight: FontWeight.w400,
            fontSize: 14,
          );
        }),
        const SizedBox(height: 10,),
        AppText(
          AppStringConstants.geographicalAreasWhereAuditCanBeDone.tr,
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
        const SizedBox(height: 5,),
         AppText(
          _helper!.agencyData.geographicalArea,
          color: AppColorConstants.colorRed,
          fontWeight: FontWeight.w400,
          fontSize: 14,
        ),
      ],
    );
  }
}
