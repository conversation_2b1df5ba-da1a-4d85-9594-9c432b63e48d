import 'package:ams/app_imports.dart';

class SuperAdminViewAgencyDetailPageHelper {
  final SuperAdminViewAgencyDetailPageState _pageState;

  SuperAdminAgencyDetailsModel agencyData = SuperAdminAgencyDetailsModel();
  ApiStatus apiStatus = ApiStatus.initial;

  void getAgencyData() async {
    if (_pageState.widget.agencyId != null) {
      apiStatus = ApiStatus.loading;
      _pageState.controller.update();
      agencyData = await _pageState.controller.getAgencyDetailsData(_pageState.widget.agencyId!);
      apiStatus = ApiStatus.success;
      _pageState.controller.update();
    }
  }

  SuperAdminViewAgencyDetailPageHelper(this._pageState) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () => getAgencyData(),
    );
  }
}
