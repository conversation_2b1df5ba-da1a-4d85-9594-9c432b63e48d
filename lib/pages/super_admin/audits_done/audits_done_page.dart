import 'package:ams/app_imports.dart';

class SuperAdminAuditsDonePage extends StatefulWidget {
  const SuperAdminAuditsDonePage({super.key});

  @override
  State<SuperAdminAuditsDonePage> createState() => SuperAdminAuditsDonePageState();
}

class SuperAdminAuditsDonePageState extends State<SuperAdminAuditsDonePage> {
  SuperAdminAuditDonePageHelper? _helper;
  late SuperAdminAuditsDoneController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? (SuperAdminAuditDonePageHelper(this));
    return GetBuilder<SuperAdminAuditsDoneController>(
      init: SuperAdminAuditsDoneController(),
      builder: (SuperAdminAuditsDoneController superAdminAuditsDoneController) {
        controller = superAdminAuditsDoneController;
        return AppScaffold(
          appBar: CommonAppBar(title: AppStringConstants.auditsDone.tr),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return tableCard(
      fromDateController: _helper!.fromDateController,
      toDateController: _helper!.toDateController,
      fromDateOnTap: () => _helper!.dateOnTap(_helper!.fromDateController),
      toDateOnTap: () => _helper!.dateOnTap(_helper!.toDateController),
      dropDownOnChanged: (value) => _helper!.sortTypeOnChanged(value as String),
      dropdownList: _helper!.dropdownList,
      dropdownValue: _helper!.sortTypeValue.isEmpty ? null : _helper!.sortTypeValue,
      searchController: _helper!.searchController,
      isDateRangeShow: true,
      isShowDropdown: true,
      isShowSearch: true,
      isAscending: _helper!.isAscending,
      sortOnTap: () => _helper!.sortOnTap(),
      children: [
        const SizedBox(height: 10),
        for (int index = 0; index < _helper!.auditDoneList.length; index++)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Table(
              columnWidths: const {
                0 : FixedColumnWidth(130)
              ,},
              border: TableBorder.all(
                color: AppColorConstants.colorMediumGrey,
                borderRadius: BorderRadius.circular(5),
              ),
              children: [
                for (var entry in _helper!.auditDoneList[index].toJson().entries)
                  TableRow(
                    children: [
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.centerLeft,
                          color: AppColorConstants.colorLightPurple,
                          child: entry.key == AppStringConstants.report
                              ? const AppText(
                                  '',
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                )
                              : AppText(
                                  entry.key.tr,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                        ),
                      ),
                      (entry.key == AppStringConstants.report)
                          ? TableCell(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        AppButton(
                                          onTap: () => gotoInspectionReportPage(),
                                          title: AppStringConstants.view.tr,
                                          borderRadius: BorderRadius.circular(5),
                                          textSize: 11,
                                          width: 48,
                                          height: 30,
                                        ),
                                        const SizedBox(width: 5,),
                                        Expanded(
                                          child: AppButton(
                                            iconSize: 10,
                                            imageIcon: AppAssetsConstants.icDownload,
                                            onTap: () {},
                                            title: AppStringConstants.auditReport.tr,
                                            backColor: AppColorConstants.colorYellow,
                                            borderRadius: BorderRadius.circular(5),
                                            textSize: 11,
                                            iconColor: AppColorConstants.colorWhite,
                                            height: 30,
                                            border: Border.all(width: 0.5, color: AppColorConstants.colorYellow),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    AppButton(
                                      iconSize: 10,
                                      imageIcon: AppAssetsConstants.icDownload,
                                      onTap: () {},
                                      title: AppStringConstants.followUpReport.tr,
                                      backColor: AppColorConstants.colorLightGreen,
                                      borderRadius: BorderRadius.circular(5),
                                      textSize: 11,
                                      iconColor: AppColorConstants.colorWhite,
                                      height: 30,
                                      border: Border.all(width: 0.5, color: AppColorConstants.colorLightGreen),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : TableCell(
                              verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                child: AppText(
                                  (entry.key == AppStringConstants.date)
                                      ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                      : entry.value.toString(),
                                  fontSize: 12,
                                  color: AppColorConstants.colorBlack,
                                ),
                              ),
                            ),
                    ],
                  ),
              ],
            ),
          ),
      ],
    );
  }
}
