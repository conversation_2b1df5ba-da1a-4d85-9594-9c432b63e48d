import 'package:ams/app_imports.dart';

class SuperAdminViewAuditorsPageHelper {
  final SuperAdminViewAuditorsPageState _pageState;

  List<String> auditorTableTitles = [
    '#',
    AppStringConstants.auditorName,
    AppStringConstants.address,
    AppStringConstants.audits,
  ];

  List<SuperAdminAuditorViewModel> auditorList = [];

  ApiStatus apiStatus = ApiStatus.initial;

  SuperAdminViewAuditorsPageHelper(this._pageState) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () => getAuditorsData(),
    );
  }

  Future<void> getAuditorsData() async {
    if (_pageState.widget.agencyId != null) {
      apiStatus = ApiStatus.loading;
      _pageState.controller.update();
      List<SuperAdminAuditorModel> data = await _pageState.controller.getAuditorsByAgencyId(_pageState.widget.agencyId!);
      for (int i = 0; i < data.length; i++) {
        auditorList.add(
          SuperAdminAuditorViewModel(
            srNo: i + 1,
            audits: data[i].nooFAudits,
            address: data[i].address,
            auditorName: data[i].auditorName,
          ),
        );
      }
      apiStatus = ApiStatus.success;
      _pageState.controller.update();
    }
  }
}
