import 'package:ams/app_imports.dart';

class SuperAdminViewAuditorsPage extends StatefulWidget {
  final int? agencyId;

  const SuperAdminViewAuditorsPage({required this.agencyId, super.key});

  @override
  State<SuperAdminViewAuditorsPage> createState() => SuperAdminViewAuditorsPageState();
}

class SuperAdminViewAuditorsPageState extends State<SuperAdminViewAuditorsPage> {
  SuperAdminViewAuditorsPageHelper? _helper;
  late SuperAdminViewAuditorAuditController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? SuperAdminViewAuditorsPageHelper(this);
    return GetBuilder<SuperAdminViewAuditorAuditController>(
      init: SuperAdminViewAuditorAuditController(),
      builder: (SuperAdminViewAuditorAuditController auditController) {
        controller = auditController;
        return AppScaffold(
          apiStatus: _helper!.apiStatus,
          appBar: CommonAppBar(
            backOnTap: () => gotoBack(),
            title: AppStringConstants.auditors.tr,
            titleSize: 20,
            titleWright: FontWeight.w500,
          ),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Table(
        columnWidths: const {
          0: FixedColumnWidth(30),
          1: FixedColumnWidth(90),
          3: FixedColumnWidth(80),
        },
        border: TableBorder.all(
          color: AppColorConstants.colorMediumGrey,
          borderRadius: BorderRadius.circular(5),
        ),
        children: [
          TableRow(
            decoration: const BoxDecoration(
              color: AppColorConstants.colorLightPurple,
            ),
            children: [
              for (int i = 0; i < _helper!.auditorTableTitles.length; i++)
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.middle,
                  child: Container(
                    padding: const EdgeInsets.all(6).copyWith(left: 12),
                    child: AppText(
                      _helper!.auditorTableTitles[i].tr,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColorConstants.colorBlue,
                    ),
                  ),
                ),
            ],
          ),
          for (int i = 0; i < _helper!.auditorList.length; i++)
            TableRow(
              decoration: const BoxDecoration(
                color: AppColorConstants.colorWhite,
              ),
              children: [
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.middle,
                  child: Container(
                    padding: const EdgeInsets.all(6).copyWith(left: 12),
                    child: AppText(
                      _helper!.auditorList[i].srNo.toString(),
                      fontSize: 12,
                    ),
                  ),
                ),
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.middle,
                  child: Container(
                    padding: const EdgeInsets.all(6).copyWith(left: 12),
                    child: AppText(
                      _helper!.auditorList[i].auditorName ?? '',
                      fontSize: 12,
                    ),
                  ),
                ),
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.middle,
                  child: Container(
                    padding: const EdgeInsets.all(5).copyWith(left: 12),
                    child: AppText(
                      _helper!.auditorList[i].address ?? '',
                      fontSize: 12,
                    ),
                  ),
                ),
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.middle,
                  child: InkWell(
                    onTap: () => gotoSuperAdminAuditConductByAuditorPage(audits: []),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      child: AppText(
                        _helper!.auditorList[i].audits.toString(),
                        fontSize: 12,
                        color: AppColorConstants.colorAppPrimary,
                      ),
                    ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
