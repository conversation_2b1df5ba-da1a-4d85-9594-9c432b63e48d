import 'package:ams/app_imports.dart';

class SuperAdminAuditConductByAuditorPage extends StatefulWidget {
  final List<AuditModel> audits;
  const SuperAdminAuditConductByAuditorPage({required this.audits, super.key});

  @override
  State<SuperAdminAuditConductByAuditorPage> createState() => SuperAdminAuditConductByAuditorPageState();
}

class SuperAdminAuditConductByAuditorPageState extends State<SuperAdminAuditConductByAuditorPage> {
  SuperAdminAuditsConductByAuditorPageHelper? _helper;
  late SuperAdminEmpanelledAgenciesController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? SuperAdminAuditsConductByAuditorPageHelper(this);
    return GetBuilder<SuperAdminEmpanelledAgenciesController>(
      init: SuperAdminEmpanelledAgenciesController(),
      builder: (SuperAdminEmpanelledAgenciesController superAdminEmpanelledAgenciesController) {
        controller = superAdminEmpanelledAgenciesController;
        return AppScaffold(
          appBar: CommonAppBar(
            backOnTap: () => gotoBack(),
            title: AppStringConstants.auditsConducted.tr,
            titleSize: 20,
            titleWright: FontWeight.w500,
          ),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView(){
    return tableCard(
      children: [
        _auditConductTable(),
      ],
    );
  }

  Widget _auditConductTable(){
    return Table(
      columnWidths: const {
        0: FixedColumnWidth(30),
      },
      border: TableBorder.all(
        color: AppColorConstants.colorMediumGrey,
        borderRadius: BorderRadius.circular(5),
      ),
      children: [
        TableRow(
          decoration: const BoxDecoration(
            color: AppColorConstants.colorLightPurple,
          ),
          children: [
            for (int i = 0; i < _helper!.auditTableTitles.length; i++)
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: Container(
                  padding: const EdgeInsets.all(6).copyWith(left: 12),
                  child: AppText(
                    _helper!.auditTableTitles[i].tr,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        for (int i = 0; i < _helper!.conductedAuditList.length; i++)
          TableRow(
            decoration: const BoxDecoration(
              color: AppColorConstants.colorWhite,
            ),
            children: [
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: Container(
                  padding: const EdgeInsets.all(6).copyWith(left: 12),
                  child: AppText(
                    _helper!.conductedAuditList[i].srNo.toString(),
                    fontSize: 12,
                  ),
                ),
              ),
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: Container(
                  padding: const EdgeInsets.all(6).copyWith(left: 12),
                  child: AppText(
                    _helper!.conductedAuditList[i].licenceNo ?? '',
                    fontSize: 12,
                  ),
                ),
              ),
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: Container(
                  padding: const EdgeInsets.all(5).copyWith(left: 12),
                  child: AppText(
                    _helper!.conductedAuditList[i].companyName ?? '',
                    fontSize: 12,
                  ),
                ),
              ),
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  child: AppText(
                    _helper!.conductedAuditList[i].representativeName ?? '',
                    fontSize: 12,
                  ),
                ),
              ),
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: InkWell(
                  onTap: (){},
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    child: AppImageAsset(image: AppAssetsConstants.icDownload,color: AppColorConstants.colorRed,),
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }

}
