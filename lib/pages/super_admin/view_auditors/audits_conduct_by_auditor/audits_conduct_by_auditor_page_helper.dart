import 'package:ams/app_imports.dart';

class SuperAdminAuditsConductByAuditorPageHelper {
  final SuperAdminAuditConductByAuditorPageState _pageState;

  List<String> auditTableTitles = [
    '#',AppStringConstants.licenseNo,AppStringConstants.companyName,AppStringConstants.representativeName,AppStringConstants.auditReport,
  ];

  List<AuditModel> conductedAuditList = [];

  SuperAdminAuditsConductByAuditorPageHelper(this._pageState);

}
