import 'package:ams/app_imports.dart';

class SuperAdminReportsPage extends StatefulWidget {
  const SuperAdminReportsPage({super.key});

  @override
  State<SuperAdminReportsPage> createState() => SuperAdminReportsPageState();
}

class SuperAdminReportsPageState extends State<SuperAdminReportsPage> {
  SuperAdminReportsPageHelper? _helper;
  late SuperAdminReportsController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? SuperAdminReportsPageHelper(this);
    return GetBuilder(
      init: SuperAdminReportsController(),
      builder: (SuperAdminReportsController superAdminReportsController) {
        controller = superAdminReportsController;
        return AppScaffold(
          apiStatus: _helper!.apiStatus,
          appBar: _appBarView(),
          body: _bodyView(),
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(
      title: AppStringConstants.report.tr,
      titleSize: 18,
    );
  }

  Widget _bodyView() {
    return tableCard(
      dropdownValue: _helper!.sortTypeValue,
      isShowDropdown: true,
      dropdownList: _helper!.dropDownList,
      isAscending: _helper!.isAscending,
      sortOnTap: () => _helper!.sortOnTap(),
      dropDownOnChanged: (value) => _helper!.sortTypeOnChanged(value),
      isShowSearch: true,
      searchController: _helper!.searchController,
      searchOnChanged: (text) => _helper!.searchOnChange(text),
      children: [
        const SizedBox(
          height: 10,
        ),
        _filterView(),
        for (int index = 0; index < _helper!.searchList.length; index++)
          Padding(
            padding: const EdgeInsets.all(8),
            child: Table(
              columnWidths: const {
                0: FixedColumnWidth(130),
              },
              border: TableBorder.all(
                color: AppColorConstants.colorMediumGrey,
                borderRadius: BorderRadius.circular(5),
              ),
              children: [
                for (var entry in _helper!.searchList[index].toJson().entries)
                  TableRow(
                    children: [
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.centerLeft,
                          color: AppColorConstants.colorPeach,
                          child: AppText(
                            entry.key.tr,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      (entry.key == AppStringConstants.report)
                          ? TableCell(
                              child: InkWell(
                                onTap: () {},
                                child: Container(
                                  padding: const EdgeInsets.fromLTRB(10, 12, 10, 10),
                                  alignment: Alignment.centerLeft,
                                  child: AppImageAsset(
                                    image: AppAssetsConstants.icDownload,
                                    color: AppColorConstants.colorRed,
                                    height: 14,
                                  ),
                                ),
                              ),
                            )
                          : TableCell(
                              verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                child: AppText(
                                  (entry.key == AppStringConstants.date)
                                      ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                      : entry.value.toString(),
                                  fontSize: 12,
                                  color: AppColorConstants.colorBlack,
                                ),
                              ),
                            ),
                    ],
                  ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _filterView() {
    return Column(
      children: [
        Row(
          children: [
            AppText(AppStringConstants.auditReportData.tr),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: trStringDropDown(
                dropdownItems: _helper!.filterDropdownList,
                hint: AppStringConstants.select.tr,
                value: _helper!.filteredDropdownValue,
                onChanged: (value) => _helper!.filterDropDownOnChange(value),
              ),
            ),
          ],
        ),
        if (_helper!.filteredDropdownValue == AppStringConstants.filter) ...[
          const SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Expanded(
                child: AppDropdownButton(
                  hint: AppStringConstants.select.tr,
                  value: _helper!.selectedChecklist,
                  onChanged: _helper!.categoryOnChange,
                  selectedItemBuilder: (context) {
                    return _helper!.checkListData
                        .map(
                          (e) => Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: AppText(
                                e.categoryName ?? '',
                                maxLines: 1,
                                fontSize: 16,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        )
                        .toList();
                  },
                  itemBuilder: _helper!.checkListData.map((element) {
                    return DropdownMenuItem(
                      value: element,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: AppText(
                          element.categoryName ?? '',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          fontWeight: FontWeight.w400,
                          fontSize: 16,
                        ),
                      ),
                    );
                  }).toList(),
                  selectedMenuItemBuilder: (context, child) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      child: AppText(
                        _helper!.selectedChecklist?.categoryName ?? '',
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        color: AppColorConstants.colorBlack,
                      ),
                    );
                  },
                  buttonHeight: 25,
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Expanded(
                child: trStringDropDown(
                  dropdownItems: _helper!.tableFilterList,
                  hint: AppStringConstants.select.tr,
                  value: _helper!.tableFilterValue,
                  onChanged: (value) => _helper!.tableFilterOnChange(value),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          if (_helper!.tableFilterValue == AppStringConstants.compliance)
            AppDropdownButton(
              dropdownItems: _helper!.complianceList,
              buttonHeight: 25,
              hint: AppStringConstants.compliance.tr,
              value: _helper!.complianceValue,
              onChanged: (value) => _helper!.complianceOnChange(value),
            ),
          if (_helper!.tableFilterValue == AppStringConstants.rating)
            trStringDropDown(
              dropdownItems: _helper!.ratingList,
              hint: AppStringConstants.rating.tr,
              value: _helper!.ratingValue,
              onChanged: (value) => _helper!.ratingOnChange(value),
            ),
          if ((_helper!.tableFilterValue == AppStringConstants.maxScore ||
              _helper!.tableFilterValue == AppStringConstants.marksScored))
            Row(
              children: [
                Expanded(
                  child: trStringDropDown(
                    dropdownItems: _helper!.scoreList,
                    hint: AppStringConstants.select.tr,
                    value: _helper!.scoreValue,
                    onChanged: (value) => _helper!.scoreOnChange(value),
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                  flex: 1,
                  child: AppTextFormField(
                    textFieldFontSize: 14,
                    textFieldHeight: 25,
                    controller: _helper!.numController,
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: false,
                      signed: true,
                    ),
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    hintText: '',
                  ),
                ),
              ],
            ),
          const SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Expanded(
                child: AppTextFormField(
                  readOnly: true,
                  border: const OutlineInputBorder(
                    borderSide: BorderSide(color: AppColorConstants.colorAppPrimary, width: 0.5),
                  ),
                  onTap: () => _helper!.dateOnTap(_helper!.fromDateController),
                  hintTextSize: 12,
                  textFieldFontSize: 12,
                  hintText: AppStringConstants.ddMmYyyy.toUpperCase(),
                  textFieldHeight: 25,
                  textFieldColor: AppColorConstants.colorLightPurple,
                  controller: _helper!.fromDateController,
                  suffixIcon: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: AppImageAsset(
                      image: AppAssetsConstants.icCalender,
                    ),
                  ),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Expanded(
                child: AppTextFormField(
                  readOnly: true,
                  border: const OutlineInputBorder(
                    borderSide: BorderSide(color: AppColorConstants.colorAppPrimary, width: 0.5),
                  ),
                  onTap: () => _helper!.dateOnTap(_helper!.toDateController),
                  hintTextSize: 12,
                  textFieldFontSize: 12,
                  hintText: AppStringConstants.ddMmYyyy.toUpperCase(),
                  textFieldHeight: 25,
                  textFieldColor: AppColorConstants.colorLightPurple,
                  controller: _helper!.toDateController,
                  suffixIcon: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: AppImageAsset(
                      image: AppAssetsConstants.icCalender,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          AppButton(
            onTap: () => _helper!.getFilter(),
            title: AppStringConstants.show.tr,
            borderRadius: BorderRadius.circular(5),
            textSize: 14,
            titleColor: AppColorConstants.colorWhite,
            height: 30,
            width: 60,
            backColor: AppColorConstants.colorBlue,
          ),
        ],
      ],
    );
  }

  Widget trStringDropDown({
    required String value,
    required void Function(dynamic)? onChanged,
    required List<String> dropdownItems,
    required String hint,
  }) {
    return AppDropdownButton(
      menuItemHeight: 30,
      hintTextSize: 12,
      buttonColor: AppColorConstants.colorLightPurple,
      buttonHeight: 25,
      hint: hint,
      value: value,
      onChanged: onChanged,
      itemBuilder: dropdownItems
          .map(
            (item) => DropdownMenuItem<String>(
              value: item,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                width: double.infinity,
                alignment: Alignment.centerLeft,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: (item == dropdownItems.last)
                        ? BorderSide.none
                        : const BorderSide(color: AppColorConstants.colorGrey, width: 0.5),
                  ),
                ),
                child: AppText(
                  item.tr,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  fontWeight: FontWeight.w400,
                  color: AppColorConstants.colorBlack.withOpacity(0.5),
                ),
              ),
            ),
          )
          .toList(),
      selectedItemBuilder: (context) {
        return dropdownItems
            .map(
              (e) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: AppText(
                    e.tr,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            )
            .toList();
      },
      selectedMenuItemBuilder: (context, child) => Container(
        height: 30,
        width: double.infinity,
        color: AppColorConstants.colorAppPrimary,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Align(
          alignment: Alignment.centerLeft,
          child: AppText(
            value.toString().tr,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            fontWeight: FontWeight.w400,
            color: AppColorConstants.colorWhite,
          ),
        ),
      ),
    );
  }
}
