import 'package:ams/app_imports.dart';

class SuperAdminReportsPageHelper {
  final SuperAdminReportsPageState _pageState;

  TextEditingController searchController = TextEditingController();
  TextEditingController numController = TextEditingController();
  TextEditingController fromDateController = TextEditingController();
  TextEditingController toDateController = TextEditingController();
  ApiStatus apiStatus = ApiStatus.initial;
  String sortTypeValue = AppStringConstants.srNo;
  bool isAscending = true;
  dynamic categoryValue;
  dynamic complianceValue;
  String tableFilterValue = AppStringConstants.rating;
  String filteredDropdownValue = AppStringConstants.all;
  String ratingValue = 'A+';
  String scoreValue = AppStringConstants.lessThan;
  List<String> filterDropdownList = [AppStringConstants.all, AppStringConstants.filter];
  List<String> categoryList = [];
  List<String> tableFilterList = [
    AppStringConstants.rating,
    AppStringConstants.maxScore,
    AppStringConstants.marksScored,
    AppStringConstants.compliance,
  ];
  List<String> ratingList = ['A+', 'A', 'B', 'No grade'];
  List<String> scoreList = [
    AppStringConstants.lessThan,
    AppStringConstants.greaterThan,
    AppStringConstants.equalTo,
    AppStringConstants.lessThanEqualTo,
    AppStringConstants.greaterThanEqualTo,
  ];
  List<String> complianceList = [];
  List<CheckListCategoryModel> checkListData = [];
  CheckListCategoryModel? selectedChecklist;
  List<String> dropDownList = [
    AppStringConstants.srNo,
    AppStringConstants.reportNo,
    AppStringConstants.contactPerson,
    AppStringConstants.companyName,
    AppStringConstants.category,
    AppStringConstants.maxScore,
    AppStringConstants.marksScored,
    AppStringConstants.rating,
    AppStringConstants.auditDate,
  ];

  List<SuperAdminReportModel> searchList = [];
  List<SuperAdminReportModel> reportList = [
    SuperAdminReportModel(
      srNo: 1,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Avengers',
      category: 'Marvel',
      maxScore: 100,
      marksScored: 99,
      rating: 'A',
      compliance: 'Thor',
      auditDate: '27/06/2024 - 27/07/2024',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 1,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Avengers',
      category: 'Marvel',
      maxScore: 100,
      marksScored: 99,
      rating: 'B',
      compliance: 'Cate',
      auditDate: '27/06/2024 - 27/07/2024',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 1,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Avengers',
      category: 'Marvel',
      maxScore: 100,
      marksScored: 99,
      rating: 'No grade',
      compliance: 'Thor',
      auditDate: '27/06/2024 - 22/07/2024',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 1,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Avengers',
      category: 'Marvel',
      maxScore: 100,
      marksScored: 80,
      rating: 'A+',
      compliance: 'Cate',
      auditDate: '27/06/2024 - 22/07/2024',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 2,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Tmkoc',
      category: 'DC',
      maxScore: 100,
      marksScored: 09,
      rating: 'B',
      compliance: 'natasha',
      auditDate: '26/06/2024 - 30/08/2035',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 2,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Tmkoc',
      category: 'DC',
      maxScore: 100,
      marksScored: 09,
      rating: 'B+',
      compliance: 'natasha',
      auditDate: '26/06/2024 - 30/08/2035',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 2,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Tmkoc',
      category: 'Marvel',
      maxScore: 100,
      marksScored: 09,
      rating: 'A+',
      compliance: 'natasha',
      auditDate: '26/06/2024 - 12/08/2035',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 2,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Tmkoc',
      category: 'DC',
      maxScore: 100,
      marksScored: 09,
      rating: 'B+',
      compliance: 'Thor',
      auditDate: '26/06/2024 - 30/08/2035',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 3,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Tiger',
      category: 'MI',
      maxScore: 100,
      marksScored: 45,
      rating: 'C+',
      compliance: 'Spoder-man',
      auditDate: '27/07/2024 - 23/06/2064',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 4,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Pathan',
      category: 'JB',
      maxScore: 100,
      marksScored: 60,
      rating: 'A',
      compliance: 'Iron man',
      auditDate: '28/08/2024 - 07/05/2025',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 5,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Mission',
      category: 'CJ',
      maxScore: 100,
      marksScored: 25,
      rating: 'B',
      compliance: 'hulk',
      auditDate: '29/09/2021 - 17/02/2023',
      report: '',
    ),
    SuperAdminReportModel(
      srNo: 6,
      reportNo: '21/45',
      contactPerson: 'Karan',
      companyName: 'Lion',
      category: 'DQ',
      maxScore: 100,
      marksScored: 33,
      rating: 'C',
      compliance: 'black panther',
      auditDate: '21/01/2020 - 25/06/2021',
      report: '',
    ),
  ];

  SuperAdminReportsPageHelper(this._pageState) {
    searchList = reportList;
    for (var report in reportList) {
      if (report.category != null && !categoryList.contains(report.category!)) {
        categoryList.add(report.category!);
      }
      if (report.compliance != null && !complianceList.contains(report.compliance!)) {
        complianceList.add(report.compliance!);
      }
    }
    numController.text = '0';
  }
  Future<void> getCheckListCategory() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    _pageState.controller.getCheckList().then((value) {
      checkListData = value;
      apiStatus = ApiStatus.success;
      _pageState.controller.update();
    });
  }

  void searchOnChange(String text) {
    searchList = searchList.where((element) {
      return ((element.reportNo.toString().toLowerCase().contains(text.toLowerCase())) ||
          (element.contactPerson.toString().toLowerCase().contains(text.toLowerCase())) ||
          (element.companyName.toString().toLowerCase().contains(text.toLowerCase())) ||
          (element.srNo.toString().toLowerCase().contains(text.toLowerCase())) ||
          (element.category.toString().toLowerCase().contains(text.toLowerCase())) ||
          (element.maxScore.toString().toLowerCase().contains(text.toLowerCase())) ||
          (element.marksScored.toString().toLowerCase().contains(text.toLowerCase())) ||
          (element.auditDate.toString().toLowerCase().contains(text.toLowerCase())) ||
          (element.rating.toString().toLowerCase().contains(text.toLowerCase())) ||
          (element.compliance.toString().toLowerCase().contains(text.toLowerCase())));
    }).toList();
    _pageState.controller.update();
  }

  void clearValue() {
    FocusScope.of(_pageState.context).unfocus();
    searchController.clear();
    searchList = reportList;
    _pageState.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      searchList.sort(
        (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
      );
    } else {
      searchList.sort(
        (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
      );
    }
    _pageState.controller.update();
  }

  void filterDropDownOnChange(String value) {
    filteredDropdownValue = value;
    if (value == AppStringConstants.all) {
      categoryValue = null;
      complianceValue = null;
      tableFilterValue = AppStringConstants.rating;
      ratingValue = 'A+';
      scoreValue = AppStringConstants.lessThan;
      numController.text = '0';
      fromDateController.text = '';
      toDateController.text = '';
      searchList = reportList;
    }
    if (value == AppStringConstants.filter) {
      getCheckListCategory();
    }
    _pageState.controller.update();
  }

  void categoryOnChange(dynamic value) {
    selectedChecklist = value;
    _pageState.controller.update();
  }

  void complianceOnChange(String value) {
    complianceValue = value;
    _pageState.controller.update();
  }

  void tableFilterOnChange(String value) {
    tableFilterValue = value;
    _pageState.controller.update();
  }

  void ratingOnChange(String value) {
    ratingValue = value;
    _pageState.controller.update();
  }

  void scoreOnChange(String value) {
    scoreValue = value;
    _pageState.controller.update();
  }

  void incrementNumber() {
    int currentValue = int.parse(numController.text);
    currentValue++;
    numController.text = (currentValue).toString();
    _pageState.controller.update();
  }

  void decrementNumber() {
    int currentValue = int.parse(numController.text);
    currentValue--;
    numController.text = (currentValue > 0 ? currentValue : 0).toString();
    _pageState.controller.update();
  }

  Future<void> dateOnTap(TextEditingController controller) async {
    DateTime? pickDate = await appDatePicker(_pageState.context);
    if (pickDate != null) {
      controller.text = DateHelper.getDDMMYYYYFormatDate(pickDate);
    }
    _pageState.controller.update();
  }

  void getFilter() {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    List<SuperAdminReportModel> filteredList = reportList;
    if (filteredDropdownValue != AppStringConstants.all) {
      if (categoryValue != null) {
        filteredList = filteredList.where((report) => report.category == categoryValue).toList();
      }
      if (tableFilterValue.isNotEmpty) {
        switch (tableFilterValue) {
          case AppStringConstants.rating:
            filteredList = filteredList.where((report) => report.rating == ratingValue).toList();
            break;
          case AppStringConstants.maxScore:
            filteredList = filteredList.where((report) => _compareScores(report.maxScore!)).toList();
            break;
          case AppStringConstants.marksScored:
            filteredList = filteredList.where((report) => _compareScores(report.marksScored!)).toList();
            break;
          case AppStringConstants.compliance:
            filteredList = filteredList.where((report) => report.compliance == complianceValue).toList();
            break;
        }
      }
    }
    if (fromDateController.text.isNotEmpty && toDateController.text.isNotEmpty) {
      DateTime fromDate = DateFormat('dd/MM/yyyy').parse(fromDateController.text);
      DateTime toDate = DateFormat('dd/MM/yyyy').parse(toDateController.text);

      filteredList = filteredList.where((report) {
        DateTime auditFromDate = DateFormat('dd/MM/yyyy').parse(report.auditDate!.split(' - ')[0]);
        DateTime auditToDate = DateFormat('dd/MM/yyyy').parse(report.auditDate!.split(' - ')[1]);
        return (auditFromDate.isAfter(fromDate) && auditToDate.isBefore(toDate)) || auditFromDate.isBefore(toDate);
      }).toList();
    }
    searchList = filteredList;
    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }

  bool _compareScores(num score) {
    int num = int.parse(numController.text);
    switch (scoreValue) {
      case AppStringConstants.lessThan:
        return score < num;
      case AppStringConstants.greaterThan:
        return score > num;
      case AppStringConstants.equalTo:
        return score == num;
      case AppStringConstants.lessThanEqualTo:
        return score <= num;
      case AppStringConstants.greaterThanEqualTo:
        return score >= num;
      default:
        return false;
    }
  }
}
