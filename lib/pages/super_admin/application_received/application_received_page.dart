import 'package:ams/app_imports.dart';

class SuperAdminApplicationReceivedPage extends StatefulWidget {
  const SuperAdminApplicationReceivedPage({super.key});

  @override
  State<SuperAdminApplicationReceivedPage> createState() => SuperAdminApplicationReceivedPageState();
}

class SuperAdminApplicationReceivedPageState extends State<SuperAdminApplicationReceivedPage> {
  SuperAdminApplicationReceivedHelper? _helper;
  late SuperAdminApplicationReceivedController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? SuperAdminApplicationReceivedHelper(this);
    return GetBuilder<SuperAdminApplicationReceivedController>(
      init: SuperAdminApplicationReceivedController(),
      builder: (SuperAdminApplicationReceivedController superAdminApplicationReceivedController) {
        controller = superAdminApplicationReceivedController;
        return AppScaffold(
          apiStatus: _helper!.apiStatus,
          appBar: CommonAppBar(
            backOnTap: () => gotoBack(),
            title: AppStringConstants.applicationReceived.tr,
            titleSize: 22,
            titleWright: FontWeight.w500,
          ),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return RefreshIndicator(
      onRefresh: () async => _helper!.getApplicationData(),
      child: tableCard(
        sortOnTap: () => _helper!.sortOnTap(),
        dropDownOnChanged: (value) => _helper!.sortTypeOnChanged(value),
        dropdownList: _helper!.dropDownList,
        dropdownValue: _helper!.sortTypeValue,
        searchController: _helper!.searchController,
        isShowDropdown: true,
        isAscending: _helper!.isAscending,
        isShowSearch: true,
        searchOnChanged: (value) => _helper!.searchOnChange(value),
        clearSearchOnTap: () => _helper!.clearValue(),
        isMainListEmpty: _helper!.tableData.isEmpty,
        children: [
          if(_helper!.searchList.isEmpty)
            Container(
              height: MediaQuery.of(context).size.height / 1.5,
              padding: const EdgeInsets.all(8.0),
              child: const NoDataFoundView(),
            ),
          for (var entry in _helper!.searchList)
          _applicationTable(entry),
        ],
      ),
    );
  }

  Widget _applicationTable(SuperAdminApplicationReceivedViewModel model) {
    var mapData = model.toJson().entries.where((entry) => entry.key != AppStringConstants.audits).toList();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Table(
        columnWidths: const {0:FixedColumnWidth(140)},
        border: TableBorder.all(
          color: AppColorConstants.colorMediumGrey,
          borderRadius: BorderRadius.circular(5),
        ),
        children: [
          for (var entry in mapData)
            TableRow(
              children: [
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    alignment: Alignment.centerLeft,
                    color: AppColorConstants.colorLightPurple,
                    child: AppText(
                      entry.key.toString().tr,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
                TableCell(
                  verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    color: AppColorConstants.colorWhite,
                    alignment: Alignment.centerLeft,
                    child: entry.key == AppStringConstants.auditors
                        ? InkWell(
                            onTap: () => gotoSuperViewAgencyAuditorsPage(agencyId: model.agencyId),
                            child: SizedBox(
                              width: double.infinity,
                              child: AppText(
                                entry.value.toString(),
                                fontSize: 12,
                                color: AppColorConstants.colorBlue,
                              ),
                            ),
                          )
                        : entry.key == AppStringConstants.viewDetails
                            ? InkWell(
                                onTap: () => gotoSuperAdminAgencyDetailPage(agencyId: model.agencyId),
                                child: Container(
                                  alignment: Alignment.centerLeft,
                                  width: double.infinity,
                                  child: AppImageAsset(
                                    height: 13,
                                    image: AppAssetsConstants.icEye,
                                  ),
                                ),
                              )
                            : entry.key == AppStringConstants.verifyReject
                                ? InkWell(
                                    onTap: () => _agencyVerificationDialogue(model.agencyId ?? 0),
                                    child: Container(
                                      alignment: Alignment.centerLeft,
                                      width: double.infinity,
                                      child: AppImageAsset(
                                        height: 20,
                                        image: AppAssetsConstants.icEdit,
                                      ),
                                    ),
                                  )
                                : entry.key == AppStringConstants.status
                                    ? AppText(
                                        entry.value.toString(),
                                        fontSize: 12,
                                        color: entry.value == AppStringConstants.approvedStatus
                                            ? AppColorConstants.colorLightGreen
                                            : (entry.value == AppStringConstants.pendingStatus)
                                                ? AppColorConstants.colorBlue
                                                : AppColorConstants.colorRed,
                                      )
                                    : AppText(
                                        entry.value.toString(),
                                        fontSize: 12,
                                        color: AppColorConstants.colorBlack,
                                      ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  void _agencyVerificationDialogue(int agencyId) async {
    _helper!.remarkController.clear();
    _helper!.approveRejectValue = '';
    return appDialog(
      titleFontSize: 16,
      context: context,
      title: AppStringConstants.approveRejectAgency.tr,
      bodyWidget: StatefulBuilder(
        builder: (context, setState) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText(
                '${AppStringConstants.remarks.tr}:',
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              const SizedBox(
                height: 5,
              ),
              AppTextFormField(
                textFieldFontSize: 14,
                controller: _helper!.remarkController,
                hintText: '',
                maxLines: 5,
                textFieldHeight: 80,
              ),
              const SizedBox(
                height: 10,
              ),
              AppText(
                '${AppStringConstants.status.tr}:',
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              const SizedBox(height: 5),
              AppDropdownButton(
                buttonHeight: 35,
                dropdownItems: _helper!.verificationDropDownList,
                hint: AppStringConstants.notSelected.tr,
                value: _helper!.approveRejectValue.isEmpty ? null : _helper!.approveRejectValue,
                onChanged: (value) => setState(
                  () => _helper!.changeVerificationDropDownValue(dropdownValue: value),
                ),
              ),
            ],
          );
        },
      ),
      buttonText: AppStringConstants.submit.tr,
      buttonTap: () => _helper!.changeAgencyStatus(agencyId),
    );
  }
}
