import 'package:ams/app_imports.dart';

class ViewAuditorPageHelper {
  final SuperAdminViewAuditorPageState _state;
  ApiStatus apiStatus = ApiStatus.initial;
  TextEditingController searchController = TextEditingController();
  TextEditingController remarkController = TextEditingController();
  String? selectedValue;
  bool isAscending = false;
  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.auditorName,
    AppStringConstants.emailAndMobile,
    AppStringConstants.agencyName,
    AppStringConstants.auditsConducted,
    AppStringConstants.experience,
    AppStringConstants.qualification,
  ];

  List<SuperAdminAuditorDetailsViewModel> auditorDetailsList = [];
  List<SuperAdminEnlistedAuditorsModel> _data = [];
  List<SuperAdminAuditorDetailsViewModel> searchList = [];

  Future<void> getApplicationData() async {
    apiStatus = ApiStatus.loading;
    _state.controller.update();
    _data = await _state.controller.getEnlistedAuditorsOfAgency(agencyId: _state.widget.agencyId.toString());
    auditorDetailsList.clear();
    initializeAuditorList();
    searchList = auditorDetailsList;
    apiStatus = ApiStatus.success;
    _state.controller.update();
  }

  void initializeAuditorList(){
    for (int i = 0; i < (_data.length); i++) {
      SuperAdminEnlistedAuditorsModel model = _data[i];
      auditorDetailsList.add(
        SuperAdminAuditorDetailsViewModel(
          srNo: i+1,
          auditsConducted: model.nooFAudits.toString(),
          experience: model.expertise,
          auditorName: model.auditorName,
          agencyName: model.agencyName,
          emailAndMobile: '${model.mobile}\n${model.email}',
          qualification: model.address,
          auditorStatus: 'Activate',
          documentAttached: [model.leadAuditorFile,model.qualificationFile,model.auditorLogFile,model.sectorsPecificFile],
        ),
      );
    }
    _state.controller.update();
  }
  ViewAuditorPageHelper(this._state) {
    Future.delayed(const Duration(milliseconds: 10),()=> getApplicationData());
  }

  void searchOnChange(String text) {
    searchList = auditorDetailsList.where((element) {
      return ((element.auditorStatus.toString().toLowerCase().contains(text)) ||
          (element.qualification.toString().toLowerCase().contains(text)) ||
          (element.experience.toString().toLowerCase().contains(text)) ||
          (element.srNo.toString().toLowerCase().contains(text)) ||
          (element.agencyName.toString().toLowerCase().contains(text)) ||
          (element.emailAndMobile.toString().toLowerCase().contains(text)) ||
          (element.auditorName.toString().toLowerCase().contains(text)) ||
          (element.auditsConducted.toString().toLowerCase().contains(text)));
    }).toList();
    _state.controller.update();
  }

  void clearValue() {
    FocusScope.of(_state.context).unfocus();
    searchController.clear();
    searchList = auditorDetailsList;
    _state.controller.update();
  }

  void dropDownOnChange(dynamic value) {
    selectedValue = value;
    sortOnTap();
    _state.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    if (isAscending) {
      if (selectedValue == AppStringConstants.srNo) {
        auditorDetailsList.sort((a, b) => a.srNo.toString().compareTo(b.srNo.toString()));
      } else if ((selectedValue ?? '') == AppStringConstants.auditorName) {
        auditorDetailsList.sort((a, b) => a.auditorName.toString().compareTo(b.auditorName.toString()));
      } else if ((selectedValue ?? '') == AppStringConstants.emailAndMobile) {
        auditorDetailsList.sort((a, b) => a.emailAndMobile.toString().compareTo(b.emailAndMobile.toString()));
      } else if ((selectedValue ?? '') == AppStringConstants.agencyName) {
        auditorDetailsList.sort((a, b) => a.agencyName.toString().compareTo(b.agencyName.toString()));
      } else if ((selectedValue ?? '') == AppStringConstants.auditsConducted) {
        auditorDetailsList.sort((a, b) => a.auditsConducted.toString().compareTo(b.auditsConducted.toString()));
      }
    } else {
      if ((selectedValue ?? '') == AppStringConstants.srNo) {
        auditorDetailsList.sort((a, b) => b.srNo.toString().compareTo(a.srNo.toString()));
      } else if ((selectedValue ?? '') == AppStringConstants.auditorName) {
        auditorDetailsList.sort((a, b) => b.auditorName.toString().compareTo(a.auditorName.toString()));
      } else if ((selectedValue ?? '') == AppStringConstants.emailAndMobile) {
        auditorDetailsList.sort((a, b) => b.emailAndMobile.toString().compareTo(a.emailAndMobile.toString()));
      } else if ((selectedValue ?? '') == AppStringConstants.agencyName) {
        auditorDetailsList.sort((a, b) => b.agencyName.toString().compareTo(a.agencyName.toString()));
      } else if ((selectedValue ?? '') == AppStringConstants.auditsConducted) {
        auditorDetailsList.sort((a, b) => b.auditsConducted.toString().compareTo(a.auditsConducted.toString()));
      }
    }
    _state.controller.update();
  }
}
