import 'package:ams/app_imports.dart';

class SuperAdminViewAuditorPage extends StatefulWidget {
  final int? agencyId ;
  const SuperAdminViewAuditorPage({required this.agencyId, super.key});

  @override
  State<SuperAdminViewAuditorPage> createState() => SuperAdminViewAuditorPageState();
}

class SuperAdminViewAuditorPageState extends State<SuperAdminViewAuditorPage> {
  late SuperAdminApplicationReceivedController controller;
  ViewAuditorPageHelper? _helper;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? (ViewAuditorPageHelper(this));
    return GetBuilder(
      init: SuperAdminApplicationReceivedController(),
      builder: (SuperAdminApplicationReceivedController superAdminApplicationReceivedController) {
        controller = superAdminApplicationReceivedController;
        return Stack(
          children: [
            AppScaffold(
              apiStatus: _helper!.apiStatus,
              appBar: _appBarView(),
              body: Column(
                children: [
                  Expanded(
                    child: tableCard(
                      children: [
                        for (var item in _helper!.searchList) _tableView(item.toJson()),
                      ],
                      sortOnTap: _helper!.sortOnTap,
                      searchController: _helper!.searchController,
                      dropdownValue: _helper!.selectedValue,
                      dropDownOnChanged: _helper!.dropDownOnChange,
                      dropdownList: _helper!.dropdownList,
                      isShowDropdown: true,
                      isAscending: _helper!.isAscending,
                      searchOnChanged: _helper!.searchOnChange,
                      isShowSearch: true,
                      clearSearchOnTap: _helper!.clearValue,
                      isMainListEmpty: _helper!.auditorDetailsList.isEmpty,
                    ),
                  ),
                ],
              ),
            ),
            if (_helper!.apiStatus == ApiStatus.loading) Container(),
          ],
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.enlistedAuditors.tr);
  }

  Widget _tableView(Map<String, dynamic> item) {
    return Padding(
      padding: const EdgeInsets.all(12.0).copyWith(left: 0, right: 0),
      child: Table(
        border: TableBorder.all(
          color: AppColorConstants.colorMediumGrey,
          borderRadius: BorderRadius.circular(5),
        ),
        children: [
          _commonTextRow(key: AppStringConstants.srNo, value: item[AppStringConstants.srNo].toString()),
          _auditorName(AppStringConstants.auditorName, item[AppStringConstants.auditorName]),
          _commonTextRow(key: AppStringConstants.emailAndMobile, value: item[AppStringConstants.emailAndMobile]),
          _commonTextRow(key: AppStringConstants.agencyName, value: item[AppStringConstants.agencyName]),
          _commonTextRow(
            key: AppStringConstants.auditsConducted,
            value: item[AppStringConstants.auditsConducted],
            onTap: () => gotoSuperAdminConductAuditReportPage(),
          ),
          _commonIconRow(AppStringConstants.experience, AppAssetsConstants.icEye, gotoSuperAdminExperiencePage),
          _commonIconRow(AppStringConstants.qualification, AppAssetsConstants.icForward, gotoSuperAdminQualificationPage),
          _commonIconRow(AppStringConstants.removeAuditor, AppAssetsConstants.icDelete, _deleteAppDialog),
          _documentAttachedRow(
            AppStringConstants.documentAttached,
            item[AppStringConstants.documentAttached],
          ),
        ],
      ),
    );
  }

  TableRow _commonTextRow({required String key, required String value, VoidCallback? onTap}) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: InkWell(
            onTap: () => onTap!(),
            child: Container(
              color: AppColorConstants.colorLightYellow100,
              padding: const EdgeInsets.all(10),
              child: AppText(
                value.toString().tr,
                fontSize: 12,
                color: onTap == null ? AppColorConstants.colorBlack : AppColorConstants.colorAppPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  TableRow _auditorName(String key, String value) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightYellow100,
            padding: const EdgeInsets.all(10),
            child: AppText(
              value.toString(),
              fontSize: 12,
              color: AppColorConstants.colorBlack,
            ),
          ),
        ),
      ],
    );
  }

  TableRow _commonIconRow(String key, String image, VoidCallback onTap) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: InkWell(
            onTap: () => onTap(),
            child: Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.all(10),
              child: AppImageAsset(
                image: image,
              ),
            ),
          ),
        ),
      ],
    );
  }

  TableRow _documentAttachedRow(String key, List<String> value) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            color: AppColorConstants.colorLightPurple,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            child: AppText(
              key.tr,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        TableCell(
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () => launchDocumentUrl('https://infotrain.fssai.gov.in/AMS/uploadauditordata/${value[0]}'),
                  child: AppText(
                    AppStringConstants.leadAuditorCertificate.tr,
                    fontSize: 10,
                    color: AppColorConstants.colorAppPrimary,
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                InkWell(
                  onTap: () => launchDocumentUrl('https://infotrain.fssai.gov.in/AMS/uploadauditordata/${value[1]}'),
                  child: AppText(
                    AppStringConstants.educationalQualificationCertificate.tr,
                    fontSize: 10,
                    color: AppColorConstants.colorAppPrimary,
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                InkWell(
                  onTap: () => launchDocumentUrl('https://infotrain.fssai.gov.in/AMS/uploadauditordata/${value[2]}'),
                  child: AppText(
                    AppStringConstants.auditorLog.tr,
                    fontSize: 10,
                    color: AppColorConstants.colorAppPrimary,
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                InkWell(
                  onTap: () => launchDocumentUrl('https://infotrain.fssai.gov.in/AMS/uploadauditordata/${value[3]}'),
                  child: AppText(
                    AppStringConstants.sectorSpecificKnowledge.tr,
                    fontSize: 10,
                    color: AppColorConstants.colorAppPrimary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future _deleteAppDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: AppColorConstants.colorWhite,
          title: Center(
            child: AppText(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              AppStringConstants.removeAuditor.tr,
              color: AppColorConstants.colorAppPrimary,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText(AppStringConstants.enterRemarks.tr, fontWeight: FontWeight.w700),
              const SizedBox(
                height: 10,
              ),
              AppTextFormField(controller: _helper!.remarkController, hintText: AppStringConstants.enterRemarks.tr),
              const SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Expanded(
                    child: AppButton(
                      height: 35,
                      title: AppStringConstants.submit.tr,
                      onTap: () {},
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: AppButton(
                      height: 35,
                      title: AppStringConstants.close.tr,
                      onTap: () => gotoBack(),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
