import 'package:ams/app_imports.dart';

class SuperAdminApplicationReceivedHelper {
  final SuperAdminApplicationReceivedPageState _pageState;

  String sortTypeValue = AppStringConstants.srNo;

  TextEditingController searchController = TextEditingController();
  TextEditingController remarkController = TextEditingController();

  List<String> dropDownList = [
    AppStringConstants.srNo,
    AppStringConstants.agencyName,
    AppStringConstants.state,
    AppStringConstants.address,
    AppStringConstants.mobile,
    AppStringConstants.status,
    AppStringConstants.remarks,
    AppStringConstants.auditors,
  ];

  List<String> verificationDropDownList = [
    AppStringConstants.approve,
    AppStringConstants.reject,
  ];
  List<SuperAdminApplicationReceivedViewModel> searchList = [];
  List<SuperAdminApplicationReceivedViewModel> tableData = [];
  String approveRejectValue = '';
  bool isAscending = true;

  ApiStatus apiStatus = ApiStatus.initial;

  List<SuperAdminApplicationReceivedModel> data = [];
  int srNo = 1;
  int pageCount = 10;

  SuperAdminApplicationReceivedHelper(this._pageState) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () => getApplicationData(),
    );
  }

  Future<void> getApplicationData() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    setDefaultValue();
    data = await _pageState.controller.getSuperAdminApplicationReceivedData();
    tableData.clear();
    _initializeData();

    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }

  void setDefaultValue() {
    sortTypeValue = AppStringConstants.srNo;
    searchController.clear();
    isAscending = true;
  }

  void clearValue() {
    FocusScope.of(_pageState.context).unfocus();
    searchController.clear();
    searchList = tableData;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }

  void _initializeData() {
    tableData.clear();
    for (int i = 0; i < (data.length); i++) {
      SuperAdminApplicationReceivedModel model = data[i];
      tableData.add(
        SuperAdminApplicationReceivedViewModel(
          agencyId: model.agencyId,
          srNo: i + 1,
          agencyName: model.agencyName,
          address: model.address,
          remarks: model.remarks,
          mobile: model.mobile,
          auditors: model.nooFAuditors,
          audits: model.nooFAudits,
          state: model.stateName,
          status: model.status == 'N'
              ? AppStringConstants.pendingStatus
              : (model.status == 'Y')
                  ? AppStringConstants.approvedStatus
                  : (model.status == 'R')
                      ? AppStringConstants.rejectedStatus
                      : '',
        ),
      );
    }
    searchList = tableData;
    _pageState.controller.update();
  }

  void changeVerificationDropDownValue({required String dropdownValue}) {
    approveRejectValue = dropdownValue;
    _pageState.controller.update();
  }

  void searchOnChange(String value) {
    searchList = tableData.where((element) {
      return ((element.srNo.toString().toLowerCase().contains(value)) ||
          (element.state.toString().toLowerCase().contains(value)) ||
          (element.agencyName.toString().toLowerCase().contains(value)) ||
          (element.mobile.toString().toLowerCase().contains(value)) ||
          (element.address.toString().toLowerCase().contains(value)) ||
          (element.auditors.toString().toLowerCase().contains(value)) ||
          (element.remarks.toString().toLowerCase().contains(value)));
    }).toList();
    _pageState.controller.update();
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      searchList.sort(
        (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
      );
    } else {
      searchList.sort(
        (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
      );
    }
    _pageState.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }

  Future<void> changeAgencyStatus(int agencyId) async {
    gotoBack();
    String status = (approveRejectValue == AppStringConstants.approve)
        ? 'Y'
        : (approveRejectValue == AppStringConstants.reject)
            ? 'R'
            : approveRejectValue;
    bool isSuccess = await _pageState.controller
        .approveRejectAgency(agencyId: agencyId, status: status, remarks: remarkController.text.trim());
    if(isSuccess){
      remarkController.clear();
      getApplicationData();
    }
  }
}
