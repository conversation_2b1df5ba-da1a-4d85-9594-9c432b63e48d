import 'package:ams/app_imports.dart';

class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({super.key});

  @override
  State<ChangePasswordPage> createState() => ChangePasswordPageState();
}

class ChangePasswordPageState extends State<ChangePasswordPage> {
  late AuthenticationController authenticationController;
  ChangePasswordPageHelper? _changePasswordPageHelper;
  @override
  Widget build(BuildContext context) {
    _changePasswordPageHelper = _changePasswordPageHelper ?? ChangePasswordPageHelper(this);
    return GetBuilder(
      init: AuthenticationController(),
      builder: (AuthenticationController controller) {
        authenticationController = controller;
        return AppScaffold(
          appBar: _appBarView(),
          body: _bodyView(),
          apiStatus: _changePasswordPageHelper!.apiStatus,
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(title: AppStringConstants.changePassword.tr);
  }

  Widget _bodyView() {
    return SingleChildScrollView(
      child: Container(
        margin: const EdgeInsets.all(15),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorAppPrimary),
          borderRadius: BorderRadius.circular(10),
        ),
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 12,
                ),  AppText(
                  AppStringConstants.enterOldPassword.tr,
                  fontWeight: FontWeight.w500,
                ),
                const SizedBox(
                  height: 2,
                ),AppTextFormField(
                  errorText: _changePasswordPageHelper!.oldPasswordError.tr,
                  contentPadding: const EdgeInsets.only(bottom: 13),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 12),
                    child: AppImageAsset(
                      image: AppAssetsConstants.icLock,
                    ),
                  ),
                  controller: _changePasswordPageHelper!.oldPasswordController,
                  hintText: AppStringConstants.enterOldPassword.tr,
                ),
                const SizedBox(
                  height: 12,
                ),
                AppText(
                  AppStringConstants.enterNewPassword.tr,
                  fontWeight: FontWeight.w500,
                ),
                const SizedBox(
                  height: 2,
                ),
                AppTextFormField(
                  errorText: _changePasswordPageHelper!.newPasswordError.tr,
                  contentPadding: const EdgeInsets.only(bottom: 13),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 12),
                    child: AppImageAsset(
                      image: AppAssetsConstants.icLock,
                    ),
                  ),
                  controller: _changePasswordPageHelper!.newPasswordController,
                  hintText: AppStringConstants.enterNewPassword.tr,
                ),
                const SizedBox(
                  height: 12,
                ),
                AppText(
                  AppStringConstants.enterConfirmPassword.tr,
                  fontWeight: FontWeight.w500,
                ),
                const SizedBox(
                  height: 2,
                ),
                AppTextFormField(
                  errorText: _changePasswordPageHelper!.confirmPasswordError.tr,
                  contentPadding: const EdgeInsets.only(bottom: 13),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 12),
                    child: AppImageAsset(
                      image: AppAssetsConstants.icLock,
                    ),
                  ),
                  controller: _changePasswordPageHelper!.confirmPasswordController,
                  hintText: AppStringConstants.enterConfirmPassword.tr,
                ),
                const SizedBox(
                  height: 12,
                ),
                AppButton(onTap: ()=>_changePasswordPageHelper!.submitOnTap(), title: AppStringConstants.submit.tr,height: 45,),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
