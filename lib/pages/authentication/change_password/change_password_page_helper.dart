import 'package:ams/app_imports.dart';


class ChangePasswordPageHelper{
  ChangePasswordPageState state;
  ApiStatus apiStatus=ApiStatus.initial;
  TextEditingController oldPasswordController=TextEditingController();
  String oldPasswordError='';
  String newPasswordError='';
  String confirmPasswordError='';
  TextEditingController newPasswordController=TextEditingController();
  TextEditingController confirmPasswordController=TextEditingController();
  ChangePasswordPageHelper(this.state);

  ///-----------------------------Functions--------------------------------

 void submitOnTap(){
   if(ValidationHelper.instance.validateEmptyController(oldPasswordController)){
     oldPasswordError=AppStringConstants.pleaseEnterOldPassword;
   }else{
     oldPasswordError='';
   }if(ValidationHelper.instance.validateEmptyController(newPasswordController)){
     newPasswordError=AppStringConstants.pleaseEnterNewPassword;
   }else if(newPasswordController.text.trim().length>12 || newPasswordController.text.trim().length<7){
     newPasswordError=AppStringConstants.pleaseEnterPasswordLength;
   }else{
     newPasswordError='';
   }if(ValidationHelper.instance.validateEmptyController(confirmPasswordController)){
     confirmPasswordError=AppStringConstants.pleaseEnterConfirmPassword;
   }else if(ValidationHelper.instance.compareValidator(newPasswordController,confirmPasswordController)){
     confirmPasswordError=AppStringConstants.confirmPasswordDoesNotMatch;
   }else{
     confirmPasswordError='';
   }
   if(oldPasswordError.isEmpty && newPasswordError.isEmpty && confirmPasswordError.isEmpty){
     changePassword();
   }
   state.authenticationController.update();
 }

 Future<void> changePassword()async{
   apiStatus=ApiStatus.loading;
   state.authenticationController.update();
   state.authenticationController.changePassword( oldPasswordController.text, newPasswordController.text).then((value){
     apiStatus=ApiStatus.success;
     state.authenticationController.update();
     if(value){
     oldPasswordController.clear();
     newPasswordController.clear();
     confirmPasswordController.clear();
     AppStringConstants.passwordChangeSuccessfully.tr.showSuccessToast();
   }else{
     AppStringConstants.incorrectUsernameOrPassword.tr.showErrorToast();
   }
   });
 }

}
