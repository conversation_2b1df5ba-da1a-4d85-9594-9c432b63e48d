import 'package:ams/app_imports.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => ForgotPasswordPageState();
}

class ForgotPasswordPageState extends State<ForgotPasswordPage> {
  ForgotPasswordHelper? _forgotPasswordHelper;
  late AuthenticationController controller;

  @override
  Widget build(BuildContext context) {
    _forgotPasswordHelper = _forgotPasswordHelper ?? ForgotPasswordHelper(this);
    return GetBuilder<AuthenticationController>(
      init: AuthenticationController(),
      builder: (AuthenticationController forgotPasswordController) {
        controller = forgotPasswordController;
        return AppScaffold(
          apiStatus: _forgotPasswordHelper!.apiStatus,
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(
          height: 40,
        ),
        _imageView(),
        const Sized<PERSON>ox(
          height: 15,
        ),
        Container(
          color: AppColorConstants.colorDarkGrey,
          height: 1,
          width: double.infinity,
        ),
        Expanded(child: SingleChildScrollView(child: _forgotPasswordContainer1())),
      ],
    );
  }

  Widget _imageView() {
    return Container(
      padding: EdgeInsets.only(top: MediaQuery.of(context).viewInsets.top),
      child: Row(
        children: [
          const SizedBox(
            width: 10,
          ),
          AppImageAsset(
            image: AppAssetsConstants.fssaiImage,
            width: 100,
          ),
          const SizedBox(
            width: 40,
          ),
          Expanded(
            child: AppImageAsset(image: AppAssetsConstants.auditManagementImage),
          ),
          const SizedBox(
            width: 10,
          ),
        ],
      ),
    );
  }

  Widget _forgotPasswordContainer1() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: AppColorConstants.colorAppPrimary),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 50,
            alignment: Alignment.center,
            decoration: const BoxDecoration(
              color: AppColorConstants.colorAppPrimary,
              borderRadius: BorderRadius.only(topRight: Radius.circular(9), topLeft: Radius.circular(9)),
            ),
            child: AppText(
              AppStringConstants.forgotPassword.tr,
              color: AppColorConstants.colorWhite,
              fontSize: 16,
              fontWeight: FontWeight.w700,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText(
                  AppStringConstants.logInId.tr,
                  fontSize: 16,
                ),
                const SizedBox(
                  height: 5,
                ),
                AppTextFormField(
                  errorText: _forgotPasswordHelper!.loginIdErrorText,
                  controller: _forgotPasswordHelper!.loginIdController,
                  hintText: AppStringConstants.logInId.tr,
                  hintTextSize: 14,
                ),
                const SizedBox(
                  height: 10,
                ),
                AppText(
                  AppStringConstants.registeredEmailId.tr,
                  fontSize: 16,
                ),
                const SizedBox(
                  height: 5,
                ),
                AppTextFormField(
                  errorText: _forgotPasswordHelper!.emailErrorText,
                  controller: _forgotPasswordHelper!.registeredEmailController,
                  hintText: AppStringConstants.registeredEmailId.tr,
                  hintTextSize: 14,
                ),
                const SizedBox(
                  height: 5,
                ),
                _forgotPasswordHelper!.isCodeSent ? AppText(AppStringConstants.sixDigitCodeHasBeenSentToTheEmail.tr,color: AppColorConstants.colorBlue,fontSize: 14,) :
                InkWell(
                  onTap: () => _forgotPasswordHelper!.sendOtp(),
                  child: AppText(
                    AppStringConstants.sendSixDigitLetterCode.tr,
                    color: AppColorConstants.colorBlue,
                    fontWeight: FontWeight.w400,
                  ),
                ),
               if(_forgotPasswordHelper!.isCodeSent)
               ...[
                 const SizedBox(
                   height: 10,
                 ),
                 AppText(
                   AppStringConstants.enterSixDigitCode.tr,
                   fontSize: 16,
                 ),
                 const SizedBox(
                   height: 5,
                 ),
                 AppTextFormField(
                   controller: _forgotPasswordHelper!.sixDigitCodeController,
                   hintText: AppStringConstants.enterSixDigitCode.tr,
                   hintTextSize: 14,
                   maxLength: 6,
                 ),
                 const SizedBox(
                   height: 10,
                 ),
                 AppText(
                   AppStringConstants.enterNewPassword.tr,
                   fontSize: 16,
                 ),
                 const SizedBox(
                   height: 5,
                 ),
                 AppTextFormField(
                   controller: _forgotPasswordHelper!.newPasswordController,
                   hintText: AppStringConstants.enterNewPassword.tr,
                   hintTextSize: 14,
                 ),
                 const SizedBox(
                   height: 10,
                 ),
                 AppText(
                   AppStringConstants.reEnterPassword.tr,
                   fontSize: 16,
                 ),
                 const SizedBox(
                   height: 5,
                 ),
                 AppTextFormField(
                   controller: _forgotPasswordHelper!.reEnterPasswordController,
                   hintText: AppStringConstants.reEnterPassword.tr,
                   hintTextSize: 14,
                 ),
                 const SizedBox(
                   height: 10,
                 ),
                 Center(
                   child: AppButton(
                     onTap: () => _forgotPasswordHelper!.createNewPassword(),
                     borderRadius: BorderRadius.circular(5),
                     backColor: AppColorConstants.colorBlue,
                     title: AppStringConstants.submit.tr,
                     textSize: 14,
                     height: 35,
                     width: 100,
                   ),
                 ),
               ],
                const SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () => gotoSignupPage(),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: AppText(AppStringConstants.signUp.tr),
                      ),
                    ),
                    const AppText(' | '),
                    InkWell(
                      onTap: () => gotoLoginPage(),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: AppText(AppStringConstants.logIn.tr),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
