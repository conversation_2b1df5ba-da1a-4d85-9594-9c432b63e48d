import 'package:ams/app_imports.dart';

class ForgotPasswordHelper {
  final ForgotPasswordPageState _pageState;
  TextEditingController loginIdController = TextEditingController();
  TextEditingController registeredEmailController = TextEditingController();
  TextEditingController sixDigitCodeController = TextEditingController();
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController reEnterPasswordController = TextEditingController();

  final ValidationHelper validationHelper = ValidationHelper.instance;

  String loginIdErrorText = '';
  String emailErrorText = '';
  String codeErrorText = '';
  String passWordErrorText = '';
  String rePassWordErrorText = '';

  ApiStatus apiStatus = ApiStatus.initial;
  bool isCodeSent = false;
  ForgotPasswordHelper(this._pageState);

  void isLoginIdValidate() {
    if (validationHelper.validateEmptyController(loginIdController)) {
      loginIdErrorText = AppStringConstants.pleaseEnterLoginID.tr;
    } else {
      loginIdErrorText = '';
    }
    _pageState.controller.update();
  }

  void isEmailValidate() {
    if (validationHelper.validateEmptyController(registeredEmailController)) {
      emailErrorText = AppStringConstants.pleaseEnterEmail.tr;
    } else if (validationHelper.regexValidator(
      registeredEmailController,
      validationHelper.emailRegExp,
    )) {
      emailErrorText = AppStringConstants.pleaseEnterValidEmail.tr;
    } else {
      emailErrorText = '';
    }
    _pageState.controller.update();
  }

  void sendOtp() {
    isLoginIdValidate();
    isEmailValidate();
    if (emailErrorText.isEmpty && loginIdErrorText.isEmpty) {
      sendSixDigitCodeToMail();
    }
    _pageState.controller.update();
  }

  void sendSixDigitCodeToMail() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    isCodeSent =
        await _pageState.controller.getSixDigitCode(loginId: loginIdController.text, registerId: registeredEmailController.text);
    if (isCodeSent) {
      apiStatus = ApiStatus.success;
      _pageState.controller.update();
    } else {
      apiStatus = ApiStatus.success;
      _pageState.controller.update();
    }
  }

  void createNewPassword() {
    if (validationHelper.validateEmptyController(newPasswordController)) {
      passWordErrorText = AppStringConstants.pleaseEnterNewPassword;
    } else {
      String newPassword = newPasswordController.text;
      if (newPassword.length < 7 || newPassword.length > 12) {
        passWordErrorText = AppStringConstants.passwordShouldBe7To12LetterLong.tr;
      } else if (!RegExp(r'^(?=.*[A-Z])').hasMatch(newPassword)) {
        passWordErrorText = AppStringConstants.passwordContainsOneUpperCase.tr;
      } else if (!RegExp(r'^(?=.*\d)').hasMatch(newPassword)) {
        passWordErrorText = AppStringConstants.passwordContainsOneDigit.tr;
      } else if (!RegExp(r'^(?=.*[!@#$%^&*(),.?":{}|<>])').hasMatch(newPassword)) {
        passWordErrorText = AppStringConstants.passwordContainsOneSpecialCharacter.tr;
      } else {
        passWordErrorText = '';
      }
    }

    if (newPasswordController.text != reEnterPasswordController.text) {
      rePassWordErrorText = AppStringConstants.bothPasswordShouldBeSame.tr;
    } else {
      rePassWordErrorText = '';
    }

    if (validationHelper.validateEmptyController(sixDigitCodeController)) {
      codeErrorText = AppStringConstants.pleaseEnterSixDigitCode.tr;
    } else if (sixDigitCodeController.text.length < 6) {
      codeErrorText = AppStringConstants.theCodeShouldBeSixLetterLong.tr;
    } else {
      codeErrorText = '';
    }

    if (rePassWordErrorText.isEmpty && passWordErrorText.isEmpty && codeErrorText.isEmpty) {
      resetPassword();
    }
  }

  void resetPassword() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    bool isChanged = await _pageState.controller.resetPassword(
      loginId: loginIdController.text,
      registerId: registeredEmailController.text,
      code: sixDigitCodeController.text,
      newPassword: newPasswordController.text,
      reEnterPassword: reEnterPasswordController.text,
    );
    if (isChanged) {
      apiStatus = ApiStatus.success;
      AppStringConstants.yourPasswordIsResetSuccessfully.tr.showSuccessToast();
      _pageState.controller.update();
    } else {
      apiStatus = ApiStatus.success;
      _pageState.controller.update();
    }
  }
}
