import 'package:ams/app_imports.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  State<SignUpPage> createState() => SignUpPageState();
}

class SignUpPageState extends State<SignUpPage> {
  SignupPageHelper? _pageHelper;
  late AuthenticationController controller;

  @override
  Widget build(BuildContext context) {
    _pageHelper = _pageHelper ?? (SignupPageHelper(this));
    return GetBuilder<AuthenticationController>(
      init: AuthenticationController(),
      builder: (AuthenticationController authenticationController) {
        controller = authenticationController;
        return AppScaffold(
          apiStatus: _pageHelper!.apiStatus,
          body: Column(
            children: [
              _appbarView(),
              _bodyView(),
            ],
          ),
        );
      },
    );
  }

  Widget _appbarView() {
    return Container(
      padding: EdgeInsets.only(top: MediaQuery.of(context).viewPadding.top, bottom: 12, left: 12, right: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColorConstants.colorDarkGrey),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 10,
          ),
          AppImageAsset(
            image: AppAssetsConstants.fssaiImage,
            width: 100,
          ),
          const SizedBox(width: 40),
          Expanded(
            child: AppImageAsset(image: AppAssetsConstants.auditManagementImage),
          ),
          const SizedBox(width: 10),
        ],
      ),
    );
  }

  Widget _bodyView() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(13),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(13),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(8),
                  topLeft: Radius.circular(8),
                ),
                border: Border.all(color: AppColorConstants.colorAppPrimary),
                color: AppColorConstants.colorAppPrimary,
              ),
              child: AppText(
                AppStringConstants.applicationFormForAuditingAgency.tr,
                color: AppColorConstants.colorWhite,
                fontSize: 16,
              ),
            ),
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(8), bottomRight: Radius.circular(8)),
                  border: Border.all(color: AppColorConstants.colorAppPrimary),
                ),
                child: ListView(
                  children: [
                    _registrationFormView(),
                    const SizedBox(height: 5),
                    _kindOfBusinessView(),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Column(
                children: [
                  AppButton(onTap: () => _pageHelper!.registrationValidation(), title: AppStringConstants.register.tr),
                  const SizedBox(height: 10),
                  Text.rich(
                    TextSpan(
                      text: '${AppStringConstants.alreadyRegistered.tr} ',
                      style: const TextStyle(color: AppColorConstants.colorDarkGrey),
                      children: <TextSpan>[
                        TextSpan(
                          text: AppStringConstants.logInHere.tr,
                          style: const TextStyle(color: AppColorConstants.colorBlue),
                          recognizer: TapGestureRecognizer()..onTap = () => gotoLoginPage(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _registrationFormView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        children: [
          commonTextField(
            controller: _pageHelper!.agencyNameController,
            hintText: AppStringConstants.nameOfAuditingAgency.tr,
            labelText: AppStringConstants.nameOfAuditingAgency.tr,
            errorText: _pageHelper!.agencyNameError.tr,
          ),
          commonTextField(
            controller: _pageHelper!.addressOfHeadOfficeOfCompanyController,
            hintText: AppStringConstants.address.tr,
            labelText: AppStringConstants.addressOfHeadOfficeOfCompanyAgency.tr,
            errorText: _pageHelper!.addressOfHeadOfficeOfCompanyError.tr,
          ),
          commonTextField(
            controller: _pageHelper!.pincodeController,
            hintText: AppStringConstants.pincode.tr,
            labelText: AppStringConstants.pincode.tr,
            errorText: _pageHelper!.pincodeError.tr,
            keyboardType: TextInputType.phone,
            maxLength: 6,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          ),
          const SizedBox(height: 2),
          AppDropdownButton(
            labelText: AppStringConstants.state.tr,
            hint: AppStringConstants.notSelected.tr,
            errorText: _pageHelper!.stateError.tr,
            buttonColor: AppColorConstants.colorWhite,
            value: _pageHelper!.selectedState,
            itemBuilder: _pageHelper!.stateList
                .map(
                  (item) => DropdownMenuItem(
                    value: item,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      width: double.infinity,
                      alignment: Alignment.centerLeft,
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: (item == _pageHelper!.stateList.last)
                              ? BorderSide.none
                              : const BorderSide(color: AppColorConstants.colorGrey, width: 0.5),
                        ),
                      ),
                      child: AppText(
                        item.stateName ?? '',
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        fontWeight: FontWeight.w400,
                        color: AppColorConstants.colorBlack.withOpacity(0.5),
                      ),
                    ),
                  ),
                )
                .toList(),
            selectedMenuItemBuilder: (context, child) {
              return Container(
                height: 40,
                width: double.infinity,
                color: AppColorConstants.colorAppPrimary,
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: AppText(
                    _pageHelper!.selectedState?.stateName ?? '',
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    fontWeight: FontWeight.w400,
                    color: AppColorConstants.colorWhite,
                  ),
                ),
              );
            },
            selectedItemBuilder: (context) {
              return _pageHelper!.stateList
                  .map(
                    (e) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: AppText(
                          e.stateName ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  )
                  .toList();
            },
            onChanged: (value) => _pageHelper!.stateOnChanged(value as StateModel),
          ),
          const SizedBox(height: 5),
          AppDropdownButton(
            labelText: AppStringConstants.district.tr,
            hint: AppStringConstants.notSelected.tr,
            errorText: _pageHelper!.districtError.tr,
            buttonColor: AppColorConstants.colorWhite,
            value: _pageHelper!.selectedDistrict,
            itemBuilder: _pageHelper!.districtList
                .map(
                  (item) => DropdownMenuItem(
                value: item,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  width: double.infinity,
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: (item == _pageHelper!.districtList.last)
                          ? BorderSide.none
                          : const BorderSide(color: AppColorConstants.colorGrey, width: 0.5),
                    ),
                  ),
                  child: AppText(
                    item.districtName ?? '',
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    fontWeight: FontWeight.w400,
                    color: AppColorConstants.colorBlack.withOpacity(0.5),
                  ),
                ),
              ),
            )
                .toList(),
            selectedMenuItemBuilder: (context, child) {
              return Container(
                height: 40,
                width: double.infinity,
                color: AppColorConstants.colorAppPrimary,
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: AppText(
                    _pageHelper!.selectedDistrict?.districtName ?? '',
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    fontWeight: FontWeight.w400,
                    color: AppColorConstants.colorWhite,
                  ),
                ),
              );
            },
            selectedItemBuilder: (context) {
              return _pageHelper!.districtList
                  .map(
                    (e) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: AppText(
                      e.districtName ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              )
                  .toList();
            },
            onChanged: (value) => _pageHelper!.districtOnChanged(value as DistrictModel),
          ),
          const SizedBox(height: 5),
          commonTextField(
            controller: _pageHelper!.nameOfContactPersonController,
            hintText: AppStringConstants.nameOfContactPerson.tr,
            labelText: AppStringConstants.nameOfContactPersonAsProvided.tr,
            errorText: _pageHelper!.nameOfContactPersonError.tr,
          ),
          commonTextField(
            controller: _pageHelper!.mobileNumberController,
            hintText: AppStringConstants.mobileNo.tr,
            labelText: AppStringConstants.mobile.tr,
            errorText: _pageHelper!.mobileNumberError.tr,
            keyboardType: TextInputType.phone,
            maxLength: 10,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          ),
          commonTextField(
            controller: _pageHelper!.tellNumberController,
            hintText: AppStringConstants.telNo.tr,
            labelText: AppStringConstants.telNo.tr,
            errorText: _pageHelper!.tellNumberError.tr,
            keyboardType: TextInputType.phone,
            maxLength: 10,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          ),
          commonTextField(
            controller: _pageHelper!.emailController,
            hintText: AppStringConstants.email.tr,
            labelText: AppStringConstants.email.tr,
            errorText: _pageHelper!.emailError.tr,
            keyboardType: TextInputType.emailAddress,
          ),
          commonTextField(
            controller: _pageHelper!.websiteController,
            hintText: AppStringConstants.website.tr,
            labelText: AppStringConstants.website.tr,
            errorText: '',
          ),
          commonTextField(
            controller: _pageHelper!.accreditationCertificateNoController,
            hintText: AppStringConstants.accreditationCertificateNo.tr,
            labelText: AppStringConstants.accreditationCertificateNoProvidedByAgency.tr,
            errorText: _pageHelper!.accreditationCertificateNoError.tr,
          ),
          commonTextField(
            controller: _pageHelper!.validityOfCertificateOfRecognitionDateController,
            labelText: AppStringConstants.validityOfCertificateOfRecognitionIssuedByFssai.tr,
            hintText: AppStringConstants.mmDdYyyy,
            errorText: _pageHelper!.validityOfCertificateOfRecognitionDateError.tr,
            suffixIcon: AppAssetsConstants.icCalender,
            readOnly: true,
            onTap: () => _pageHelper!.pickValidUpToDate(),
          ),
          commonTextField(
            controller: _pageHelper!.numberOfAuditorController,
            hintText: AppStringConstants.totalNumberOfAuditors.tr,
            labelText: AppStringConstants.numberOfAuditor.tr,
            errorText: _pageHelper!.numberOfAuditorError.tr,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            keyboardType: TextInputType.phone,
          ),
          commonTextField(
            controller: _pageHelper!.fssaiRecognitionController,
            labelText: '${AppStringConstants.fssaiRecognitionNumber.tr}*',
            hintText: AppStringConstants.recognitionNumHint.tr,
            errorText: _pageHelper!.fssaiRecognitionError.tr,
          ),
          commonTextField(
            controller: _pageHelper!.legalEntityStatusController,
            labelText: '${AppStringConstants.legalEntityStatus.tr}${AppStringConstants.proprietorPartnerLlp.tr}',
            hintText: AppStringConstants.legalEntityStatusHint.tr,
            errorText: _pageHelper!.legalEntityStatusError.tr,
          ),
          commonTextField(
            controller: _pageHelper!.scopeOfAuditingController,
            labelText: AppStringConstants.scopeOfAuditing.tr,
            hintText: '',
            textInputAction: TextInputAction.newline,
            maxLines: 5,
            textFieldHeight: 70,
            errorText: _pageHelper!.scopeOfAuditingError.tr,
          ),
          commonTextField(
            controller: _pageHelper!.geographicalAreaController,
            labelText: AppStringConstants.geographicalAreasWhereTheyCanAudit.tr,
            hintText: '',
            textInputAction: TextInputAction.newline,
            maxLines: 5,
            textFieldHeight: 70,
            errorText: _pageHelper!.geographicalAreaError.tr,
          ),
        ],
      ),
    );
  }

  Widget _kindOfBusinessView() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
          color: AppColorConstants.colorAppPrimary,
          child: AppText(
            AppStringConstants.chooseKOB.tr,
            color: AppColorConstants.colorWhite,
            fontSize: 14,
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: _pageHelper!.kobDetailsList
                .map(
                  (e) => kobView(e),
                )
                .toList(),
          ),
        ),
      ],
    );
  }

  Widget kobView(KobDetailsModel kobModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: AppText(
            kobModel.kobMasterName,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        for (var item in kobModel.kobDetails)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 3),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () => _pageHelper!.selectKob(item),
                  child: Padding(
                    padding: const EdgeInsets.only(right: 5),
                    child: Icon(
                      (item.isSelected) ? Icons.check_box_rounded : Icons.check_box_outline_blank_rounded,
                    ),
                  ),
                ),
                Expanded(
                  child: AppText(
                    item.kobDetailName,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget commonTextField({
    required TextEditingController controller,
    required String hintText,
    required String labelText,
    required String errorText,
    int maxLines = 1,
    int? maxLength,
    bool readOnly = false,
    GestureTapCallback? onTap,
    String? suffixIcon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    TextInputAction? textInputAction,
    double textFieldHeight = 52.0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(labelText),
        const SizedBox(height: 2),
        AppTextFormField(
          textFieldHeight: textFieldHeight,
          controller: controller,
          hintText: hintText,
          maxLines: maxLines,
          readOnly: readOnly,
          onTap: onTap,
          errorText: errorText,
          maxLength: maxLength,
          textInputAction: textInputAction,
          suffixIcon: (suffixIcon != null)
              ? Padding(
                  padding: const EdgeInsets.all(15),
                  child: AppImageAsset(image: suffixIcon),
                )
              : null,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
        ),
        const SizedBox(height: 5),
      ],
    );
  }
}
