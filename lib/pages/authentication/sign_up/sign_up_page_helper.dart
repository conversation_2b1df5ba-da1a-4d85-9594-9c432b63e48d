import 'package:ams/app_imports.dart';

class SignupPageHelper {
  final SignUpPageState _pageState;

  final TextEditingController agencyNameController = TextEditingController();
  final TextEditingController addressOfHeadOfficeOfCompanyController = TextEditingController();
  final TextEditingController pincodeController = TextEditingController();
  final TextEditingController nameOfContactPersonController = TextEditingController();
  final TextEditingController mobileNumberController = TextEditingController();
  final TextEditingController tellNumberController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController websiteController = TextEditingController();
  final TextEditingController accreditationCertificateNoController = TextEditingController();
  final TextEditingController validityOfCertificateOfRecognitionDateController = TextEditingController();
  final TextEditingController numberOfAuditorController = TextEditingController();
  final TextEditingController fssaiRecognitionController = TextEditingController();
  final TextEditingController legalEntityStatusController = TextEditingController();
  final TextEditingController scopeOfAuditingController = TextEditingController();
  final TextEditingController geographicalAreaController = TextEditingController();

  StateModel? selectedState;
  DistrictModel? selectedDistrict;
  DateTime? selectedDate;

  String agencyNameError = '';
  String addressOfHeadOfficeOfCompanyError = '';
  String pincodeError = '';
  String stateError = '';
  String districtError = '';
  String nameOfContactPersonError = '';
  String mobileNumberError = '';
  String tellNumberError = '';
  String emailError = '';
  String accreditationCertificateNoError = '';
  String validityOfCertificateOfRecognitionDateError = '';
  String numberOfAuditorError = '';
  String fssaiRecognitionError = '';
  String legalEntityStatusError = '';
  String scopeOfAuditingError = '';
  String geographicalAreaError = '';

  ValidationHelper validationHelper = ValidationHelper.instance;

  List<KobDetailsModel> kobDetailsList = [];

  List<StateModel> stateList = [];
  List<DistrictModel> districtList = [];

  ApiStatus apiStatus = ApiStatus.initial;

  SignupPageHelper(this._pageState) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () {
        getData();
      },
    );
  }

  Future<void> getData() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    stateList = await _pageState.controller.getStateListData();
    kobDetailsList = await _pageState.controller.getKobDetailsData();
    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }

  Future<void> getDistrictData() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    districtList = await _pageState.controller.getDistrictListData(selectedState?.stateId ?? 0);
    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }

  void stateOnChanged(StateModel value) {
    selectedState = value;
    selectedDistrict = null;
    getDistrictData();
    _pageState.controller.update();
  }

  void districtOnChanged(DistrictModel value) {
    selectedDistrict = value;
    _pageState.controller.update();
  }

  void registrationValidation() {
    if (validationHelper.validateEmptyController(agencyNameController)) {
      agencyNameError = AppStringConstants.plzEnterNameOfAuditingAgency;
    } else {
      agencyNameError = '';
    }
    if (validationHelper.validateEmptyController(addressOfHeadOfficeOfCompanyController)) {
      addressOfHeadOfficeOfCompanyError = AppStringConstants.plzEnterAddressOfHeadOfficeOfCompanyAgency;
    } else {
      addressOfHeadOfficeOfCompanyError = '';
    }
    if (validationHelper.validateEmptyController(pincodeController)) {
      pincodeError = AppStringConstants.plzEnterPinCode;
    } else if (validationHelper.lengthValidator(pincodeController, 6)) {
      pincodeError = AppStringConstants.plzEnterValidPinCode;
    } else {
      pincodeError = '';
    }
    if (selectedState == null) {
      stateError = AppStringConstants.plzSelectState;
    } else {
      stateError = '';
    }
    if (selectedDistrict == null) {
      districtError = AppStringConstants.plzSelectDistrict;
    } else {
      districtError = '';
    }
    if (validationHelper.validateEmptyController(nameOfContactPersonController)) {
      nameOfContactPersonError = AppStringConstants.plzEnterNameOfContactPerson;
    } else {
      nameOfContactPersonError = '';
    }
    if (validationHelper.validateEmptyController(mobileNumberController)) {
      mobileNumberError = AppStringConstants.plzEnterMobileNumber;
    } else if (validationHelper.lengthValidator(mobileNumberController, 10)) {
      mobileNumberError = AppStringConstants.plzEnterValidMobileNumber;
    } else {
      mobileNumberError = '';
    }
    if (validationHelper.validateEmptyController(tellNumberController)) {
      tellNumberError = AppStringConstants.plzEnterTellNumber;
    } else if (validationHelper.lengthValidator(tellNumberController, 10)) {
      tellNumberError = AppStringConstants.plzEnterValidTellNumber;
    } else {
      tellNumberError = '';
    }
    if (validationHelper.validateEmptyController(emailController)) {
      emailError = AppStringConstants.plzEnterEmail;
    } else if (validationHelper.regexValidator(emailController, validationHelper.emailRegExp)) {
      emailError = AppStringConstants.plzEnterValidEmail;
    } else {
      emailError = '';
    }
    if (validationHelper.validateEmptyController(accreditationCertificateNoController)) {
      accreditationCertificateNoError = AppStringConstants.plzEnterAccreditationCertificateNo;
    } else {
      accreditationCertificateNoError = '';
    }
    if (validationHelper.validateEmptyController(validityOfCertificateOfRecognitionDateController)) {
      validityOfCertificateOfRecognitionDateError = AppStringConstants.plzPickValidityOfCertificateOfRecognitionDate;
    } else {
      validityOfCertificateOfRecognitionDateError = '';
    }
    if (validationHelper.validateEmptyController(numberOfAuditorController)) {
      numberOfAuditorError = AppStringConstants.plzEnterNumOfAuditor;
    } else {
      numberOfAuditorError = '';
    }
    if (validationHelper.validateEmptyController(fssaiRecognitionController)) {
      fssaiRecognitionError = AppStringConstants.plzEnterFssaiRecognitionNumber;
    } else {
      fssaiRecognitionError = '';
    }
    if (validationHelper.validateEmptyController(legalEntityStatusController)) {
      legalEntityStatusError = AppStringConstants.plzEnterLegalEntityStatus;
    } else {
      legalEntityStatusError = '';
    }
    if (validationHelper.validateEmptyController(scopeOfAuditingController)) {
      scopeOfAuditingError = AppStringConstants.plzEnterScopeOfAuditing;
    } else {
      scopeOfAuditingError = '';
    }
    if (validationHelper.validateEmptyController(geographicalAreaController)) {
      geographicalAreaError = AppStringConstants.plzEnterGeographicalAreasWhereTheyCanAudit;
    } else {
      geographicalAreaError = '';
    }
    if (agencyNameError.isEmpty &&
        addressOfHeadOfficeOfCompanyError.isEmpty &&
        pincodeError.isEmpty &&
        stateError.isEmpty &&
        nameOfContactPersonError.isEmpty &&
        mobileNumberError.isEmpty &&
        tellNumberError.isEmpty &&
        emailError.isEmpty &&
        accreditationCertificateNoError.isEmpty &&
        validityOfCertificateOfRecognitionDateError.isEmpty &&
        numberOfAuditorError.isEmpty &&
        fssaiRecognitionError.isEmpty &&
        legalEntityStatusError.isEmpty &&
        scopeOfAuditingError.isEmpty &&
        geographicalAreaError.isEmpty) {
      if (kobDetailsList.every(
        (element) => element.kobDetails.every(
          (element) => element.isSelected == false,
        ),
      )) {
        AppStringConstants.plzChooseOneKOB.tr.showRequiredToast();
      } else {
        List<KOb> kobList = [];
        for (var element in kobDetailsList) {
          element.kobDetails
              .where(
                (element) => element.isSelected,
              )
              .toList()
              .forEach(
            (kobDetails) {
              kobList.add(
                KOb(
                  kobRegistrationId: null,
                  auditAgencyRegistrationId: element.kobMasterId,
                  kobId: kobDetails.kobDetailId,
                  agencyRegistration: null,
                ),
              );
            },
          );
        }

        AgencyRegistrationModel agencyRegistrationModel = AgencyRegistrationModel(
          scopeOfAuditing: scopeOfAuditingController.text,
          geographicalArea: geographicalAreaController.text,
          agencyName: agencyNameController.text,
          address: addressOfHeadOfficeOfCompanyController.text,
          status: 'N',
          activeFlag: 'Deactive',
          emailFlag: 'N',
          feeStatus: 'N',
          secondFeeStatus: 'N',
          verifyStatus: 'N',
          validUpTo: selectedDate!,
          state: selectedState!.stateId ?? 0,
          district: selectedDistrict!.stateId ?? 0,
          pincode: pincodeController.text,
          contactPersonName: nameOfContactPersonController.text,
          mobile: mobileNumberController.text,
          phone: tellNumberController.text,
          email: emailController.text,
          website: websiteController.text,
          legalEntryStatus: legalEntityStatusController.text,
          certificateNumber: accreditationCertificateNoController.text,
          numberOfAuditor: int.parse(numberOfAuditorController.text),
          recognitionNumber: fssaiRecognitionController.text,
          steps: 1,
          auditAgencyRegistrationId: null,
          kOb: kobList,
        );
        logs('agencyRegistrationModel ---> ${agencyRegistrationModel.toJson()}');
        gotoUploadDocumentPage(agencyRegistrationModel);
      }
    }
    _pageState.controller.update();
  }

  void selectKob(KobDetail item) {
    item.isSelected = !item.isSelected;
    _pageState.controller.update();
  }

  Future<void> pickValidUpToDate() async {
    DateTime? date = await appDatePicker(_pageState.context);
    if (date != null) {
      validityOfCertificateOfRecognitionDateController.text = DateHelper.getMMDDYYYYFormatDate(date);
      selectedDate = date;
      _pageState.controller.update();
    }
  }
}
