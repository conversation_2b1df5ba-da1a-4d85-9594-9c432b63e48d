import 'package:ams/app_imports.dart';

class UploadDocumentPageHelper {
  final UploadDocumentPageState _pageState;

  File? accereditationFile;
  File? agreementFile;
  File? assesmentReportFile;
  File? leadAuditorCourseFile;
  File? legalEnterStatusFile;
  File? otherDocumentFile;

  bool isButtonActive = false;
  ApiStatus apiStatus = ApiStatus.initial;

  UploadDocumentPageHelper(this._pageState);

  Future<void> pickAccereditationFile(File? file) async {
    File? pickFile = await FilePickerHelper.instance.pickFiles(
      _pageState.context,
    );
    if (pickFile != null) {
      accereditationFile = pickFile;
      _pageState.controller.update();
    }
    activeButton();
  }

  Future<void> pickAgreementFile(File? file) async {
    File? pickFile = await FilePickerHelper.instance.pickFiles(
      _pageState.context,
    );
    if (pickFile != null) {
      agreementFile = pickFile;
      _pageState.controller.update();
    }
    activeButton();
  }

  Future<void> pickAssesmentReportFile(File? file) async {
    File? pickFile = await FilePickerHelper.instance.pickFiles(
      _pageState.context,
    );
    if (pickFile != null) {
      assesmentReportFile = pickFile;
      _pageState.controller.update();
    }
    activeButton();
  }

  Future<void> pickLeadAuditorCourseFile(File? file) async {
    File? pickFile = await FilePickerHelper.instance.pickFiles(
      _pageState.context,
    );
    if (pickFile != null) {
      leadAuditorCourseFile = pickFile;
      _pageState.controller.update();
    }
    activeButton();
  }

  Future<void> pickLegalEnterStatusFile(File? file) async {
    File? pickFile = await FilePickerHelper.instance.pickFiles(
      _pageState.context,
    );
    if (pickFile != null) {
      legalEnterStatusFile = pickFile;
      _pageState.controller.update();
    }
    activeButton();
  }

  Future<void> pickOtherDocumentFile(File? file) async {
    File? pickFile = await FilePickerHelper.instance.pickFiles(
      _pageState.context,
    );
    if (pickFile != null) {
      otherDocumentFile = pickFile;
      _pageState.controller.update();
    }
    activeButton();
  }

  void activeButton() {
    isButtonActive = accereditationFile != null &&
        agreementFile != null &&
        assesmentReportFile != null &&
        leadAuditorCourseFile != null &&
        legalEnterStatusFile != null;
    _pageState.controller.update();
  }

  Future<void> saveOnTap() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    String response = await _pageState.controller.registerAgency(
      registrationModel: _pageState.widget.agencyRegistrationModel,
      certificateFilePath: accereditationFile?.path ?? '',
      confidentialFilePath: agreementFile?.path ?? '',
      accreditationFilePath: assesmentReportFile?.path ?? '',
      leadAuditorFilePath: leadAuditorCourseFile?.path ?? '',
      legalEnterStatusFilePath: legalEnterStatusFile?.path ?? '',
      otherFilePath: otherDocumentFile?.path ?? '',
    );
    apiStatus = ApiStatus.success;
    _pageState.controller.update();
    if (response.isNotEmpty) {
      gotoLoginPage(message: response);
    }
  }
}
