import 'package:ams/app_imports.dart';

class UploadDocumentPage extends StatefulWidget {
  final AgencyRegistrationModel agencyRegistrationModel;

  const UploadDocumentPage({required this.agencyRegistrationModel, super.key});

  @override
  State<UploadDocumentPage> createState() => UploadDocumentPageState();
}

class UploadDocumentPageState extends State<UploadDocumentPage> {
  UploadDocumentPageHelper? _pageHelper;
  late AuthenticationController controller;

  @override
  Widget build(BuildContext context) {
    _pageHelper = _pageHelper ?? (UploadDocumentPageHelper(this));
    return GetBuilder<AuthenticationController>(
      init: AuthenticationController(),
      builder: (AuthenticationController authenticationController) {
        controller = authenticationController;
        return AppScaffold(
          apiStatus: _pageHelper!.apiStatus,
          body: Column(
            children: [
              _appbarView(),
              _bodyView(),
            ],
          ),
        );
      },
    );
  }

  Widget _appbarView() {
    return Container(
      padding: EdgeInsets.only(top: MediaQuery.of(context).viewPadding.top, bottom: 12, left: 12, right: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColorConstants.colorDarkGrey),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 10,
          ),
          AppImageAsset(
            image: AppAssetsConstants.fssaiImage,
            width: 100,
          ),
          const SizedBox(width: 40),
          Expanded(
            child: AppImageAsset(image: AppAssetsConstants.auditManagementImage),
          ),
          const SizedBox(width: 10),
        ],
      ),
    );
  }

  Widget _bodyView() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(13),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(13),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(8),
                  topLeft: Radius.circular(8),
                ),
                border: Border.all(color: AppColorConstants.colorAppPrimary),
                color: AppColorConstants.colorAppPrimary,
              ),
              child: Row(
                children: [
                  AppIconButton(
                    onTap: () => gotoBack(),
                    iconImage: AppAssetsConstants.icBack,
                    iconColor: AppColorConstants.colorWhite,
                    buttonColor: Colors.transparent,
                    buttonHeight: 20,
                    buttonWidth: 20,
                  ),
                  Expanded(
                    child: AppText(
                      AppStringConstants.uploadDocument.tr,
                      color: AppColorConstants.colorWhite,
                      fontSize: 16,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(8), bottomRight: Radius.circular(8)),
                    border: Border.all(color: AppColorConstants.colorAppPrimary),
                  ),
                  child: _uploadDocView(),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Column(
                children: [
                  AppButton(
                    onTap: () => _pageHelper!.saveOnTap(),
                    title: AppStringConstants.uploadAndSave.tr,
                    isActive: _pageHelper!.isButtonActive,
                  ),
                  const SizedBox(height: 10),
                  AppText(AppStringConstants.pressHereToUploadFile.tr),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _uploadDocView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStringConstants.allDocAreMandatoryExpectNo6.tr,
          color: AppColorConstants.colorCoralRed,
        ),
        const SizedBox(height: 10),
        documentTile(
          '1. ${AppStringConstants.certificateOfAccereditation.tr}',
          _pageHelper!.accereditationFile,
          () => _pageHelper!.pickAccereditationFile(_pageHelper!.accereditationFile),
        ),
        documentTile(
          '2. ${AppStringConstants.confidentialityAgreement.tr}',
          _pageHelper!.agreementFile,
          () => _pageHelper!.pickAgreementFile(_pageHelper!.accereditationFile),
        ),
        documentTile(
          '3. ${AppStringConstants.aCopyOfTheLastAssessmentReport.tr}',
          _pageHelper!.assesmentReportFile,
          () => _pageHelper!.pickAssesmentReportFile(_pageHelper!.accereditationFile),
        ),
        documentTile(
          '4. ${AppStringConstants.leadAuditorCourseCertificate.tr}',
          _pageHelper!.leadAuditorCourseFile,
          () => _pageHelper!.pickLeadAuditorCourseFile(_pageHelper!.accereditationFile),
        ),
        documentTile(
          '5. ${AppStringConstants.legalEnterStatus.tr}*',
          _pageHelper!.legalEnterStatusFile,
          () => _pageHelper!.pickLegalEnterStatusFile(_pageHelper!.accereditationFile),
        ),
        documentTile(
          '6. ${AppStringConstants.anyOtherDocument.tr}',
          _pageHelper!.otherDocumentFile,
          () => _pageHelper!.pickOtherDocumentFile(_pageHelper!.accereditationFile),
        ),
      ],
    );
  }

  Widget documentTile(String title, File? file, GestureTapCallback onTap) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppText(title),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              color: AppColorConstants.colorWhite,
              border: Border.all(color: AppColorConstants.colorGrey),
              borderRadius: BorderRadius.circular(5),
            ),
            child: Row(
              children: [
                AppButton(
                  onTap: onTap,
                  height: 24,
                  width: 110,
                  borderRadius: BorderRadius.circular(5),
                  title: AppStringConstants.chooseFile.tr,
                  textSize: 12,
                  border: Border.all(color: AppColorConstants.colorGrey),
                  backColor: AppColorConstants.colorLightGrey,
                  titleColor: AppColorConstants.colorBlack,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: AppText(
                      (file == null) ? AppStringConstants.noFileChoosen.tr : file.path.split('/').last,
                      color: (file == null) ? AppColorConstants.colorGrey : AppColorConstants.colorBlack,
                      fontWeight: FontWeight.w400,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
