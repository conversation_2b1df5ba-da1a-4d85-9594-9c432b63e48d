import 'package:ams/app_imports.dart';

class LoginPage extends StatefulWidget {
  final String message;

  const LoginPage({required this.message, super.key});

  @override
  State<LoginPage> createState() => LoginPageState();
}

class LoginPageState extends State<LoginPage> {
  LoginPageHelper? loginHelper;
  late AuthenticationController controller;

  @override
  Widget build(BuildContext context) {
    loginHelper = loginHelper ?? (LoginPageHelper(this));
    return GetBuilder<AuthenticationController>(
      init: AuthenticationController(),
      builder: (AuthenticationController authenticationController) {
        controller = authenticationController;
        return AppScaffold(
          isIconDark: false,
          apiStatus: loginHelper!.apiStatus,
          statusBarColor: AppColorConstants.colorAppPrimary,
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return Stack(
      children: [
        Container(
          color: AppColorConstants.colorAppPrimary,
          width: double.infinity,
          height: double.infinity,
        ),
        AppImageAsset(
          image: AppAssetsConstants.splashBgImage,
          fit: BoxFit.cover,
          width: double.infinity,
        ),
        Center(
          child: _loginForm(),
        ),
      ],
    );
  }

  Widget _loginForm() {
    return Padding(
      padding: const EdgeInsets.all(15),
      child: SingleChildScrollView(
        child: Container(
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite.withOpacity(0.55),
            border: Border.all(color: AppColorConstants.colorWhite, width: 1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppImageAsset(image: AppAssetsConstants.fssaiImage),
                const SizedBox(
                  height: 20,
                ),
                if(widget.message.isNotEmpty)
                Container(
                  color: AppColorConstants.colorWhite,
                  margin: const EdgeInsets.symmetric(vertical: 5),
                  padding: const EdgeInsets.all(10),
                  child: AppText(
                    widget.message,
                    color: AppColorConstants.colorLightGreen,
                    fontWeight: FontWeight.w600,
                    textAlign: TextAlign.center,
                  ),
                ),
                AppTextFormField(
                  contentPadding: const EdgeInsets.only(bottom: 13),
                  prefixIconConstraints: const BoxConstraints(minWidth: 20,maxHeight: 20),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.all(5).copyWith(bottom: 0),
                    child: AppImageAsset(
                      width: 20,height: 20,
                      fit: BoxFit.contain,
                      image: AppAssetsConstants.icMail,
                    ),
                  ),
                  textFieldColor: AppColorConstants.colorWhite,
                  hintColor: AppColorConstants.colorDarkGrey,
                  controller: loginHelper!.userNameController,
                  hintText: AppStringConstants.userName.tr,
                  errorText: loginHelper!.usernameErrorText,
                  errorBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColorConstants.colorRed),
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(5), topRight: Radius.circular(5)),
                  ),
                  border: const UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColorConstants.colorAppPrimary),
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(5), topRight: Radius.circular(5)),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                AppTextFormField(
                  prefixIconConstraints: const BoxConstraints(minWidth: 35,maxHeight: 35),
                  contentPadding: const EdgeInsets.only(bottom: 13),
                  obscureText: true,
                  maxLines: 1,
                  prefixIcon: Padding(
                    padding: const EdgeInsets.only(top: 5),
                    child: AppImageAsset(
                      image: AppAssetsConstants.icLock,
                    ),
                  ),
                  textFieldColor: AppColorConstants.colorWhite,
                  hintColor: AppColorConstants.colorDarkGrey,
                  controller: loginHelper!.passwordController,
                  hintText: AppStringConstants.password.tr,
                  errorText: loginHelper!.passwordErrorText,
                  errorBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColorConstants.colorRed),
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(5), topRight: Radius.circular(5)),
                  ),
                  border: const UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColorConstants.colorAppPrimary),
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(5), topRight: Radius.circular(5)),
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                AppButton(
                  onTap: () => loginHelper!.login(),
                  title: AppStringConstants.logIn.tr,
                ),
                const SizedBox(
                  height: 15,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: () => gotoSignupPage(),
                      child: AppText(
                        AppStringConstants.signUp.tr,
                        fontSize: 16,
                      ),
                    ),
                    InkWell(
                      onTap: () => gotoForgotPasswordPage(),
                      child: AppText(
                        AppStringConstants.forgotPassword.tr,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
