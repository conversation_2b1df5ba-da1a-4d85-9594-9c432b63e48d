import 'package:ams/app_imports.dart';

class LoginPageHelper {
  final LoginPageState _pageState;
  TextEditingController userNameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  ApiStatus apiStatus = ApiStatus.initial;
  final ValidationHelper validationHelper = ValidationHelper.instance;

  String usernameErrorText = '';
  String passwordErrorText = '';

  LoginPageHelper(this._pageState);

  Future<void> login() async {
    FocusScope.of(_pageState.context).unfocus();
    if (validationHelper.validateEmptyController(userNameController)) {
      usernameErrorText = AppStringConstants.pleaseEnterYourUserName.tr;
    } else {
      usernameErrorText = '';
    }
    if (validationHelper.validateEmptyController(passwordController)) {
      passwordErrorText = AppStringConstants.pleaseEnterPassword.tr;
    } else {
      passwordErrorText = '';
    }
    _pageState.controller.update();
    if (passwordErrorText.isEmpty && usernameErrorText.isEmpty) {
      apiStatus = ApiStatus.loading;
      _pageState.controller.login(userNameController.text, passwordController.text).then((value) async {
        if (value) {
          AppStringConstants.logInSuccessMsg.tr.showSuccessToast();
          int profileId = await getPrefIntValue(AppPrefConstants.profileId) ?? 0;
          if (profileId == 1) {
            gotoSuperAdminDashboardPage();
          } else if (profileId == 2) {
            gotoAgencyDashboardPage();
          } else if (profileId == 3) {
            gotoAuditorDashboardPage();
          }
        }
        apiStatus = ApiStatus.success;
        _pageState.controller.update();
      });
    }
  }
}
