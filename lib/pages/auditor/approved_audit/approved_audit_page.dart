import 'package:ams/app_imports.dart';

class AuditorApprovedAuditPage extends StatefulWidget {
  const AuditorApprovedAuditPage({super.key});

  @override
  State<AuditorApprovedAuditPage> createState() => AuditorApprovedAuditPageState();
}

class AuditorApprovedAuditPageState extends State<AuditorApprovedAuditPage> {
  AuditorApprovedAuditPageHelper? _helper;
  late AuditorApprovedAuditController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? (AuditorApprovedAuditPageHelper(this));
    return GetBuilder<AuditorApprovedAuditController>(
      init: AuditorApprovedAuditController(),
      builder: (AuditorApprovedAuditController approvedAuditController) {
        controller = approvedAuditController;
        return AppScaffold(
          appBar: CommonAppBar(title: AppStringConstants.approvedAuditReports.tr),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return tableCard(
      fromDateController: _helper!.fromDateController,
      toDateController: _helper!.toDateController,
      fromDateOnTap: () => _helper!.dateOnTap(_helper!.fromDateController),
      toDateOnTap: () => _helper!.dateOnTap(_helper!.toDateController),
      dropDownOnChanged: (value) => _helper!.sortTypeOnChanged(value as String),
      dropdownList: _helper!.dropdownList,
      dropdownValue: _helper!.sortTypeValue.isEmpty ? null : _helper!.sortTypeValue,
      searchController: _helper!.searchController,
      isDateRangeShow: true,
      isShowDropdown: true,
      isShowSearch: true,
      isAscending: _helper!.isAscending,
      sortOnTap: () => _helper!.sortOnTap(),
      children: [
        const SizedBox(height: 10),
        for (int index = 0; index < _helper!.approvedAuditReportViewList.length; index++)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Table(
              border: TableBorder.all(
                color: AppColorConstants.colorMediumGrey,
                borderRadius: BorderRadius.circular(5),
              ),
              children: [
                for (var entry in _helper!.approvedAuditReportViewList[index].toJson().entries)
                  TableRow(
                    children: [
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.centerLeft,
                          color: AppColorConstants.colorLightGrey,
                          child: AppText(
                            entry.key.tr,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      (entry.key == AppStringConstants.report)
                          ? TableCell(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AppButton(
                                      onTap: () => gotoInspectionReportPage(),
                                      title: AppStringConstants.view.tr,
                                      borderRadius: BorderRadius.circular(5),
                                      textSize: 12,
                                      width: 80,
                                      height: 30,
                                    ),
                                    const SizedBox(height: 8),
                                    AppButton(
                                      onTap: () => _followUpReportDialog(),
                                      title: AppStringConstants.uploadFollowUpReport.tr,
                                      backColor: AppColorConstants.colorYellow,
                                      borderRadius: BorderRadius.circular(5),
                                      textSize: 10,
                                      height: 30,
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : TableCell(
                              verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                child: AppText(
                                  (entry.key == AppStringConstants.submitDate)
                                      ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                      : '${entry.value ?? ''}',
                                  fontSize: 12,
                                  color: AppColorConstants.colorBlack,
                                ),
                              ),
                            ),
                    ],
                  ),
              ],
            ),
          ),
      ],
    );
  }

  Future<dynamic> _followUpReportDialog() {
    _helper!.followUpFile = null;
    return appDialog(
      context: context,
      title: AppStringConstants.uploadFollowUpReport.tr,
      buttonText: AppStringConstants.submit.tr,
      bodyWidget: StatefulBuilder(
        builder: (context, setState) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText(
                '${AppStringConstants.followUpReport.tr} : ',
                fontWeight: FontWeight.w600,
              ),
              const SizedBox(height: 5),
              Row(
                children: [
                  AppButton(
                    onTap: () => _helper!.pickFollowUpFile(setState),
                    height: 24,
                    borderRadius: BorderRadius.circular(5),
                    title: AppStringConstants.chooseFile.tr,
                    textSize: 12,
                    width: 110,
                    border: Border.all(color: AppColorConstants.colorGrey),
                    backColor: AppColorConstants.colorLightGrey,
                    titleColor: AppColorConstants.colorBlack,
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: AppText(
                        (_helper!.followUpFile == null)
                            ? AppStringConstants.noFileChoosen.tr
                            : _helper!.followUpFile?.path.split('/').last ?? '',
                        color: (_helper!.followUpFile == null) ? AppColorConstants.colorGrey : AppColorConstants.colorBlack,
                        fontWeight: FontWeight.w400,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
      buttonTap: () => _helper!.submitFollowUpReport(),
    );
  }
}
