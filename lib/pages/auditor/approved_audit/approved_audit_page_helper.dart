import 'package:ams/app_imports.dart';
class AuditorApprovedAuditPageHelper {
  final AuditorApprovedAuditPageState _pageState;

  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.reportNo,
    AppStringConstants.fboName,
    AppStringConstants.licenseNo,
    AppStringConstants.stateDistrict,
    AppStringConstants.agency,
    AppStringConstants.auditorName,
    AppStringConstants.kob,
    AppStringConstants.submitDate,
  ];
  List<AuditorApprovedAuditViewModel> approvedAuditReportViewList = [
    AuditorApprovedAuditViewModel(
      agencyName: 'test',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      stateDistrict: 'Ahmedabad/Gujarat.',
      kob: 'test',
      submitDate: DateTime.now().subtract(const Duration(days: 15)),
      auditorName: 'test',
      reportNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 1,
    ),
    AuditorApprovedAuditViewModel(
      agencyName: 'test agencyName2',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      stateDistrict: 'Lakhnow/Madhya Pradesh.',
      kob: 'test',
      submitDate: DateTime.now().subtract(const Duration(days: 30)),
      auditorName: 'test auditorName2',
      reportNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 2,
    ),
    AuditorApprovedAuditViewModel(
      agencyName: 'test agencyName3',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      stateDistrict: 'Harina/Madhya Pradesh.',
      kob: 'test',
      submitDate: DateTime.now().subtract(const Duration(days: 1)),
      auditorName: 'test auditorName3',
      reportNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 3,
    ),
    AuditorApprovedAuditViewModel(
      agencyName: 'test agencyName4',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      stateDistrict: 'Lahor/Pubjab',
      kob: 'test',
      submitDate: DateTime.now().subtract(const Duration(days: 45)),
      auditorName: 'test auditorName4',
      reportNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 4,
    ),
  ];
  List<AuditorApprovedAuditViewModel> tmpList = [];

  File? followUpFile;

  String sortTypeValue = AppStringConstants.srNo;
  bool isAscending = true;

  TextEditingController searchController = TextEditingController();
  TextEditingController fromDateController = TextEditingController();
  TextEditingController toDateController = TextEditingController();

  AuditorApprovedAuditPageHelper(this._pageState) {
    tmpList = approvedAuditReportViewList;
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      if (sortTypeValue == AppStringConstants.submitDate) {
        approvedAuditReportViewList.sort(
          (a, b) => a.submitDate!.compareTo(b.submitDate ?? DateTime.now()),
        );
      } else {
        approvedAuditReportViewList.sort(
          (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
        );
      }
    } else {
      if (sortTypeValue == AppStringConstants.submitDate) {
        approvedAuditReportViewList.sort(
          (a, b) => b.submitDate!.compareTo(a.submitDate ?? DateTime.now()),
        );
      } else {
        approvedAuditReportViewList.sort(
          (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
        );
      }
    }
    _pageState.controller.update();
  }

  Future<void> dateOnTap(TextEditingController controller) async {
    DateTime? pickDate = await appDatePicker(_pageState.context);
    if (pickDate != null) {
      controller.text = DateHelper.getDDMMYYYYFormatDate(pickDate);
    }
    _pageState.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }

  Future<void> pickFollowUpFile(StateSetter setState) async {
    followUpFile = await FilePickerHelper.instance.pickFiles(_pageState.context);
    setState(() {});
  }

  void submitFollowUpReport() {
    if (followUpFile == null) {
      AppStringConstants.plzSelectFile.tr.showErrorToast();
    } else {
      gotoBack();
    }
  }
}
