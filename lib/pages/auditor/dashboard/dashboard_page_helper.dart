import 'package:ams/app_imports.dart';

class AuditorDashboardPageHelper {
  final AuditorDashboardPageState _pageState;

  List<Map<String, dynamic>> dashboardList = [
    {
      'icon': AppAssetsConstants.icApproved,
      'title': AppStringConstants.auditApproved,
      'value': '0',
      'color': AppColorConstants.colorLightGreen,
    },
    {
      'icon': AppAssetsConstants.icPeople,
      'title': AppStringConstants.approvalPending,
      'value': '0',
      'color': AppColorConstants.colorLightGreen,
    },
    {
      'icon': AppAssetsConstants.icRejected,
      'title': AppStringConstants.nonCompliance,
      'value': '0',
      'color': AppColorConstants.colorLightRed,
    },
    {
      'icon': AppAssetsConstants.icRejected,
      'title': AppStringConstants.cancelled,
      'value': '0',
      'color': AppColorConstants.colorLightRed,
    },
    {
      'icon': AppAssetsConstants.icApplicationReceived,
      'title': AppStringConstants.applicationReceived,
      'value': '0',
      'color': AppColorConstants.colorBlue,
    },
  ];

  String selectedDrawerItem = AppStringConstants.dashboard;
  final List<Map<String, dynamic>> drawerItem = [
    {
      'icon': AppAssetsConstants.icDrawerDashboard,
      'title': AppStringConstants.dashboard,
    },
    {
      'icon': AppAssetsConstants.icPersonalInfo,
      'title': AppStringConstants.personalInfo,
    },
    {
      'icon': AppAssetsConstants.icChangePassword,
      'title': AppStringConstants.changePassword,
    },
    {
      'icon': AppAssetsConstants.icApproved,
      'title': AppStringConstants.auditApproved,
    },
    {
      'icon': AppAssetsConstants.icPeople,
      'title': AppStringConstants.approvalPending,
    },
    {
      'icon': AppAssetsConstants.icRejected,
      'title': AppStringConstants.nonCompliance,
    },
    {
      'icon': AppAssetsConstants.icRejected,
      'title': AppStringConstants.cancelled,
    },
    {
      'icon': AppAssetsConstants.icApplicationReceived,
      'title': AppStringConstants.applicationReceived,
    },
    {
      'icon': AppAssetsConstants.icLogOut,
      'title': AppStringConstants.logOut,
    },
  ];

  final GlobalKey<ScaffoldState> drawerKey = GlobalKey<ScaffoldState>();

  ApiStatus apiStatus = ApiStatus.initial;
  AuditorDashboardModel auditorDashboardModel = AuditorDashboardModel();

  AuditorDashboardPageHelper(this._pageState) {
    Future.delayed(const Duration(milliseconds: 10), () => getDashboardData());
  }

  Future<void> getDashboardData() async {
    apiStatus = ApiStatus.loading;
    _pageState.controller.update();
    auditorDashboardModel = await _pageState.controller.getDashboardData();
      dashboardList[0]['value'] = '${auditorDashboardModel.inspectionApproved}';
      dashboardList[1]['value'] = '${auditorDashboardModel.approvalPendingWithAgency}';
      dashboardList[2]['value'] = '${auditorDashboardModel.inspectionReportNonCompliance}';
      dashboardList[3]['value'] = '${auditorDashboardModel.auditRequestCancelled}';
      dashboardList[4]['value'] = '${auditorDashboardModel.inspectionConducted}';
    apiStatus = ApiStatus.success;
    _pageState.controller.update();
  }

  void drawerOnTap(String item) {
    drawerKey.currentState!.closeDrawer();
    selectedDrawerItem = item;
    Future.delayed(
      const Duration(milliseconds: 400),
      () {
        if (item == AppStringConstants.dashboard) {
          getDashboardData();
        } else if (item == AppStringConstants.personalInfo) {
          gotoAuditorPersonalInfoPage();
        } else if (item == AppStringConstants.changePassword) {
          gotoChangePasswordPage();
        } else if (item == AppStringConstants.auditApproved) {
          gotoAuditorApprovedAuditPage();
        } else if (item == AppStringConstants.approvalPending) {
          gotoAuditorApprovalPendingAuditPage();
        } else if (item == AppStringConstants.nonCompliance) {
          gotoAuditorNonCompliancePage();
        } else if (item == AppStringConstants.cancelled) {
          gotoAuditorCancelledAuditPage();
        } else if (item == AppStringConstants.applicationReceived) {
          gotoAuditorReceivedAuditPage();
        } else if (item == AppStringConstants.logOut) {
          logoutDialog(context: _pageState.context);
        }
      },
    );
    _pageState.controller.update();
  }

  void dashboardOnTap(int index) {
    switch (index) {
      case 0:
        gotoAuditorApprovedAuditPage();
        break;
      case 1:
        gotoAuditorApprovalPendingAuditPage();
        break;
      case 2:
        gotoAuditorNonCompliancePage();
        break;
      case 3:
        gotoAuditorCancelledAuditPage();
        break;
      case 4:
        gotoAuditorReceivedAuditPage();
        break;
    }
  }
}
