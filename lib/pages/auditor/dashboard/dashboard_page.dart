import 'package:ams/app_imports.dart';

class AuditorDashboardPage extends StatefulWidget {
  const AuditorDashboardPage({super.key});

  @override
  State<AuditorDashboardPage> createState() => AuditorDashboardPageState();
}

class AuditorDashboardPageState extends State<AuditorDashboardPage> {
  AuditorDashboardPageHelper? _helper;
  late AuditorDashboardController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? (AuditorDashboardPageHelper(this));
    return GetBuilder<AuditorDashboardController>(
      init: AuditorDashboardController(),
      builder: (AuditorDashboardController auditorDashboardController) {
        controller = auditorDashboardController;
        return AppScaffold(
          apiStatus: _helper!.apiStatus,
          scaffoldKey: _helper!.drawerKey,
          drawer: _drawerView(),
          appBar: _appBarView(),
          body: _bodyView(),
        );
      },
    );
  }

  CommonAppBar _appBarView() {
    return CommonAppBar(
      title: AppStringConstants.dashboard.tr,
      titleSize: 18,
      isBack: false,
      leading: Padding(
        padding: const EdgeInsets.only(left: 0, bottom: 15, top: 15, right: 0),
        child: InkWell(
          onTap: () => _helper!.drawerKey.currentState?.openDrawer(),
          child: AppImageAsset(
            image: AppAssetsConstants.icMenu,
            height: 10,
            width: 10,
          ),
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: AppImageAsset(
            image: AppAssetsConstants.fssaiImage,
            width: 110,
          ),
        ),
      ],
    );
  }

  Widget _bodyView() {
    return RefreshIndicator(
      onRefresh: () => _helper!.getDashboardData(),
      child: GridView.builder(
        itemCount: _helper!.dashboardList.length,
        padding: const EdgeInsets.all(12),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight(
          crossAxisCount: 2,
          height: 115,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemBuilder: (context, index) {
          return DashboardCard(
            text: _helper!.dashboardList[index]['title'].toString().tr,
            image: _helper!.dashboardList[index]['icon'],
            onTap: () => _helper!.dashboardOnTap(index),
            value: _helper!.dashboardList[index]['value'],
            color: _helper!.dashboardList[index]['color'],
          );
        },
      ),
    );
  }

  Drawer _drawerView() {
    return Drawer(
      backgroundColor: AppColorConstants.colorWhite,
      child: ListView(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                height: 180,
                width: double.infinity,
                color: AppColorConstants.colorAppPrimary,
                child: AppImageAsset(
                  image: AppAssetsConstants.agencyDrawerProfile,
                ),
              ),
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10).copyWith(bottom: 5),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColorConstants.colorWhite,
                    ),
                    child: ClipRRect(
                      child: AppImageAsset(
                        image: AppAssetsConstants.icProfile,
                        height: 45,
                        width: 30,
                      ),
                    ),
                  ),
                  const AppText(
                    'Welcome user',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColorConstants.colorWhite,
                  ),
                ],
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(22.0),
            child: Column(
              children: _helper!.drawerItem.map((item) {
                return _drawerTile(item['title'], item['icon']);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _drawerTile(String name, String image) {
    return InkWell(
      onTap: () => _helper!.drawerOnTap(name),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5),
        padding: EdgeInsets.only(
          top: 11,
          bottom: 11,
          left: (name == AppStringConstants.logOut) ? 15 : 10,
        ),
        width: double.infinity,
        height: 45,
        decoration: BoxDecoration(
          color: _helper!.selectedDrawerItem == name ? AppColorConstants.colorAppPrimary : AppColorConstants.colorWhite,
          borderRadius: BorderRadius.circular(100),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            AppImageAsset(
              image: image,
              height: 23,
              width: 23,
              color: _helper!.selectedDrawerItem == name
                  ? AppColorConstants.colorWhite
                  : name == AppStringConstants.logOut
                      ? AppColorConstants.colorRedAccent
                      : AppColorConstants.colorDarkGrey,
            ),
            const SizedBox(width: 15),
            AppText(
              name.tr,
              color: _helper!.selectedDrawerItem == name
                  ? AppColorConstants.colorWhite
                  : name == AppStringConstants.logOut
                      ? AppColorConstants.colorRedAccent
                      : AppColorConstants.colorDarkGrey,
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
          ],
        ),
      ),
    );
  }
}
