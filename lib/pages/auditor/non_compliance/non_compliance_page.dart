import 'package:ams/app_imports.dart';

class AuditorNonCompliancePage extends StatefulWidget {
  const AuditorNonCompliancePage({super.key});

  @override
  State<AuditorNonCompliancePage> createState() => AuditorNonCompliancePageState();
}

class AuditorNonCompliancePageState extends State<AuditorNonCompliancePage> {
  AuditorNonCompliancePageHelper? _helper;
  late AuditorNonComplianceController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? (AuditorNonCompliancePageHelper(this));
    return GetBuilder<AuditorNonComplianceController>(
      init: AuditorNonComplianceController(),
      builder: (AuditorNonComplianceController nonComplianceController) {
        return AppScaffold(
          appBar: CommonAppBar(title: AppStringConstants.nonCompliance.tr),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return tableCard(
      dropDownOnChanged: (value) => _helper!.sortTypeOnChanged(value as String),
      dropdownList: _helper!.dropdownList,
      dropdownValue: _helper!.sortTypeValue.isEmpty ? null : _helper!.sortTypeValue,
      searchController: _helper!.searchController,
      isShowDropdown: true,
      isShowSearch: true,
      isAscending: _helper!.isAscending,
      sortOnTap: () => _helper!.sortOnTap(),
      children: [
        const SizedBox(height: 10),
        for (var element in _helper!.auditViewList)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Table(
              border: TableBorder.all(
                color: AppColorConstants.colorMediumGrey,
                borderRadius: BorderRadius.circular(5),
              ),
              children: [
                for (var entry in element.toJson().entries)
                  TableRow(
                    children: [
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.centerLeft,
                          color: AppColorConstants.colorLightGrey,
                          child: AppText(
                            entry.key.tr,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                     TableCell(
                              verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                child: (entry.key == AppStringConstants.auditorName || entry.key == AppStringConstants.address)
                                    ? Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          AppText(
                                            entry.value.toString(),
                                            fontSize: 12,
                                            color: AppColorConstants.colorBlack,
                                          ),
                                          Row(
                                            children: [
                                              AppImageAsset(image: AppAssetsConstants.icPhone),
                                              Expanded(
                                                child: AppText(
                                                  element.mobileNo ?? '',
                                                  fontSize: 12,
                                                  color: AppColorConstants.colorBlack,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : AppText(
                                        (entry.key == AppStringConstants.dateOfRequest)
                                            ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                            : '${entry.value ?? ''}',
                                        fontSize: 12,
                                        color: AppColorConstants.colorBlack,
                                      ),
                              ),
                            ),
                    ],
                  ),
              ],
            ),
          ),
      ],
    );
  }
}
