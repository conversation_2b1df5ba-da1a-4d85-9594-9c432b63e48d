import 'package:ams/app_imports.dart';

class AuditorApprovalPendingAuditPageHelper {
  final AuditorApprovalPendingAuditPageState _pageState;

  List<AuditorApprovalPendingAuditViewModel> approvalPendingAuditViewList = [
    AuditorApprovalPendingAuditViewModel(
      contactPerson: 'test',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      address: 'Ahmedabad/Gujarat.',
      kob: 'test',
      mobileNo: '**********',
      date: DateTime.now().subtract(const Duration(days: 15)),
      report: '',
      auditorName: 'test',
      reportNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 1,
    ),
    AuditorApprovalPendingAuditViewModel(
      contactPerson: 'test agencyName2',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      address: 'Lakhnow/Madhya Pradesh.',
      kob: 'test',
      date: DateTime.now().subtract(const Duration(days: 30)),
      report: '',
      mobileNo: '**********',
      auditorName: 'test auditorName2',
      reportNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 2,
    ),
    AuditorApprovalPendingAuditViewModel(
      contactPerson: 'test agencyName3',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      address: 'Harina/Madhya Pradesh.',
      kob: 'test',
      mobileNo: '653214587',
      date: DateTime.now().subtract(const Duration(days: 1)),
      report: '',
      auditorName: 'test auditorName3',
      reportNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 3,
    ),
    AuditorApprovalPendingAuditViewModel(
      contactPerson: 'test agencyName4',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      address: 'Lahor/Pubjab',
      kob: 'test',
      date: DateTime.now().subtract(const Duration(days: 45)),
      report: '',
      mobileNo: '**********',
      auditorName: 'test auditorName4',
      reportNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 4,
    ),
  ];
  List<AuditorApprovalPendingAuditViewModel> tmpList = [];
  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.reportNo,
    AppStringConstants.auditorName,
    AppStringConstants.licenseNo,
    AppStringConstants.contactPerson,
    AppStringConstants.fboName,
    AppStringConstants.address,
    AppStringConstants.kob,
    AppStringConstants.date,
  ];

  String sortTypeValue = AppStringConstants.srNo;
  bool isAscending = true;

  TextEditingController searchController = TextEditingController();

  AuditorApprovalPendingAuditPageHelper(this._pageState) {
    tmpList = approvalPendingAuditViewList;
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      if (sortTypeValue == AppStringConstants.date) {
        approvalPendingAuditViewList.sort(
          (a, b) => a.date!.compareTo(b.date ?? DateTime.now()),
        );
      } else {
        approvalPendingAuditViewList.sort(
          (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
        );
      }
    } else {
      if (sortTypeValue == AppStringConstants.date) {
        approvalPendingAuditViewList.sort(
          (a, b) => b.date!.compareTo(a.date ?? DateTime.now()),
        );
      } else {
        approvalPendingAuditViewList.sort(
          (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
        );
      }
    }
    _pageState.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }
}
