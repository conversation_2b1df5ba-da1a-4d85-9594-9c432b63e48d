import 'package:ams/app_imports.dart';

class AuditorApprovalPendingAuditPage extends StatefulWidget {
  const AuditorApprovalPendingAuditPage({super.key});

  @override
  State<AuditorApprovalPendingAuditPage> createState() => AuditorApprovalPendingAuditPageState();
}

class AuditorApprovalPendingAuditPageState extends State<AuditorApprovalPendingAuditPage> {
  AuditorApprovalPendingAuditPageHelper? _helper;
  late AuditorApprovalPendingAuditController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? AuditorApprovalPendingAuditPageHelper(this);
    return GetBuilder<AuditorApprovalPendingAuditController>(
      init: AuditorApprovalPendingAuditController(),
      builder: (AuditorApprovalPendingAuditController pendingAuditController) {
        controller = pendingAuditController;
        return AppScaffold(
          appBar: CommonAppBar(title: AppStringConstants.approvalPending.tr),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return tableCard(
      dropDownOnChanged: (value) => _helper!.sortTypeOnChanged(value as String),
      dropdownList: _helper!.dropdownList,
      dropdownValue: _helper!.sortTypeValue.isEmpty ? null : _helper!.sortTypeValue,
      searchController: _helper!.searchController,
      isShowDropdown: true,
      isShowSearch: true,
      isAscending: _helper!.isAscending,
      sortOnTap: () => _helper!.sortOnTap(),
      children: [
        const SizedBox(height: 10),
        for (var element in _helper!.approvalPendingAuditViewList)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Table(
              border: TableBorder.all(
                color: AppColorConstants.colorMediumGrey,
                borderRadius: BorderRadius.circular(5),
              ),
              children: [
                for (var entry in element.toJson().entries)
                  TableRow(
                    children: [
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.centerLeft,
                          color: AppColorConstants.colorLightGrey,
                          child: AppText(
                            entry.key.tr,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      (entry.key == AppStringConstants.report)
                          ? TableCell(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AppButton(
                                      onTap: () => gotoInspectionReportPage(),
                                      title: AppStringConstants.view.tr,
                                      borderRadius: BorderRadius.circular(5),
                                      textSize: 12,
                                      width: 80,
                                      height: 30,
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : TableCell(
                              verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                child: (entry.key == AppStringConstants.auditorName || entry.key == AppStringConstants.address)
                                    ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          AppText(
                                            entry.value.toString(),
                                            fontSize: 12,
                                            color: AppColorConstants.colorBlack,
                                          ),
                                          Row(
                                            children: [
                                              AppImageAsset(image: AppAssetsConstants.icPhone),
                                              Expanded(
                                                child: AppText(
                                                  (entry.key == AppStringConstants.address) ? replaceLastSixChars(element.mobileNo ?? '') :element.mobileNo ?? '',
                                                  fontSize: 12,
                                                  color: AppColorConstants.colorBlack,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : AppText(
                                        (entry.key == AppStringConstants.date)
                                            ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                            : '${entry.value ?? ''}',
                                        fontSize: 12,
                                        color: AppColorConstants.colorBlack,
                                      ),
                              ),
                            ),
                    ],
                  ),
              ],
            ),
          ),
      ],
    );
  }
}
