import 'package:ams/app_imports.dart';

class AuditorPersonalInfoPage extends StatefulWidget {
  const AuditorPersonalInfoPage({super.key});

  @override
  State<AuditorPersonalInfoPage> createState() => AuditorPersonalInfoPageState();
}

class AuditorPersonalInfoPageState extends State<AuditorPersonalInfoPage> {
  AuditPersonalInfoPageHelper? _helper;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? (AuditPersonalInfoPageHelper(this));
    return AppScaffold(
      appBar: CommonAppBar(title: AppStringConstants.auditorDetails.tr),
      body: _bodyView(),
    );
  }

  Widget _bodyView() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 11, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColorConstants.colorAppPrimary),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 13),
            alignment: Alignment.center,
            decoration: const BoxDecoration(
              color: AppColorConstants.colorAppPrimary,
              borderRadius: BorderRadius.only(topLeft: Radius.circular(7), topRight: Radius.circular(7)),
            ),
            child: AppText(
              AppStringConstants.auditorDetails.tr,
              fontSize: 18,
              color: AppColorConstants.colorWhite,
              fontWeight: FontWeight.w600,
            ),
          ),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(8),
              children: [
                _personalInfoView(),
                const SizedBox(height: 12),
                _auditAgencyInfoView(),
                const SizedBox(height: 12),
                _docAttachedInfoView(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _personalInfoView() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorSkyBlue),
            borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 22, vertical: 10),
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 30),
              commonRichText('${AppStringConstants.name.tr} : ', 'Test'),
              commonRichText('${AppStringConstants.email.tr} : ', '<EMAIL>'),
              commonRichText('${AppStringConstants.mobile.tr} : ', '88956 56231'),
              commonRichText('${AppStringConstants.address.tr} : ', 'Test Add, Chandigarh'),
            ],
          ),
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            AppImageAsset(image: AppAssetsConstants.imgPersonalInfo),
            AppText(
              AppStringConstants.personalInfo.tr,
              fontSize: 18,
              color: AppColorConstants.colorWhite,
            ),
          ],
        ),
      ],
    );
  }

  Widget _auditAgencyInfoView() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorLightGreen),
            borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 22, vertical: 10),
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 30),
              commonRichText('${AppStringConstants.agencyName.tr} : ', 'Test Agency'),
            ],
          ),
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            AppImageAsset(image: AppAssetsConstants.imgAgencyInfo),
            AppText(
              AppStringConstants.auditAgencyInfo.tr,
              fontSize: 18,
              color: AppColorConstants.colorWhite,
            ),
          ],
        ),
      ],
    );
  }

  Widget _docAttachedInfoView() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorSkyBlue),
            borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 22, vertical: 10),
          padding: const EdgeInsets.all(12),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 30),
              AppText('Lead Auditor Certificate\nEducational Qualification Certificate\nAuditor Log\nSector Specific Knowledge'),
            ],
          ),
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            AppImageAsset(image: AppAssetsConstants.imgPersonalInfo),
            AppText(
              AppStringConstants.documentAttached.tr,
              fontSize: 18,
              color: AppColorConstants.colorWhite,
            ),
          ],
        ),
      ],
    );
  }

  Widget commonRichText(String firstText, String secondText) {
    return Text.rich(
      TextSpan(
        text: firstText,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
        children: [
          TextSpan(
            text: secondText,
            style: const TextStyle(fontWeight: FontWeight.w400),
          ),
        ],
      ),
    );
  }
}
