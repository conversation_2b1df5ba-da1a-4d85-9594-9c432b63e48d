

import 'package:ams/app_imports.dart';

class AuditorReceivedAuditPageHelper {
  final AuditorReceivedAuditPageState _pageState;

  List<AuditorReceivedAuditViewModel> receivedAuditViewList = [
    AuditorReceivedAuditViewModel(
      contactPerson: 'contactPerson1',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      address: 'Ahmedabad/Gujarat.',
      kob: 'test',
      mobileNo: '**********',
      dateOfRequest: DateTime.now().subtract(const Duration(days: 15)),
      report: '',
      auditorName: 'test',
      idNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 1,
      agencyName: 'agencyName1',
    ),
    AuditorReceivedAuditViewModel(
      contactPerson: 'contactPerson2',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      address: 'Lakhnow/Madhya Pradesh.',
      kob: 'test',
      mobileNo: '**********',
      dateOfRequest: DateTime.now().subtract(const Duration(days: 30)),
      report: '',
      auditorName: 'test auditorName2',
      idNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 2,
      agencyName: 'agencyName2',
      isResubmit: true,
    ),
    AuditorReceivedAuditViewModel(
      contactPerson: 'contactPerson3',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      address: 'Harina/Madhya Pradesh.',
      kob: 'test',
      mobileNo: '**********',
      dateOfRequest: DateTime.now().subtract(const Duration(days: 1)),
      report: '',
      auditorName: 'test auditorName3',
      idNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 3,
      agencyName: 'agencyName3',
    ),
    AuditorReceivedAuditViewModel(
      contactPerson: 'contactPerson4',
      fboName: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
      address: 'Lahor/Pubjab',
      kob: 'test',
      mobileNo: '**********',
      dateOfRequest: DateTime.now().subtract(const Duration(days: 45)),
      report: '',
      auditorName: 'test auditorName4',
      idNo: '42/68',
      licenseNo: '13023999000032',
      srNo: 4,
      isResubmit: true,
      agencyName: 'agencyName4',
    ),
  ];
  List<AuditorReceivedAuditViewModel> tmpList = [];
  List<String> dropdownList = [
    AppStringConstants.srNo,
    AppStringConstants.idNo,
    AppStringConstants.agencyName,
    AppStringConstants.auditorName,
    AppStringConstants.licenseNo,
    AppStringConstants.contactPerson,
    AppStringConstants.fboName,
    AppStringConstants.address,
    AppStringConstants.kob,
    AppStringConstants.dateOfRequest,
  ];

  String sortTypeValue = AppStringConstants.srNo;
  bool isAscending = true;

  TextEditingController searchController = TextEditingController();

  AuditorReceivedAuditPageHelper(this._pageState){
    tmpList = receivedAuditViewList;
  }

  void sortTypeOnChanged(String value) {
    sortTypeValue = value;
    if (isAscending) {
      if (sortTypeValue == AppStringConstants.dateOfRequest) {
        receivedAuditViewList.sort(
              (a, b) => a.dateOfRequest!.compareTo(b.dateOfRequest ?? DateTime.now()),
        );
      } else {
        receivedAuditViewList.sort(
              (a, b) => a.toJson()[sortTypeValue].compareTo(b.toJson()[sortTypeValue]),
        );
      }
    } else {
      if (sortTypeValue == AppStringConstants.dateOfRequest) {
        receivedAuditViewList.sort(
              (a, b) => b.dateOfRequest!.compareTo(a.dateOfRequest ?? DateTime.now()),
        );
      } else {
        receivedAuditViewList.sort(
              (a, b) => b.toJson()[sortTypeValue].compareTo(a.toJson()[sortTypeValue]),
        );
      }
    }
    _pageState.controller.update();
  }

  void sortOnTap() {
    isAscending = !isAscending;
    sortTypeOnChanged(sortTypeValue);
    _pageState.controller.update();
  }
}
