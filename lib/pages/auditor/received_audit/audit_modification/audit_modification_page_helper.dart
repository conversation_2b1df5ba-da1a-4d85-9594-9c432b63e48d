import 'package:ams/app_imports.dart';

class AuditorAuditModificationPageHelper {
  final AuditorAuditModificationPageState _pageState;

  ThirdPartyModificationAuditModel thirdPartyAuditModel = ThirdPartyModificationAuditModel(
    companyName:
        'BIOG<PERSON>ZE BIOTECH PRIVATE LIMITED\nKind of Business: Relabeller - Food or Health Supplements and Nutraceuticals etc.',
    reportNo: '75/48',
    auditStartDate: DateTime(2024, 7, 1),
    auditEndDate: DateTime(2024, 7, 7),
    auditorName: 'Test auditor',
    auditorContactNumber: '**********',
    address: '2255/4-7, 2ND FLOOR, MARI WALA TOWN, MANI MAJRAChandigarhChandigarh Chandigarh Chandigarh',
    email: '<EMAIL>',
    mobileNumber: '9317******',
    fboFssaiLicenseNo: '13023999000032',
    majorNonConformity: TextEditingController(text: 'Test'),
    minorNonConformity: TextEditingController(text: 'Test1'),
    comments: TextEditingController(text: 'Comment'),
    fboRepresentativeName: 'AJAY BHARDWAJ',
    auditorAgreementUrl: 'https://infotrain.fssai.gov.in/AMS/uploadauditdata/49milkauditoragreement.pdf',
    confidentialityUrl: 'https://infotrain.fssai.gov.in/AMS/uploadauditdata/49milkauditoragreement.pdf',
  );

  List<AuditModificationItem> auditModificationItems = [
    AuditModificationItem(
      srNo: '1',
      fileUrl: 'https://infotrain.fssai.gov.in/AMS/uploadauditdata/49milkauditoragreement.pdf',
      narration: 'Test',
      maxPoints: '2',
      question: 'Food establishment has an updated FSSAI license and is displayed at a prominent location',
      comments: TextEditingController(),
      pointScored: '2',
    ),
    AuditModificationItem(
      srNo: '2',
      narration: 'Test',
      maxPoints: '2',
      question: 'Food establishment has an updated FSSAI license and is displayed at a prominent location',
      comments: TextEditingController(),
      pointScored: '2',
    ),
    AuditModificationItem(
      srNo: '3',
      narration: 'Test',
      maxPoints: '4',
      isMandatory: true,
      question: 'Food establishment has an updated FSSAI license and is displayed at a prominent location',
      comments: TextEditingController(),
      pointScored: '0',
    ),
    AuditModificationItem(
      srNo: '4',
      fileUrl: 'https://infotrain.fssai.gov.in/AMS/uploadauditdata/49milkauditoragreement.pdf',
      narration: 'Test',
      maxPoints: '2',
      question: 'Food establishment has an updated FSSAI license and is displayed at a prominent location',
      comments: TextEditingController(),
      pointScored: '2',
    ),
    AuditModificationItem(
      srNo: '5',
      narration: 'Test',
      maxPoints: '2',
      question: 'Food establishment has an updated FSSAI license and is displayed at a prominent location',
      comments: TextEditingController(),
      pointScored: '2',
    ),
    AuditModificationItem(
      srNo: '6',
      narration: 'Test',
      maxPoints: '4',
      isMandatory: true,
      question: 'Food establishment has an updated FSSAI license and is displayed at a prominent location',
      comments: TextEditingController(),
      pointScored: '0',
    ),
  ];

  final TextEditingController fistTextController = TextEditingController();
  final TextEditingController secondTextController = TextEditingController();
  final TextEditingController thirdTextController = TextEditingController();
  final TextEditingController forthTextController = TextEditingController();

  AuditorAuditModificationPageHelper(this._pageState);

  Future<void> pickAgreementFile() async {
    File? file = await FilePickerHelper.instance.pickFiles(_pageState.context, fileSizeLimit: 2);
    if (file != null) {
      thirdPartyAuditModel.auditorAgreementFile = file;
    }
    _pageState.controller.update();
  }

  void removeAgreementFile() {
    thirdPartyAuditModel.auditorAgreementFile = null;
    _pageState.controller.update();
  }

  void removeAgreementUrl() {
    thirdPartyAuditModel.auditorAgreementUrl = null;
    _pageState.controller.update();
  }

  Future<void> pickConfidentialityFile() async {
    File? file = await FilePickerHelper.instance.pickFiles(_pageState.context, fileSizeLimit: 2);
    if (file != null) {
      thirdPartyAuditModel.confidentialityFile = file;
    }
    _pageState.controller.update();
  }

  void removeConfidentialityFile() {
    thirdPartyAuditModel.confidentialityFile = null;
    _pageState.controller.update();
  }

  void removeConfidentialityUrl() {
    thirdPartyAuditModel.confidentialityUrl = null;
    _pageState.controller.update();
  }

  Future<void> pickOtherFile() async {
    File? file = await FilePickerHelper.instance.pickFiles(_pageState.context, fileSizeLimit: 2);
    if (file != null) {
      thirdPartyAuditModel.otherFile = file;
    }
    _pageState.controller.update();
  }

  void removeOtherFile() {
    thirdPartyAuditModel.otherFile = null;
    _pageState.controller.update();
  }

  void removeOtherUrl() {
    thirdPartyAuditModel.otherUrl = null;
    _pageState.controller.update();
  }

  void removeChecklistUrl(AuditModificationItem element) {
    element.fileUrl = null;
    _pageState.controller.update();
    gotoBack();
  }

  Future<void> uploadCheckListFile(AuditModificationItem modificationItem) async {
    File? file = await FilePickerHelper.instance.pickFiles(_pageState.context, fileSizeLimit: 2);
    if (file != null) {
      modificationItem.file = file;
      _pageState.controller.update();
    }
  }

  void removeCheckListFile(AuditModificationItem modificationItem) {
    modificationItem.file = null;
    _pageState.controller.update();
  }

  void submitOnTap() {}
}
