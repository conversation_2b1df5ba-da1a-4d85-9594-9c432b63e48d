import 'package:ams/app_imports.dart';

class AuditorAuditModificationPage extends StatefulWidget {
  const AuditorAuditModificationPage({super.key});

  @override
  State<AuditorAuditModificationPage> createState() => AuditorAuditModificationPageState();
}

class AuditorAuditModificationPageState extends State<AuditorAuditModificationPage> {
  AuditorAuditModificationPageHelper? _helper;
  late AuditorReceivedAuditController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? AuditorAuditModificationPageHelper(this);
    return GetBuilder<AuditorReceivedAuditController>(
      init: AuditorReceivedAuditController(),
      builder: (AuditorReceivedAuditController auditorReceivedAuditController) {
        controller = auditorReceivedAuditController;
        return AppScaffold(
          appBar: CommonAppBar(title: AppStringConstants.auditModification.tr),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return Column(
      children: [
        Expanded(
          child: ListView(
            padding: const EdgeInsets.all(12),
            children: [
              _textFieldView(),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text.rich(
                      TextSpan(
                        text: '${AppStringConstants.note.tr} : ',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: AppColorConstants.colorRedAccent,
                        ),
                        children: [
                          TextSpan(
                            text: AppStringConstants.uploadFileNote.tr,
                            style: const TextStyle(
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text.rich(
                      TextSpan(
                        text: '${AppStringConstants.note.tr} : ',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: AppColorConstants.colorRedAccent,
                        ),
                        children: [
                          TextSpan(
                            text: AppStringConstants.requiredQuestionNote.tr,
                            style: const TextStyle(
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              _thirdPartyAuditView(),
              const SizedBox(height: 10),
              _inspectionReportView(),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: AppButton(
            onTap: () => _helper!.submitOnTap(),
            textSize: 20,
            title: AppStringConstants.submit.tr,
            backColor: AppColorConstants.colorYellow,
          ),
        ),
      ],
    );
  }

  Widget _textFieldView() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: AppTextFormField(
                controller: _helper!.fistTextController,
                hintText: '',
                textFieldHeight: 40,
              ),
            ),
            const SizedBox(width: 11),
            Expanded(
              child: AppTextFormField(
                controller: _helper!.secondTextController,
                hintText: '',
                textFieldHeight: 40,
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              child: AppTextFormField(
                controller: _helper!.thirdTextController,
                hintText: '',
                textFieldHeight: 40,
              ),
            ),
            const SizedBox(width: 11),
            Expanded(
              child: AppTextFormField(
                controller: _helper!.forthTextController,
                hintText: '',
                textFieldHeight: 40,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _thirdPartyAuditView() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppColorConstants.colorVividOrange),
      ),
      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColorConstants.colorVividOrange,
              border: Border.all(color: AppColorConstants.colorVividOrange),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(3),
                topLeft: Radius.circular(3),
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 13),
            child: AppText(
              AppStringConstants.fssaiThirdPartyAudits.tr,
              color: AppColorConstants.colorWhite,
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                  alignment: Alignment.centerLeft,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(5), topRight: Radius.circular(5)),
                    border: Border(
                      left: BorderSide(color: AppColorConstants.colorLightGrey300),
                      right: BorderSide(color: AppColorConstants.colorLightGrey300),
                      top: BorderSide(color: AppColorConstants.colorLightGrey300),
                    ),
                    color: AppColorConstants.colorPeach,
                  ),
                  child: const AppText('Test Agency Name', fontWeight: FontWeight.w600, fontSize: 16),
                ),
                Table(
                  border: TableBorder.all(
                    color: AppColorConstants.colorLightGrey300,
                    borderRadius: const BorderRadius.only(bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5)),
                  ),
                  children: [
                    for (var entry in _helper!.thirdPartyAuditModel.toJson().entries)
                      _buildTableRow(
                        entry.key,
                        (entry.key == AppStringConstants.auditStartDate || entry.key == AppStringConstants.auditEndDate)
                            ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                            : entry.value,
                      ),
                    _buildTextBoxRow(AppStringConstants.majorNonConformity, _helper!.thirdPartyAuditModel.majorNonConformity),
                    _buildTextBoxRow(AppStringConstants.minorNonConformity, _helper!.thirdPartyAuditModel.minorNonConformity),
                    _buildTextBoxRow(AppStringConstants.comments, _helper!.thirdPartyAuditModel.comments),
                    _buildFileRow(
                      title: AppStringConstants.auditorAgreementFile.tr,
                      removeFileOnTap: () => _helper!.removeAgreementFile(),
                      removeFileUrlOnTap: () => _helper!.removeAgreementUrl(),
                      file: _helper!.thirdPartyAuditModel.auditorAgreementFile,
                      fileUrl: _helper!.thirdPartyAuditModel.auditorAgreementUrl,
                      pickFileOnTap: () => _helper!.pickAgreementFile(),
                    ),
                    _buildFileRow(
                      title: AppStringConstants.confidentialityFile.tr,
                      removeFileOnTap: () => _helper!.removeConfidentialityFile(),
                      removeFileUrlOnTap: () => _helper!.removeConfidentialityUrl(),
                      file: _helper!.thirdPartyAuditModel.confidentialityFile,
                      fileUrl: _helper!.thirdPartyAuditModel.confidentialityUrl,
                      pickFileOnTap: () => _helper!.pickConfidentialityFile(),
                    ),
                    _buildFileRow(
                      title: AppStringConstants.otherFile.tr,
                      removeFileOnTap: () => _helper!.removeOtherFile(),
                      removeFileUrlOnTap: () => _helper!.removeOtherUrl(),
                      file: _helper!.thirdPartyAuditModel.otherFile,
                      fileUrl: _helper!.thirdPartyAuditModel.otherUrl,
                      pickFileOnTap: () => _helper!.pickOtherFile(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _inspectionReportView() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppColorConstants.colorVividOrange),
      ),
      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColorConstants.colorVividOrange,
              border: Border.all(color: AppColorConstants.colorVividOrange),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(3),
                topLeft: Radius.circular(3),
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 13),
            child: const AppText(
              '${AppStringConstants.inspectionChecklistFor} GENERAL MANUFACTURING',
              color: AppColorConstants.colorWhite,
              fontSize: 18,
              textAlign: TextAlign.center,
              fontWeight: FontWeight.w700,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                for (var element in _helper!.auditModificationItems)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    child: Table(
                      border: TableBorder.all(
                        color: AppColorConstants.colorLightGrey300,
                      ),
                      children: [
                        for (var entry in element.toJson().entries)
                          TableRow(
                            children: [
                              TableCell(
                                verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                  alignment: Alignment.centerLeft,
                                  color: AppColorConstants.colorPeach,
                                  child: AppText(
                                    entry.key.tr,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              TableCell(
                                verticalAlignment: TableCellVerticalAlignment.middle,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                  child: (entry.key == AppStringConstants.filesUploaded)
                                      ? (entry.value != null && entry.value.toString().isNotEmpty)
                                          ? Row(
                                              children: [
                                                InkWell(
                                                  onTap: () => launchDocumentUrl(entry.value.toString()),
                                                  child: Padding(
                                                    padding: const EdgeInsets.symmetric(vertical: 8),
                                                    child: AppImageAsset(
                                                      image: AppAssetsConstants.icDownload,
                                                      height: 20,
                                                      width: 20,
                                                      color: AppColorConstants.colorBlack,
                                                    ),
                                                  ),
                                                ),
                                                InkWell(
                                                  onTap: () {
                                                    appDialog(
                                                      context: context,
                                                      title: AppStringConstants.deleteFile.tr,
                                                      buttonText: AppStringConstants.ok.tr,
                                                      buttonTap: () => _helper!.removeChecklistUrl(element),
                                                      subTitle: AppStringConstants.fileWillBeRemovedPermanently.tr,
                                                    );
                                                  },
                                                  child: Padding(
                                                    padding: const EdgeInsets.symmetric(horizontal: 8).copyWith(bottom: 3),
                                                    child: AppImageAsset(
                                                      image: AppAssetsConstants.icDelete,
                                                      color: AppColorConstants.colorBlack,
                                                      height: 18,
                                                      width: 18,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            )
                                          : const SizedBox()
                                      : AppText(
                                          '${(entry.key == AppStringConstants.comments && entry.value.toString().isEmpty) ? '-' : (entry.key == AppStringConstants.srNo && element.isMandatory) ? '${entry.value}*' : entry.value}',
                                          fontSize: 12,
                                          color: (entry.key == AppStringConstants.question && element.isMandatory)
                                              ? AppColorConstants.colorRedAccent
                                              : AppColorConstants.colorBlack,
                                        ),
                                ),
                              ),
                            ],
                          ),
                        _buildUpdateFileRow(element),
                        _buildUpdateNarrationRow(element),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  TableRow _buildTableRow(String title, String value) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorPeach,
            child: AppText(
              title.tr,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: AppText(
              value,
              fontSize: 12,
              color: AppColorConstants.colorBlack,
            ),
          ),
        ),
      ],
    );
  }

  TableRow _buildTextBoxRow(String title, TextEditingController? controller) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorPeach,
            child: AppText(
              title.tr,
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: AppColorConstants.colorBlue,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: AppTextFormField(
              controller: controller,
              hintText: '',
              textFieldHeight: 30,
              contentPadding: const EdgeInsets.symmetric(horizontal: 5),
              textFieldFontSize: 13,
            ),
          ),
        ),
      ],
    );
  }

  TableRow _buildFileRow({
    required String title,
    required File? file,
    required String? fileUrl,
    required GestureTapCallback pickFileOnTap,
    required GestureTapCallback removeFileOnTap,
    required GestureTapCallback removeFileUrlOnTap,
  }) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorPeach,
            child: AppText(
              title.tr,
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: AppColorConstants.colorBlue,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (fileUrl?.isNotEmpty ?? false)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 5),
                    child: Row(
                      children: [
                        Expanded(
                          child: AppText(
                            '${AppStringConstants.viewFile.tr} : ',
                            color: AppColorConstants.colorRedAccent,
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                          ),
                        ),
                        InkWell(
                          onTap: () => launchDocumentUrl(fileUrl!),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: AppImageAsset(
                              image: AppAssetsConstants.icDownload,
                              color: AppColorConstants.colorBlack,
                              height: 18,
                              width: 18,
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            appDialog(
                              context: context,
                              title: AppStringConstants.deleteFile.tr,
                              buttonText: AppStringConstants.ok.tr,
                              buttonTap: () {
                                gotoBack();
                                removeFileUrlOnTap();
                              },
                              subTitle: AppStringConstants.fileWillBeRemovedPermanently.tr,
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8).copyWith(bottom: 3),
                            child: AppImageAsset(
                              image: AppAssetsConstants.icDelete,
                              color: AppColorConstants.colorBlack,
                              height: 18,
                              width: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                AppButton(
                  onTap: pickFileOnTap,
                  width: 100,
                  height: 30,
                  textSize: 12,
                  title: AppStringConstants.chooseFile.tr,
                  borderRadius: BorderRadius.circular(5),
                  border: Border.all(color: AppColorConstants.colorYellow),
                  backColor: AppColorConstants.colorYellow,
                ),
                if (file?.path.isNotEmpty ?? false)
                  Padding(
                    padding: const EdgeInsets.only(top: 5),
                    child: Row(
                      children: [
                        Expanded(
                          child: AppText(
                            file?.path.split('/').last ?? '',
                            fontSize: 12,
                            color: AppColorConstants.colorRedAccent,
                          ),
                        ),
                        InkWell(
                          onTap: removeFileOnTap,
                          child: const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Icon(
                              Icons.cancel,
                              color: AppColorConstants.colorRedAccent,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  TableRow _buildUpdateFileRow(AuditModificationItem modificationItem) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorPeach,
            child: AppText(
              AppStringConstants.updateFile.tr,
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: AppColorConstants.colorBlue,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppButton(
                  onTap: () => _helper!.uploadCheckListFile(modificationItem),
                  width: 100,
                  height: 30,
                  textSize: 12,
                  title: AppStringConstants.chooseFile.tr,
                  borderRadius: BorderRadius.circular(5),
                  border: Border.all(color: AppColorConstants.colorYellow),
                  backColor: AppColorConstants.colorYellow,
                ),
                if (modificationItem.file?.path.isNotEmpty ?? false)
                  Padding(
                    padding: const EdgeInsets.only(top: 5),
                    child: Row(
                      children: [
                        Expanded(
                          child: AppText(
                            modificationItem.file?.path.split('/').last ?? '',
                            fontSize: 12,
                            color: AppColorConstants.colorRedAccent,
                          ),
                        ),
                        InkWell(
                          onTap: () => _helper!.removeCheckListFile(modificationItem),
                          child: const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Icon(
                              Icons.cancel,
                              color: AppColorConstants.colorRedAccent,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  TableRow _buildUpdateNarrationRow(AuditModificationItem modificationItem) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorPeach,
            child: AppText(
              AppStringConstants.updateNarration.tr,
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: AppColorConstants.colorBlue,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppTextFormField(
                  controller: modificationItem.comments,
                  hintText: '',
                  textFieldHeight: 40,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
