import 'package:ams/app_imports.dart';

class AuditorReceivedAuditPage extends StatefulWidget {
  const AuditorReceivedAuditPage({super.key});

  @override
  State<AuditorReceivedAuditPage> createState() => AuditorReceivedAuditPageState();
}

class AuditorReceivedAuditPageState extends State<AuditorReceivedAuditPage> {
  AuditorReceivedAuditPageHelper? _helper;
  late AuditorReceivedAuditController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? AuditorReceivedAuditPageHelper(this);
    return GetBuilder<AuditorReceivedAuditController>(
      init: AuditorReceivedAuditController(),
      builder: (AuditorReceivedAuditController receivedAuditController) {
        controller = receivedAuditController;
        return AppScaffold(
          appBar: CommonAppBar(title: AppStringConstants.applicationReceived.tr),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return tableCard(
      dropDownOnChanged: (value) => _helper!.sortTypeOnChanged(value as String),
      dropdownList: _helper!.dropdownList,
      dropdownValue: _helper!.sortTypeValue.isEmpty ? null : _helper!.sortTypeValue,
      searchController: _helper!.searchController,
      isShowDropdown: true,
      isShowSearch: true,
      isAscending: _helper!.isAscending,
      sortOnTap: () => _helper!.sortOnTap(),
      children: [
        const SizedBox(height: 10),
        for (var element in _helper!.receivedAuditViewList)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Table(
              border: TableBorder.all(
                color: AppColorConstants.colorMediumGrey,
                borderRadius: BorderRadius.circular(5),
              ),
              children: [
                for (var entry in element.toJson().entries)
                  TableRow(
                    children: [
                      TableCell(
                        verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.centerLeft,
                          color: AppColorConstants.colorLightGrey,
                          child: AppText(
                            entry.key.tr,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      (entry.key == AppStringConstants.report)
                          ? TableCell(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    (element.isResubmit)
                                        ? AppButton(
                                            onTap: () => gotoAuditorAuditModificationPage(),
                                            title: AppStringConstants.conductAuditing.tr,
                                            backColor: AppColorConstants.colorYellowShade400,
                                            borderRadius: BorderRadius.circular(5),
                                            textSize: 11,
                                            titleColor: AppColorConstants.colorWhite,
                                            height: 30,
                                            border: Border.all(width: 0.5, color: AppColorConstants.colorYellowShade400),
                                          )
                                        : AppButton(
                                            onTap: () => gotoConductAuditPage(),
                                            title: AppStringConstants.conductAuditing.tr,
                                            backColor: AppColorConstants.colorBlue,
                                            borderRadius: BorderRadius.circular(5),
                                            textSize: 11,
                                            titleColor: AppColorConstants.colorWhite,
                                            height: 30,
                                            border: Border.all(width: 0.5, color: AppColorConstants.colorBlue),
                                          ),
                                  ],
                                ),
                              ),
                            )
                          : TableCell(
                              verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                child: (entry.key == AppStringConstants.auditorName || entry.key == AppStringConstants.address)
                                    ? Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          AppText(
                                            entry.value.toString(),
                                            fontSize: 12,
                                            color: AppColorConstants.colorBlack,
                                          ),
                                          Row(
                                            children: [
                                              AppImageAsset(image: AppAssetsConstants.icPhone),
                                              Expanded(
                                                child: AppText(
                                                  (entry.key == AppStringConstants.address) ? replaceLastSixChars(element.mobileNo ?? '') :element.mobileNo ?? '',
                                                  fontSize: 12,
                                                  color: AppColorConstants.colorBlack,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : AppText(
                                        (entry.key == AppStringConstants.dateOfRequest)
                                            ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                            : '${entry.value ?? ''}',
                                        fontSize: 12,
                                        color: AppColorConstants.colorBlack,
                                      ),
                              ),
                            ),
                    ],
                  ),
              ],
            ),
          ),
      ],
    );
  }
}
