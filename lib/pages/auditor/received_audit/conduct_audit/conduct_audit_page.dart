import 'package:ams/app_imports.dart';

class ConductAuditPage extends StatefulWidget {
  const ConductAuditPage({super.key});

  @override
  State<ConductAuditPage> createState() => ConductAuditPageState();
}

class ConductAuditPageState extends State<ConductAuditPage> {
  ConductAuditPageHelper? _helper;
  late AuditorReceivedAuditController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? ConductAuditPageHelper(this);
    return GetBuilder<AuditorReceivedAuditController>(
      init: AuditorReceivedAuditController(),
      builder: (AuditorReceivedAuditController receivedAuditController) {
        controller = receivedAuditController;
        return AppScaffold(
          appBar: CommonAppBar(title: AppStringConstants.auditReportToBeSubmittedByAuditor.tr),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return tableCard(

      headerSize: 16,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text.rich(
                TextSpan(
                  text: '${AppStringConstants.requestNo.tr} : ',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                  children: const [
                    TextSpan(
                      text: '77',
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  text: '${AppStringConstants.requestDate.tr} : ',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                  children: [
                    TextSpan(
                      text: DateHelper.getDDMMYYYYFormatDate(DateTime.now()),
                      style: const TextStyle(
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: _commonTextField(
                _helper!.licenseNumberController,
                AppStringConstants.licenseNumber.tr,
                readyOnly: true,
                textFieldColor: AppColorConstants.colorLightGrey100,
              ),
            ),
            InkWell(
              onTap: () => _helper!.verifyLicenseOnTap(),
              child: Padding(
                padding: const EdgeInsets.only(left: 6, top: 10),
                child: AppText(
                  AppStringConstants.checkValidity.tr,
                  color: AppColorConstants.colorBlue,
                ),
              ),
            ),
          ],
        ),
        if (_helper!.isVerify) ...[
          _licenseTableView(),
          const SizedBox(height: 15),
        ],
        _commonTextField(
          _helper!.auditAgencyController,
          AppStringConstants.auditAgency.tr,
          readyOnly: true,
          textFieldColor: AppColorConstants.colorLightGrey100,
        ),
        _commonTextField(
          _helper!.auditedByController,
          AppStringConstants.auditedBy.tr,
          readyOnly: true,
          textFieldColor: AppColorConstants.colorLightGrey100,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _commonTextField(
                _helper!.auditStartDateController,
                AppStringConstants.auditStartDate.tr,
                errorText: _helper!.auditStartDateError.tr,
                readyOnly: true,
                hintText: AppStringConstants.mmDdYyyy.toLowerCase(),
                prefixIcon: AppAssetsConstants.icCalender,
                onTap: () => _helper!.pickDate(_helper!.auditStartDateController),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _commonTextField(
                _helper!.auditEndDateController,
                AppStringConstants.auditEndDate.tr,
                errorText: _helper!.auditEndDateError.tr,
                readyOnly: true,
                hintText: AppStringConstants.mmDdYyyy.toLowerCase(),
                prefixIcon: AppAssetsConstants.icCalender,
                onTap: () => _helper!.pickDate(_helper!.auditEndDateController),
              ),
            ),
          ],
        ),
        AppText(AppStringConstants.kindOfBusiness.tr,fontWeight: FontWeight.w500),
        const SizedBox(height: 2),
        AppDropdownButton(
          buttonHeight: 40,
          errorText: _helper!.kobError.tr,
          dropdownMenuHeight: 150,
          hint: AppStringConstants.notSelected.tr,
          value: _helper!.selectedKob.isEmpty ? null : _helper!.selectedKob,
          onChanged: (value) => _helper!.kobOnChanged(value),
          dropdownItems: _helper!.kobList,
          buttonColor: AppColorConstants.colorWhite,
        ),
        const SizedBox(height: 5),
        _commonTextField(
          _helper!.numOfFoodHandlersController,
          AppStringConstants.numOfFoodHandlers.tr,
          keyboardType: TextInputType.phone,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          errorText: _helper!.numOfFoodHandlersError.tr,
        ),
        _commonTextField(
          _helper!.numOfTrainedFoodSafetyController,
          AppStringConstants.numOfTrainedFoodSafetySupervisor.tr,
          keyboardType: TextInputType.phone,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          errorText: _helper!.numOfTrainedFoodSafetyError.tr,
        ),
        const SizedBox(height: 10),
        AppButton(
          onTap: () => _helper!.proceedValidation(),
          title: AppStringConstants.proceed.tr,
        ),
      ],
    );
  }

  Widget _licenseTableView() {
    return Table(
      border: TableBorder.all(
        color: AppColorConstants.colorMediumGrey,
        borderRadius: BorderRadius.circular(5),
      ),
      children: [
        for (var entry in _helper!.licenseTableData.entries)
          TableRow(
            children: [
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                child: Container(
                  padding: const EdgeInsets.all(10),
                  alignment: Alignment.centerLeft,
                  color: AppColorConstants.colorLightGrey,
                  child: AppText(
                    entry.key.tr,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ),
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                child: Container(
                  padding: const EdgeInsets.all(10),
                  child: AppText(
                    (entry.key == AppStringConstants.expiryDate)
                        ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                        : (entry.key == AppStringConstants.mobileNo)
                            ? replaceLastSixChars(entry.value)
                            : '${entry.value ?? ''}',
                    fontSize: 12,
                    color: AppColorConstants.colorBlack,
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _commonTextField(
    TextEditingController controller,
    String labelText, {
    String? hintText,
    bool readyOnly = false,
    Color? textFieldColor,
    String? prefixIcon,
    GestureTapCallback? onTap,
    TextInputType? keyboardType,
    int? maxLength,
    List<TextInputFormatter>? inputFormatters,
    String? errorText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(labelText, fontWeight: FontWeight.w500),
        const SizedBox(height: 2),
        AppTextFormField(
          controller: controller,
          hintText: hintText,
          textFieldHeight: 40,
          onTap: onTap,
          maxLength: maxLength,
          inputFormatters: inputFormatters,
          keyboardType: keyboardType,
          readOnly: readyOnly,
          errorText: errorText ?? '',
          textFieldColor: textFieldColor,
          prefixIcon: (prefixIcon?.isEmpty ?? true)
              ? null
              : Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: AppImageAsset(image: AppAssetsConstants.icCalender),
                ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }
}
