import 'package:ams/app_imports.dart';

class ConductAuditPageHelper {
  final ConductAuditPageState _pageState;

  final TextEditingController licenseNumberController = TextEditingController(text: '13023999000032');
  final TextEditingController auditAgencyController = TextEditingController(text: 'Test agency');
  final TextEditingController auditedByController = TextEditingController(text: 'Test auditor');
  final TextEditingController auditStartDateController = TextEditingController();
  final TextEditingController auditEndDateController = TextEditingController();
  final TextEditingController numOfFoodHandlersController = TextEditingController();
  final TextEditingController numOfTrainedFoodSafetyController = TextEditingController();

  String auditStartDateError = '';
  String auditEndDateError = '';
  String kobError = '';
  String numOfFoodHandlersError = '';
  String numOfTrainedFoodSafetyError = '';

  String selectedKob = '';

  bool isVerify = false;

  List<String> kobList = [
    'General Manufacturing',
    'Milk Processing',
    'Meat Processing',
    'Slaughter House',
    'Catering',
    'Retail',
    'Transport',
    'Storage And Warehouse',
    'Fish and Fish Products',
  ];

  Map<String, dynamic> licenseTableData = {
    AppStringConstants.contactPerson: 'Ajay Bhardwaj',
    AppStringConstants.fbo: 'BIOGLAZE BIOTECH PRIVATE LIMITED',
    AppStringConstants.district: 'Chandigarh',
    AppStringConstants.state: 'Chandigarh',
    AppStringConstants.mobileNo: '**********',
    AppStringConstants.email: '<EMAIL>',
    AppStringConstants.kob: 'Relabelter - Food or Health\nSupplements and Nutraceuticals\netc.',
    AppStringConstants.expiryDate: DateTime.now(),
  };

  ValidationHelper validationHelper = ValidationHelper.instance;

  ConductAuditPageHelper(this._pageState);

  void verifyLicenseOnTap() {
    isVerify = true;
    _pageState.controller.update();
  }

  Future<void> pickDate(TextEditingController controller) async {
    DateTime? dateTime = await appDatePicker(_pageState.context);
    if (dateTime != null) {
      controller.text = DateHelper.getDDMMYYYYFormatDate(dateTime);
    }
    _pageState.controller.update();
  }

  void proceedValidation() {
    if (!isVerify) {
      AppStringConstants.plzVerifyLicense.tr.showRequiredToast();
    } else {
      if (validationHelper.validateEmptyController(auditStartDateController)) {
        auditStartDateError = AppStringConstants.plzSelectAuditStartDate;
      } else {
        auditStartDateError = '';
      }
      if (validationHelper.validateEmptyController(auditEndDateController)) {
        auditEndDateError = AppStringConstants.plzSelectAuditEndDate;
      } else {
        auditEndDateError = '';
      }
      if (selectedKob.isEmpty) {
        kobError = AppStringConstants.plzSelectKob;
      } else {
        kobError = '';
      }
      if (validationHelper.validateEmptyController(numOfFoodHandlersController)) {
        numOfFoodHandlersError = AppStringConstants.plzEnterNumOfFoodHandlers;
      } else {
        numOfFoodHandlersError = '';
      }
      if (validationHelper.validateEmptyController(numOfTrainedFoodSafetyController)) {
        numOfTrainedFoodSafetyError = AppStringConstants.plzEnterNumOfTrainedFoodSafetySupervisor;
      } else {
        numOfTrainedFoodSafetyError = '';
      }

      if(
        auditStartDateError.isEmpty &&
        auditEndDateError.isEmpty &&
        kobError.isEmpty &&
        numOfFoodHandlersError.isEmpty &&
        numOfTrainedFoodSafetyError.isEmpty){
        isVerify = false;
        gotoChecklistPage();
      }
    }
    _pageState.controller.update();
  }

  void kobOnChanged(value) {
    selectedKob = value;
    _pageState.controller.update();
  }
}
