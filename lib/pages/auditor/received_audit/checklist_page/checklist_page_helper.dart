import 'package:ams/app_imports.dart';

class ChecklistPageHelper {
  final ChecklistPageState _pageState;

  List<CheckListModel> checklist = [
    CheckListModel(
      title: 'Inspection Checklist Item for Milk and Milk Product Processing',
      checkListItem: [
        CheckListItem(
          question: 'Food establishment has an updated FSSAI license and is displayed at a prominent location',
          comments: TextEditingController(),
        ),
      ],
    ),
    CheckListModel(
      title: 'I.Design & Facilities',
      checkListItem: [
        CheckListItem(
          question:
              'The design of food premises provides adequate working space; permit maintenance & cleaning to prevent the entry of dirt, dust & pests.',
          comments: TextEditingController(),
        ),
        CheckListItem(
          question: 'The internal structure & fittings are made of non-toxic and impermeable material.',
          comments: TextEditingController(),
        ),
        CheckListItem(
          question:
              'Potable water (meeting standards of IS:10500) is used as product ingredient or in contact with food or food contact surface. Tested for quality semi-annually. Check for records.',
          isMandatory: true,
          comments: TextEditingController(),
        ),
      ],
    ),
    CheckListModel(
      title: 'II.Control of operation',
      checkListItem: [
        CheckListItem(
          question:
              'The design of food premises provides adequate working space; permit maintenance & cleaning to prevent the entry of dirt, dust & pests.',
          comments: TextEditingController(),
        ),
        CheckListItem(
          question: 'The internal structure & fittings are made of non-toxic and impermeable material.',
          comments: TextEditingController(),
        ),
        CheckListItem(
          question:
              'Potable water (meeting standards of IS:10500) is used as product ingredient or in contact with food or food contact surface. Tested for quality semi-annually. Check for records.',
          isMandatory: true,
          comments: TextEditingController(),
        ),
      ],
    ),
    CheckListModel(
      title: 'III.Maintenance & sanitation',
      checkListItem: [
        CheckListItem(
          question:
              'The design of food premises provides adequate working space; permit maintenance & cleaning to prevent the entry of dirt, dust & pests.',
          comments: TextEditingController(),
        ),
        CheckListItem(
          question: 'The internal structure & fittings are made of non-toxic and impermeable material.',
          comments: TextEditingController(),
        ),
        CheckListItem(
          question:
              'Potable water (meeting standards of IS:10500) is used as product ingredient or in contact with food or food contact surface. Tested for quality semi-annually. Check for records.',
          isMandatory: true,
          comments: TextEditingController(),
        ),
      ],
    ),
    CheckListModel(
      title: 'IV.Personal Hygiene',
      checkListItem: [
        CheckListItem(
          question:
              'The design of food premises provides adequate working space; permit maintenance & cleaning to prevent the entry of dirt, dust & pests.',
          comments: TextEditingController(),
        ),
        CheckListItem(
          question: 'The internal structure & fittings are made of non-toxic and impermeable material.',
          comments: TextEditingController(),
        ),
        CheckListItem(
          question:
              'Potable water (meeting standards of IS:10500) is used as product ingredient or in contact with food or food contact surface. Tested for quality semi-annually. Check for records.',
          isMandatory: true,
          comments: TextEditingController(),
        ),
      ],
    ),
    CheckListModel(
      title: 'V.Training & Records Keeping',
      checkListItem: [
        CheckListItem(
          question:
              'The design of food premises provides adequate working space; permit maintenance & cleaning to prevent the entry of dirt, dust & pests.',
          comments: TextEditingController(),
        ),
        CheckListItem(
          question: 'The internal structure & fittings are made of non-toxic and impermeable material.',
          comments: TextEditingController(),
        ),
        CheckListItem(
          question:
              'Potable water (meeting standards of IS:10500) is used as product ingredient or in contact with food or food contact surface. Tested for quality semi-annually. Check for records.',
          isMandatory: true,
          comments: TextEditingController(),
        ),
      ],
    ),
  ];

  final TextEditingController majorNonconformityController = TextEditingController();
  final TextEditingController minorNonconformityController = TextEditingController();
  final TextEditingController commentController = TextEditingController();

  File? auditorAgreementFile;
  File? confidentialityFile;
  File? otherFile;

  ChecklistPageHelper(this._pageState);

  Future<void> selectFile(CheckListItem item) async {
    item.file = await FilePickerHelper.instance.pickFiles(_pageState.context, fileSizeLimit: 2);
    _pageState.controller.update();
  }

  void deleteFile(CheckListItem questionModel) {
    questionModel.file = null;
    _pageState.controller.update();
  }

  void scoringOnChanged(String value, CheckListItem questionModel) {
    questionModel.scoring = value;
    _pageState.controller.update();
  }

  void submitValidation() {
    List<CheckListItem> itemList = [];
    for (var element in checklist) {
      for (var item in element.checkListItem) {
        if (item.isMandatory) {
          itemList.add(item);
        }
      }
    }
    if (checklist.every(
      (element) => element.checkListItem.every(
        (element) => element.scoring == AppStringConstants.na,
      ),
    )) {
      AppStringConstants.scoreErrorOfQuestion.tr.showErrorToast();
    } else if (itemList.every(
      (element) => element.scoring == AppStringConstants.na,
    )) {
      AppStringConstants.scoreErrorOfMandatoryQuestion.tr.showErrorToast();
    }
  }

  Future<void> pickAgreementFile() async {
    File? file = await FilePickerHelper.instance.pickFiles(_pageState.context, fileSizeLimit: 2);
    if (file != null) {
      auditorAgreementFile = file;
    }
    _pageState.controller.update();
  }

  void deleteAgreementFile() {
    auditorAgreementFile = null;
    _pageState.controller.update();
  }

  Future<void> pickConfidentialityFile() async {
    File? file = await FilePickerHelper.instance.pickFiles(_pageState.context, fileSizeLimit: 2);
    if (file != null) {
      confidentialityFile = file;
    }
    _pageState.controller.update();
  }

  void deleteConfidentialityFile() {
    confidentialityFile = null;
    _pageState.controller.update();
  }

  Future<void> pickOtherFile() async {
    File? file = await FilePickerHelper.instance.pickFiles(_pageState.context, fileSizeLimit: 2);
    if (file != null) {
      otherFile = file;
    }
    _pageState.controller.update();
  }

  void deleteOtherFile() {
    otherFile = null;
    _pageState.controller.update();
  }
}
