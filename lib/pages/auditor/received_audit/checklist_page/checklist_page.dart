import 'package:ams/app_imports.dart';

class ChecklistPage extends StatefulWidget {
  const ChecklistPage({super.key});

  @override
  State<ChecklistPage> createState() => ChecklistPageState();
}

class ChecklistPageState extends State<ChecklistPage> {
  ChecklistPageHelper? _helper;
  late AuditorReceivedAuditController controller;

  @override
  Widget build(BuildContext context) {
    _helper = _helper ?? ChecklistPageHelper(this);
    return GetBuilder<AuditorReceivedAuditController>(
      init: AuditorReceivedAuditController(),
      builder: (AuditorReceivedAuditController auditorReceivedAuditController) {
        controller = auditorReceivedAuditController;
        return AppScaffold(
          appBar: CommonAppBar(title: 'Slaughter House'),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return Column(
      children: [
        Expanded(
          child: ListView(
            padding: const EdgeInsets.all(12),
            children: [
              _kobView(),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text.rich(
                      TextSpan(
                        text: '${AppStringConstants.note.tr} : ',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: AppColorConstants.colorRedAccent,
                        ),
                        children: [
                          TextSpan(
                            text: AppStringConstants.uploadFileNote.tr,
                            style: const TextStyle(
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text.rich(
                      TextSpan(
                        text: '${AppStringConstants.note.tr} : ',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: AppColorConstants.colorRedAccent,
                        ),
                        children: [
                          TextSpan(
                            text: AppStringConstants.requiredQuestionNote.tr,
                            style: const TextStyle(
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              _checkListView(),
              const SizedBox(height: 20),
              _otherSubmissionView(),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(12).copyWith(top: 8),
          child: AppButton(onTap: () => _helper!.submitValidation(), title: AppStringConstants.submit.tr,backColor: AppColorConstants.colorTeal,),
        ),
      ],
    );
  }

  Widget _kobView() {
    return Container(
      decoration: BoxDecoration(
        color: AppColorConstants.colorWhite,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppColorConstants.colorTeal,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColorConstants.colorTeal,
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(8), topRight: Radius.circular(8)),
              border: Border.all(
                color: AppColorConstants.colorTeal,
              ),
            ),
            child: const AppText(
              'Slaughter House',
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: AppColorConstants.colorWhite,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text.rich(
                  TextSpan(
                    text: '${AppStringConstants.licenseNo.tr} : ',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                    children: const [
                      TextSpan(
                        text: '13023999000032',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                const AppText('AJAY BHARDWAJ'),
                const AppText('2255/4-7, 2ND FLOOR, MARI WALA TOWN,\nMANI MAJRAChandigarhChandigarh Chandigarh\nChandigarh'),
                const AppText(
                  'BIOGLAZE BIOTECH PRIVATE LIMITED\nRelabeller - Food or Health Supplements and\nNutraceuticals etc.',
                  fontWeight: FontWeight.w400,
                ),
                Row(
                  children: [
                    Expanded(
                      child: Text.rich(
                        TextSpan(
                          text: '${AppStringConstants.auditStartDate.tr} : ',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                          children: [
                            TextSpan(
                              text: DateHelper.getDDMMYYYYFormatDate(DateTime.now()),
                              style: const TextStyle(
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text.rich(
                        TextSpan(
                          text: '${AppStringConstants.auditEndDate.tr} : ',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                          children: [
                            TextSpan(
                              text: DateHelper.getDDMMYYYYFormatDate(DateTime.now()),
                              style: const TextStyle(
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _checkListView() {
    return Column(
      children: _helper!.checklist
          .map(
            (checkList) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: ExpansionTile(
                initiallyExpanded: checkList.isGroupOpen,
                onExpansionChanged: (value) {
                  checkList.isGroupOpen = value;
                  controller.update();
                },
                trailing: AppImageAsset(
                  image: (checkList.isGroupOpen) ? AppAssetsConstants.icCloseDropdown : AppAssetsConstants.icOpenDropdown,
                  height: 8,
                  width: 8,
                  color: AppColorConstants.colorWhite,
                ),
                title: Container(
                  color: AppColorConstants.colorTeal,
                  child: AppText(
                    checkList.title ?? '',
                    fontWeight: FontWeight.w600,
                    color: AppColorConstants.colorWhite,
                  ),
                ),
                collapsedBackgroundColor: AppColorConstants.colorTeal,
                backgroundColor: AppColorConstants.colorTeal,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                  side: const BorderSide(color: AppColorConstants.colorTeal),
                ),
                collapsedShape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                  side: const BorderSide(color: AppColorConstants.colorTeal),
                ),
                children: [
                  Container(
                    decoration: const BoxDecoration(
                      color: AppColorConstants.colorWhite,
                    ),
                    child: ListView.separated(
                      itemCount: checkList.checkListItem.length,
                      shrinkWrap: true,
                      separatorBuilder: (context, index) => const Divider(),
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        return _checkCardView(questionModel: checkList.checkListItem[index], index: index);
                      },
                    ),
                  ),
                ],
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _checkCardView({required CheckListItem questionModel, required int index}) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Table(
            border: TableBorder.all(
              color: AppColorConstants.colorMediumGrey,
              borderRadius: BorderRadius.circular(4),
            ),
            columnWidths: const {
              0: FlexColumnWidth(2),
              1: FlexColumnWidth(3),
            },
            children: [
              _buildTableRow('No', '${index + 1}${(questionModel.isMandatory) ? "*" : ''}'),
              _buildTableRow(
                AppStringConstants.question.tr,
                '${questionModel.question}',
                isMandatory: questionModel.isMandatory,
              ),
              _buildScoringRow(questionModel),
              _buildFileUploadRow(questionModel),
              _buildTextFieldRow(questionModel),
            ],
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  TableRow _buildTableRow(String fieldName, String? value, {bool isMandatory = false}) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.fill,
          child: Container(
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(8.0),
            child: AppText(
              fieldName,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        TableCell(
          child: Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            child: AppText(
              '$value',
              color: isMandatory ? AppColorConstants.colorRedAccent : AppColorConstants.colorBlack,
            ),
          ),
        ),
      ],
    );
  }

  TableRow _buildScoringRow(CheckListItem questionModel) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.fill,
          child: Container(
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(8.0),
            child: const AppText(
              'Scoring',
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        TableCell(
          child: Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            child: AppDropdownButton(
              buttonHeight: 30,
              buttonWidth: 80,
              hint: AppStringConstants.na,
              value: questionModel.scoring,
              onChanged: (value) => _helper!.scoringOnChanged(value as String, questionModel),
              dropdownItems:
                  (questionModel.isMandatory) ? [AppStringConstants.na, '0', '4'] : [AppStringConstants.na, '0', '1', '2'],
            ),
          ),
        ),
      ],
    );
  }

  TableRow _buildFileUploadRow(CheckListItem questionModel) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.fill,
          child: Container(
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(8.0),
            child: AppText(
              AppStringConstants.file.tr,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        TableCell(
          child: Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppButton(
                  onTap: () => _helper!.selectFile(questionModel),
                  height: 28,
                  width: 100,
                  textSize: 12,
                  titleColor: AppColorConstants.colorAppPrimary,
                  title: AppStringConstants.chooseFile.tr,
                  borderRadius: BorderRadius.circular(5),
                  border: Border.all(color: AppColorConstants.colorAppPrimary),
                  backColor: AppColorConstants.colorLightPurple,
                ),
                if (questionModel.file?.path.isNotEmpty ?? false)
                  InkWell(
                    onTap: () => _helper!.deleteFile(questionModel),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 5),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: AppText(
                              questionModel.file?.path.split('/').last ?? '',
                              fontSize: 12,
                              color: AppColorConstants.colorRedAccent,
                            ),
                          ),
                          const Icon(
                            Icons.cancel,
                            size: 20,
                            color: AppColorConstants.colorRedAccent,
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  TableRow _buildTextFieldRow(CheckListItem questionModel) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.fill,
          child: Container(
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorLightPurple,
            padding: const EdgeInsets.all(8.0),
            child: AppText(
              AppStringConstants.comments.tr,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        TableCell(
          child: Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: AppTextFormField(
                controller: questionModel.comments,
                hintText: '',
                textFieldHeight: 30,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _otherSubmissionView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        commonTextField(
          controller: _helper!.majorNonconformityController,
          labelText: AppStringConstants.majorNonConformity.tr,
        ),
        commonTextField(
          controller: _helper!.minorNonconformityController,
          labelText: AppStringConstants.minorNonConformity.tr,
        ),
        commonTextField(controller: _helper!.commentController, labelText: AppStringConstants.comments.tr),
        const SizedBox(height: 5),
        fileChooseView(
          file: _helper!.auditorAgreementFile,
          labelText: AppStringConstants.auditorAgreement.tr,
          cancelOnTap: () => _helper!.deleteAgreementFile(),
          fileOnTap: () => _helper!.pickAgreementFile(),
        ),
        fileChooseView(
          file: _helper!.confidentialityFile,
          labelText: AppStringConstants.confidentiality.tr,
          cancelOnTap: () => _helper!.deleteConfidentialityFile(),
          fileOnTap: () => _helper!.pickConfidentialityFile(),
        ),
        fileChooseView(
          file: _helper!.otherFile,
          labelText: AppStringConstants.othersIfAny.tr,
          cancelOnTap: () => _helper!.deleteOtherFile(),
          fileOnTap: () => _helper!.pickOtherFile(),
        ),
      ],
    );
  }

  Widget commonTextField({required TextEditingController controller, required String labelText}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(labelText),
        AppTextFormField(
          textFieldHeight: 80,
          controller: controller,
          hintText: '',
          maxLines: 5,
        ),
        const SizedBox(height: 5),
      ],
    );
  }

  Widget fileChooseView({
    required File? file,
    required String labelText,
    required GestureTapCallback fileOnTap,
    required GestureTapCallback cancelOnTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(labelText),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorGrey),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Row(
            children: [
              AppButton(
                onTap: fileOnTap,
                width: 100,
                height: 30,
                textSize: 12,
                titleColor: AppColorConstants.colorBlack,
                title: AppStringConstants.chooseFile.tr,
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: AppColorConstants.colorGrey),
                backColor: AppColorConstants.colorLightGrey,
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 5),
                  child: Row(
                    children: [
                      Expanded(
                        child: AppText(
                          (file?.path.isNotEmpty ?? false) ? file?.path.split('/').last ?? '' : AppStringConstants.noFileChoosen.tr,
                          fontSize: 12,
                          color: (file?.path.isNotEmpty ?? false) ? AppColorConstants.colorRedAccent : AppColorConstants.colorGrey,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      if (file?.path.isNotEmpty ?? false)
                        InkWell(
                          onTap: cancelOnTap,
                          child: const Icon(
                            Icons.cancel,
                            size: 20,
                            color: AppColorConstants.colorRedAccent,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }
}
