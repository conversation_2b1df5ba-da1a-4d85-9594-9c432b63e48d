import 'package:ams/app_imports.dart';

class NoInterNetConnectionPage extends StatefulWidget {
  const NoInterNetConnectionPage({super.key});

  @override
  State<NoInterNetConnectionPage> createState() => _NoInterNetConnectionPageState();
}

class _NoInterNetConnectionPageState extends State<NoInterNetConnectionPage> {
  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            height: 309,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  AppAssetsConstants.imgNoInternet,
                ),
              ),
            ),
          ),
          const AppText(
            AppStringConstants.oops,
            fontWeight: FontWeight.w600,
            fontSize: 36,
          ),
          const SizedBox(height: 10),
           const AppText(
            textAlign: TextAlign.center,
            AppStringConstants.internetConnectionNotFoundCheckTheConnection,
            fontWeight: FontWeight.w300,
            fontSize: 24,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 12).copyWith(top: 50),
            child: AppButton(
              title: AppStringConstants.tryAgain.tr,
              onTap: () async {
                bool isConnected = await ConnectivityHelper.instance.isConnectNetworkWithMessage();
                if (isConnected) {
                  int profileId=await getPrefIntValue(AppPrefConstants.profileId)??0;
                  if(profileId==1){
                    gotoSuperAdminDashboardPage();
                  }else if(profileId==2){
                    gotoAgencyDashboardPage();
                  }else if(profileId==3){
                    gotoAuditReportPage();
                  }else{
                   gotoBack();
                  }
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
