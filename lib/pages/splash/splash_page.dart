import 'package:ams/app_imports.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    Future.delayed(
      const Duration(seconds: 5),
      () => gotoLoginPage(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      isIconDark: false,
      statusBarColor: AppColorConstants.colorAppPrimary,
      body: Stack(
        children: [
          Container(
            color: AppColorConstants.colorAppPrimary,
            width: double.infinity,
            height: double.infinity,
          ),
          AppImageAsset(
            image: AppAssetsConstants.splashBgImage,
            fit: BoxFit.cover,
            width: double.infinity,
          ),
          Center(
            child: Padding(
              padding: const EdgeInsets.all(55),
              child: AppImageAsset(
                image: AppAssetsConstants.splashLogo,
                width: double.infinity,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
