import 'package:ams/app_imports.dart';

class InspectionReportPage extends StatefulWidget {
  const InspectionReportPage({super.key});

  @override
  State<InspectionReportPage> createState() => InspectionReportPageState();
}

class InspectionReportPageState extends State<InspectionReportPage> {
  InspectionReportPageHelper? _pageHelper;
  late InspectionReportController controller;

  @override
  Widget build(BuildContext context) {
    _pageHelper = _pageHelper ?? InspectionReportPageHelper(this);
    return GetBuilder<InspectionReportController>(
      init: InspectionReportController(),
      builder: (InspectionReportController reportController) {
        controller = reportController;
        return AppScaffold(
          appBar: CommonAppBar(title: AppStringConstants.viewDetail.tr),
          body: _bodyView(),
        );
      },
    );
  }

  Widget _bodyView() {
    return ListView(
      padding: const EdgeInsets.all(12),
      children: [
        _thirdPartyAuditView(),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Text.rich(
            TextSpan(
              text: '${AppStringConstants.note.tr} : ',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColorConstants.colorRedAccent,
                fontSize: 13,
              ),
              children: [
                TextSpan(
                  text: AppStringConstants.requiredQuestionNote.tr,
                  style: const TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ),
        _inspectionReportView(),
        const SizedBox(height: 12,),
        pointsAndRatingView(),
      ],
    );
  }

  Widget _thirdPartyAuditView() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppColorConstants.colorVividOrange),
      ),

      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColorConstants.colorVividOrange,
              border: Border.all(color: AppColorConstants.colorVividOrange),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(3),
                topLeft: Radius.circular(3),
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 13),
            child: AppText(
              AppStringConstants.fssaiThirdPartyAudits.tr,
              color: AppColorConstants.colorWhite,
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                  alignment: Alignment.centerLeft,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(5), topRight: Radius.circular(5)),
                    border: Border(
                      left: BorderSide(color: AppColorConstants.colorLightGrey300),
                      right: BorderSide(color: AppColorConstants.colorLightGrey300),
                      top: BorderSide(color: AppColorConstants.colorLightGrey300),
                    ),
                    color: AppColorConstants.colorPeach,
                  ),
                  child: const AppText('Test Agency Name', fontWeight: FontWeight.w600, fontSize: 16),
                ),
                Table(
                  border: TableBorder.all(
                    color: AppColorConstants.colorLightGrey300,
                    borderRadius: const BorderRadius.only(bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5)),
                  ),
                  children: [
                    for (var entry in _pageHelper!.thirdPartyAuditModel.toJson().entries)
                      TableRow(
                        children: [
                          TableCell(
                            verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                              alignment: Alignment.centerLeft,
                              color: AppColorConstants.colorPeach,
                              child: AppText(
                                entry.key.tr,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                          TableCell(
                            verticalAlignment: TableCellVerticalAlignment.middle,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                              child: (entry.key == AppStringConstants.filesUploaded)
                                  ? uploadedFileView()
                                  : AppText(
                                      (entry.key == AppStringConstants.auditStartDate ||
                                              entry.key == AppStringConstants.auditEndDate)
                                          ? DateHelper.getDDMMYYYYFormatDate(entry.value)
                                          : entry.value,
                                      fontSize: 12,
                                      color: AppColorConstants.colorBlack,
                                    ),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget uploadedFileView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppButton(
          onTap: (_pageHelper!.thirdPartyAuditModel.auditorAgreementFile?.isNotEmpty ?? false)
              ? () => launchDocumentUrl(_pageHelper!.thirdPartyAuditModel.auditorAgreementFile!)
              : () {},
          title: AppStringConstants.auditorAgreementFile.tr,
          imageIcon: (_pageHelper!.thirdPartyAuditModel.auditorAgreementFile?.isNotEmpty ?? false)
              ? AppAssetsConstants.icDownload
              : AppAssetsConstants.icCancel,
          iconSize: 10,
          iconColor: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.circular(5),
          backColor: AppColorConstants.colorYellow,
          textSize: 10,
          height: 25,
          border: Border.all(color: AppColorConstants.colorYellow),
        ),
        const SizedBox(height: 5),
        AppButton(
          onTap: (_pageHelper!.thirdPartyAuditModel.confidentialityFile?.isNotEmpty ?? false)
              ? () => launchDocumentUrl(_pageHelper!.thirdPartyAuditModel.confidentialityFile!)
              : () {},
          title: AppStringConstants.confidentialityFile.tr,
          imageIcon: (_pageHelper!.thirdPartyAuditModel.confidentialityFile?.isNotEmpty ?? false)
              ? AppAssetsConstants.icDownload
              : AppAssetsConstants.icCancel,
          iconColor: AppColorConstants.colorWhite,
          iconSize: 10,
          borderRadius: BorderRadius.circular(5),
          backColor: AppColorConstants.colorYellow,
          textSize: 10,
          height: 25,
          border: Border.all(color: AppColorConstants.colorYellow),
        ),
        const SizedBox(height: 5),
        AppButton(
          onTap: (_pageHelper!.thirdPartyAuditModel.otherFile?.isNotEmpty ?? false)
              ? () => launchDocumentUrl(_pageHelper!.thirdPartyAuditModel.otherFile!)
              : () {},
          title: AppStringConstants.other.tr,
          imageIcon: (_pageHelper!.thirdPartyAuditModel.otherFile?.isNotEmpty ?? false)
              ? AppAssetsConstants.icDownload
              : AppAssetsConstants.icCancel,
          iconSize: 10,
          iconColor: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.circular(5),
          backColor: AppColorConstants.colorYellow,
          textSize: 10,
          height: 25,
          width: 80,
          border: Border.all(color: AppColorConstants.colorYellow),
        ),
      ],
    );
  }

  Widget _inspectionReportView() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppColorConstants.colorVividOrange),
      ),
      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColorConstants.colorVividOrange,
              border: Border.all(color: AppColorConstants.colorVividOrange),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(3),
                topLeft: Radius.circular(3),
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 13),
            child: const AppText(
              '${AppStringConstants.inspectionChecklistFor} GENERAL MANUFACTURING',
              color: AppColorConstants.colorWhite,
              fontSize: 18,
              textAlign: TextAlign.center,
              fontWeight: FontWeight.w700,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                for (var element in _pageHelper!.inspectionReportList)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    child: Table(
                      border: TableBorder.all(
                        color: AppColorConstants.colorLightGrey300,
                      ),
                      children: [
                        for (var entry in element.toJson().entries)
                          TableRow(
                            children: [
                              TableCell(
                                verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                  alignment: Alignment.centerLeft,
                                  color: AppColorConstants.colorPeach,
                                  child: AppText(
                                    entry.key.tr,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              TableCell(
                                verticalAlignment: TableCellVerticalAlignment.middle,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                  child: (entry.key == AppStringConstants.fileUploaded)
                                      ? (entry.value != null && entry.value.toString().isNotEmpty)
                                          ? Align(
                                              alignment: Alignment.centerLeft,
                                              child: InkWell(
                                                onTap: () => launchDocumentUrl(entry.value.toString()),
                                                child: Padding(
                                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                                  child: AppImageAsset(
                                                    image: AppAssetsConstants.icDownload,
                                                    height: 20,
                                                    width: 20,
                                                  ),
                                                ),
                                              ),
                                            )
                                          : const SizedBox()
                                      : AppText(
                                          '${(entry.key == AppStringConstants.comments && entry.value.toString().isEmpty) ? '-' : (entry.key == AppStringConstants.srNo && element.isMandatory) ? '${entry.value}*' : entry.value}',
                                          fontSize: 12,
                                          color: (entry.key == AppStringConstants.question && element.isMandatory)
                                              ? AppColorConstants.colorRedAccent
                                              : AppColorConstants.colorBlack,
                                        ),
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget pointsAndRatingView() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppColorConstants.colorVividOrange),
      ),
      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColorConstants.colorVividOrange,
              border: Border.all(color: AppColorConstants.colorVividOrange),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(3),
                topLeft: Radius.circular(3),
              ),
            ),

            padding: const EdgeInsets.symmetric(vertical: 13),
            child: AppText(
              AppStringConstants.pointsAndCompliance.tr,
              color: AppColorConstants.colorWhite,
              fontSize: 18,
              textAlign: TextAlign.center,
              fontWeight: FontWeight.w700,
            ),
      ),
      Padding(
        padding: const EdgeInsets.all(12.0),
        child: Table(
          border: TableBorder.all(
            color: AppColorConstants.colorLightGrey300,
          ),
                children: [
                  pointCommonRow(AppStringConstants.totalPoints.tr, '1'),
                  pointCommonRow(AppStringConstants.pointsScored.tr, '1'),
                  pointCommonRow(AppStringConstants.rating.tr, '1'),
                  pointCommonRow(AppStringConstants.compliance.tr, 'Needs Improvement'),
                ],
              ),
      ),

        ],
      ),
    );
  }

  TableRow pointCommonRow(String key, String value) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.intrinsicHeight,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            alignment: Alignment.centerLeft,
            color: AppColorConstants.colorPeach,
            child: AppText(
              key.tr,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: AppText(
              value.toString(),
              fontSize: 12,
              fontWeight: FontWeight.w700,
              color: AppColorConstants.colorBlack,
            ),
          ),
        ),
      ],
    );
  }
}
